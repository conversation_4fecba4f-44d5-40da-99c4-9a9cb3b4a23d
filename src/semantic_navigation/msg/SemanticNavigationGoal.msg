# 语义导航目标消息
Header header

# 目标位置
geometry_msgs/PoseStamped target_pose

# 导航模式
string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE

# 语义约束
string[] avoid_classes    # 需要避开的语义类别
string[] prefer_classes   # 优先通过的语义类别

# 安全参数
float32 safety_distance   # 安全距离 (米)
float32 fire_avoidance_distance  # 火焰避让距离 (米)

# 优先级设置
uint8 priority           # 导航优先级 (0-10, 10最高)
bool emergency_mode      # 是否为应急模式

# 超时设置
duration timeout         # 导航超时时间
