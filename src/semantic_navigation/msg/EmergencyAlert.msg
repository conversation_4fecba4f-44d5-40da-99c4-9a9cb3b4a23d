# 应急警报消息
Header header

# 警报类型
string alert_type           # FIRE, SMOKE, OBSTACLE, SYSTEM_FAILURE

# 警报等级
string severity             # LOW, MEDIUM, HIGH, CRITICAL

# 位置信息
geometry_msgs/Point location
float32 affected_radius     # 影响半径 (米)

# 描述信息
string description          # 警报描述
string recommended_action   # 建议行动

# 时间信息
time detection_time         # 检测时间
duration estimated_duration # 预计持续时间

# 相关数据
float32 confidence          # 置信度
string[] related_objects    # 相关物体
