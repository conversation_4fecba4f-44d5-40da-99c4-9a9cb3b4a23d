# 火灾安全区域配置文件

# 安全区域定义
safety_zones:
  # 主要安全区域
  - name: "main_exit"
    type: "emergency_exit"
    position: [0.0, 0.0, 0.0]        # 相对于地图原点的位置
    radius: 2.0                      # 安全半径 (米)
    priority: 10                     # 优先级 (1-10, 10最高)
    description: "主要紧急出口"
    
  - name: "secondary_exit"
    type: "emergency_exit"
    position: [10.0, 10.0, 0.0]
    radius: 1.5
    priority: 8
    description: "次要紧急出口"
    
  # 安全避难区域
  - name: "safe_room_1"
    type: "safe_room"
    position: [5.0, 5.0, 0.0]
    radius: 3.0
    priority: 7
    description: "安全避难室1"
    
  - name: "safe_room_2"
    type: "safe_room"
    position: [-5.0, 5.0, 0.0]
    radius: 2.5
    priority: 6
    description: "安全避难室2"

# 危险区域定义
danger_zones:
  # 高风险区域
  - name: "kitchen_area"
    type: "high_risk"
    position: [3.0, -2.0, 0.0]
    radius: 2.0
    risk_level: "HIGH"
    description: "厨房区域 - 火灾高风险"
    
  - name: "electrical_room"
    type: "high_risk"
    position: [-3.0, -2.0, 0.0]
    radius: 1.5
    risk_level: "HIGH"
    description: "电气室 - 火灾高风险"
    
  # 中等风险区域
  - name: "storage_area"
    type: "medium_risk"
    position: [0.0, -5.0, 0.0]
    radius: 2.5
    risk_level: "MEDIUM"
    description: "储藏区域 - 火灾中等风险"

# 应急路径配置
emergency_paths:
  # 主要疏散路径
  - name: "main_evacuation_path"
    waypoints:
      - [0.0, 0.0, 0.0]
      - [2.0, 1.0, 0.0]
      - [4.0, 2.0, 0.0]
      - [6.0, 3.0, 0.0]
    width: 1.5                       # 路径宽度 (米)
    priority: 10
    description: "主要疏散通道"
    
  # 备用疏散路径
  - name: "backup_evacuation_path"
    waypoints:
      - [0.0, 0.0, 0.0]
      - [-2.0, 1.0, 0.0]
      - [-4.0, 2.0, 0.0]
      - [-6.0, 3.0, 0.0]
    width: 1.2
    priority: 8
    description: "备用疏散通道"

# 火焰检测配置
fire_detection_zones:
  # 重点监控区域
  - name: "critical_monitoring_zone"
    position: [0.0, 0.0, 0.0]
    radius: 5.0
    detection_sensitivity: 0.7       # 检测敏感度
    alert_threshold: 0.8             # 警报阈值
    description: "重点火焰监控区域"
    
  # 一般监控区域
  - name: "general_monitoring_zone"
    position: [0.0, 0.0, 0.0]
    radius: 15.0
    detection_sensitivity: 0.8
    alert_threshold: 0.9
    description: "一般火焰监控区域"

# 应急响应配置
emergency_response:
  # 自动响应设置
  auto_response:
    enable_auto_evacuation: true     # 启用自动疏散
    auto_alert_authorities: false    # 自动报警 (暂时禁用)
    auto_fire_suppression: false     # 自动灭火 (暂时禁用)
    
  # 响应时间设置
  response_times:
    detection_to_alert: 2.0          # 检测到警报时间 (秒)
    alert_to_evacuation: 5.0         # 警报到疏散时间 (秒)
    max_evacuation_time: 120.0       # 最大疏散时间 (秒)
    
  # 通信设置
  communication:
    enable_voice_alerts: false       # 启用语音警报 (暂时禁用)
    enable_visual_alerts: true       # 启用视觉警报
    alert_repeat_interval: 10.0      # 警报重复间隔 (秒)
