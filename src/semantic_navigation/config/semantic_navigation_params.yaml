# 语义导航参数配置文件

# 安全参数
fire_safety_distance: 2.0          # 火焰安全距离 (米)
emergency_stop_distance: 1.0       # 应急停止距离 (米)
max_navigation_time: 300.0          # 最大导航时间 (秒)
smoke_avoidance_distance: 1.5       # 烟雾避让距离 (米)

# 性能参数
status_publish_rate: 2.0            # 状态发布频率 (Hz)
monitoring_rate: 10.0               # 监控频率 (Hz)
path_deviation_threshold: 1.0       # 路径偏差阈值 (米)
max_speed: 1.0                      # 最大速度 (m/s)
max_angular_speed: 1.0              # 最大角速度 (rad/s)

# 应急参数
fire_detection_threshold: 0.8       # 火焰检测阈值
consecutive_fire_detections: 3      # 连续火焰检测次数
emergency_timeout: 30.0             # 应急超时时间 (秒)
emergency_response_time: 2.0        # 应急响应时间 (秒)

# 路径规划参数
planning_timeout: 10.0              # 规划超时时间 (秒)
replanning_frequency: 1.0           # 重规划频率 (Hz)
goal_tolerance: 0.5                 # 目标容忍度 (米)
angular_tolerance: 0.1              # 角度容忍度 (弧度)

# 语义代价权重
semantic_costs:
  fire: 1000.0                      # 火焰区域代价 (禁入)
  smoke: 100.0                      # 烟雾区域代价
  person: 50.0                      # 人员区域代价
  chair: 10.0                       # 椅子代价 (可推开)
  table: 20.0                       # 桌子代价 (需绕行)
  wall: 1000.0                      # 墙壁代价 (禁入)
  door: 1.0                         # 门口代价 (优先通过)
  corridor: 1.0                     # 走廊代价 (优先通过)
  unknown: 50.0                     # 未知区域代价

# 避障参数
obstacle_avoidance:
  enable_dynamic_avoidance: true    # 启用动态避障
  obstacle_inflation_radius: 0.3    # 障碍物膨胀半径 (米)
  min_obstacle_distance: 0.2        # 最小障碍物距离 (米)
  avoidance_force_gain: 2.0         # 避障力增益
  
# 应急导航参数
emergency_navigation:
  enable_emergency_exits: true      # 启用应急出口
  exit_search_radius: 10.0          # 出口搜索半径 (米)
  emergency_speed_limit: 0.5        # 应急速度限制 (m/s)
  fire_escape_priority: true        # 火灾逃生优先级

# 可视化参数
visualization:
  enable_path_visualization: true   # 启用路径可视化
  enable_safety_zones: true         # 启用安全区域显示
  enable_fire_markers: true         # 启用火焰标记
  marker_lifetime: 5.0              # 标记生存时间 (秒)
