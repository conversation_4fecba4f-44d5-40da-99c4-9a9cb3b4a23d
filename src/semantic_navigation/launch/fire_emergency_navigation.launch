<!-- 火灾应急导航启动文件 -->
<launch>
  <!-- 应急模式参数 -->
  <arg name="emergency_mode" default="true" doc="Enable emergency mode"/>
  <arg name="fire_escape_mode" default="true" doc="Enable fire escape mode"/>
  <arg name="auto_evacuation" default="false" doc="Enable automatic evacuation"/>
  <arg name="safety_distance" default="2.0" doc="Fire safety distance in meters"/>
  <arg name="emergency_speed" default="0.5" doc="Emergency navigation speed"/>

  <!-- 包含基础语义导航 -->
  <include file="$(find semantic_navigation)/launch/semantic_navigation.launch">
    <arg name="navigation_mode" value="emergency"/>
    <arg name="enable_visualization" value="true"/>
    <arg name="enable_safety_monitoring" value="true"/>
  </include>

  <!-- 应急路径规划器 -->
  <node name="emergency_path_planner" 
        pkg="semantic_navigation" 
        type="emergency_path_planner.py" 
        output="screen"
        ns="semantic_navigation">
    
    <!-- 应急参数 -->
    <param name="emergency_mode" value="$(arg emergency_mode)"/>
    <param name="fire_escape_mode" value="$(arg fire_escape_mode)"/>
    <param name="fire_safety_distance" value="$(arg safety_distance)"/>
    <param name="emergency_speed_limit" value="$(arg emergency_speed)"/>
    <param name="enable_auto_evacuation" value="$(arg auto_evacuation)"/>
    
    <!-- 规划参数 -->
    <param name="planning_frequency" value="2.0"/>
    <param name="replanning_threshold" value="1.0"/>
    <param name="exit_search_radius" value="10.0"/>
    <param name="path_safety_margin" value="1.0"/>
    
    <!-- 话题重映射 -->
    <remap from="fire_locations" to="/semantic_perception/fire_locations_3d"/>
    <remap from="semantic_map" to="/semantic_mapping/enhanced_pointcloud"/>
    <remap from="current_pose" to="/pose_center/odom"/>
    <remap from="emergency_path" to="/semantic_navigation/emergency_path"/>
    <remap from="evacuation_command" to="/semantic_navigation/evacuation_command"/>
  </node>

  <!-- 火焰威胁评估器 -->
  <node name="fire_threat_assessor" 
        pkg="semantic_navigation" 
        type="fire_threat_assessor.py" 
        output="screen"
        ns="semantic_navigation">
    
    <!-- 威胁评估参数 -->
    <param name="threat_assessment_rate" value="5.0"/>
    <param name="critical_distance" value="1.0"/>
    <param name="warning_distance" value="3.0"/>
    <param name="spread_prediction_time" value="30.0"/>
    
    <!-- 话题重映射 -->
    <remap from="fire_detections" to="/semantic_perception/enhanced_fire_detections"/>
    <remap from="fire_locations_3d" to="/semantic_perception/fire_locations_3d"/>
    <remap from="fire_spread_risk" to="/semantic_perception/fire_spread_risk"/>
    <remap from="threat_assessment" to="/semantic_navigation/threat_assessment"/>
    <remap from="evacuation_zones" to="/semantic_navigation/evacuation_zones"/>
  </node>

  <!-- 应急通信节点 -->
  <node name="emergency_communication" 
        pkg="semantic_navigation" 
        type="emergency_communication.py" 
        output="screen"
        ns="semantic_navigation">
    
    <!-- 通信参数 -->
    <param name="enable_voice_alerts" value="false"/>
    <param name="enable_visual_alerts" value="true"/>
    <param name="alert_repeat_interval" value="10.0"/>
    <param name="emergency_contact_enabled" value="false"/>
    
    <!-- 话题重映射 -->
    <remap from="emergency_alert" to="/semantic_navigation/emergency_alert"/>
    <remap from="navigation_status" to="/semantic_navigation/status"/>
    <remap from="threat_assessment" to="/semantic_navigation/threat_assessment"/>
  </node>

  <!-- 应急可视化增强 -->
  <node name="emergency_visualization" 
        pkg="semantic_navigation" 
        type="emergency_visualization.py" 
        output="screen"
        ns="semantic_navigation">
    
    <!-- 可视化参数 -->
    <param name="show_fire_zones" value="true"/>
    <param name="show_evacuation_paths" value="true"/>
    <param name="show_safety_zones" value="true"/>
    <param name="show_threat_levels" value="true"/>
    <param name="marker_lifetime" value="5.0"/>
    
    <!-- 话题重映射 -->
    <remap from="fire_locations_3d" to="/semantic_perception/fire_locations_3d"/>
    <remap from="emergency_path" to="/semantic_navigation/emergency_path"/>
    <remap from="evacuation_zones" to="/semantic_navigation/evacuation_zones"/>
    <remap from="emergency_markers" to="/semantic_navigation/emergency_markers"/>
  </node>

  <!-- 系统状态监控 -->
  <node name="emergency_system_monitor" 
        pkg="semantic_navigation" 
        type="system_monitor.py" 
        output="screen"
        ns="semantic_navigation">
    
    <!-- 监控参数 -->
    <param name="monitoring_frequency" value="1.0"/>
    <param name="system_timeout" value="30.0"/>
    <param name="enable_auto_restart" value="true"/>
    
    <!-- 话题重映射 -->
    <remap from="system_status" to="/semantic_navigation/system_status"/>
    <remap from="component_health" to="/semantic_navigation/component_health"/>
  </node>

  <!-- 记录和日志 -->
  <node name="emergency_logger" 
        pkg="rosbag" 
        type="record" 
        name="emergency_data_recorder"
        args="-o /tmp/emergency_navigation_$(env USER)_$(eval arg('emergency_mode'))
              /semantic_navigation/status
              /semantic_navigation/emergency_alert  
              /semantic_perception/enhanced_fire_detections
              /semantic_perception/fire_locations_3d
              /semantic_navigation/emergency_path
              /semantic_navigation/threat_assessment
              /cmd_vel
              /pose_center/odom"
        output="screen"/>

  <!-- 应急模式提示 -->
  <node name="emergency_mode_announcer" 
        pkg="rostopic" 
        type="rostopic" 
        name="emergency_mode_announcer"
        args="pub -r 0.1 /semantic_navigation/mode_announcement std_msgs/String 
              'data: EMERGENCY_NAVIGATION_ACTIVE - Fire Escape Mode Enabled'"
        output="screen"/>

</launch>
