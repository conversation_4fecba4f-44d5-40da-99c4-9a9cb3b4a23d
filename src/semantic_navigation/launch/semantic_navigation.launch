<!-- 语义导航系统启动文件 -->
<launch>
  <!-- 参数配置 -->
  <arg name="navigation_mode" default="normal" doc="Navigation mode: normal, emergency, fire_escape"/>
  <arg name="enable_visualization" default="true" doc="Enable visualization"/>
  <arg name="enable_safety_monitoring" default="true" doc="Enable safety monitoring"/>
  <arg name="config_file" default="$(find semantic_navigation)/config/semantic_navigation_params.yaml"/>
  <arg name="safety_zones_file" default="$(find semantic_navigation)/config/fire_safety_zones.yaml"/>

  <!-- 加载参数 -->
  <rosparam file="$(arg config_file)" command="load" ns="semantic_navigation"/>
  <rosparam file="$(arg safety_zones_file)" command="load" ns="semantic_navigation"/>

  <!-- 语义导航管理器 -->
  <node name="semantic_navigation_manager" 
        pkg="semantic_navigation" 
        type="semantic_navigation_manager.py" 
        output="screen"
        ns="semantic_navigation">
    
    <!-- 参数重映射 -->
    <rosparam file="$(arg config_file)" command="load"/>
    
    <!-- 话题重映射 -->
    <remap from="robot_pose" to="/pose_center/odom"/>
    <remap from="semantic_map" to="/semantic_mapping/enhanced_pointcloud"/>
    <remap from="fire_detections" to="/semantic_perception/enhanced_fire_detections"/>
    <remap from="emergency_alert_input" to="/semantic_perception/emergency_alert"/>
    <remap from="fire_locations_3d" to="/semantic_perception/fire_locations_3d"/>
    
    <!-- 输出话题 -->
    <remap from="navigation_status" to="/semantic_navigation/status"/>
    <remap from="emergency_alert" to="/semantic_navigation/emergency_alert"/>
    <remap from="planned_path_visualization" to="/semantic_navigation/planned_path"/>
    <remap from="safety_zones" to="/semantic_navigation/safety_zones"/>
    <remap from="cmd_vel" to="/cmd_vel"/>
  </node>

  <!-- 增强火焰检测节点 -->
  <node name="enhanced_fire_detection_node" 
        pkg="semantic_perception" 
        type="enhanced_fire_detection_node.py" 
        output="screen"
        ns="semantic_perception">
    
    <!-- 参数配置 -->
    <param name="processing_rate" value="8.0"/>
    <param name="confidence_threshold" value="0.65"/>
    <param name="enable_visualization" value="$(arg enable_visualization)"/>
    <param name="enable_emergency_mode" value="true"/>
    <param name="enable_3d_tracking" value="true"/>
    
    <!-- 应急参数 -->
    <param name="fire_alert_threshold" value="0.85"/>
    <param name="consecutive_detection_threshold" value="2"/>
    <param name="emergency_distance_threshold" value="3.0"/>
    
    <!-- 话题重映射 -->
    <remap from="image_raw" to="/stereo_camera/left/image_rect_color"/>
    <remap from="depth_image" to="/stereo_camera/depth/depth_registered"/>
    <remap from="camera_info" to="/stereo_camera/left/camera_info"/>
    
    <!-- 输出话题 -->
    <remap from="enhanced_fire_detections" to="/semantic_perception/enhanced_fire_detections"/>
    <remap from="emergency_alert" to="/semantic_perception/emergency_alert"/>
    <remap from="fire_locations_3d" to="/semantic_perception/fire_locations_3d"/>
    <remap from="enhanced_fire_visualization" to="/semantic_perception/enhanced_fire_visualization"/>
    <remap from="fire_spread_risk" to="/semantic_perception/fire_spread_risk"/>
  </node>

  <!-- 可视化节点 (可选) -->
  <group if="$(arg enable_visualization)">
    <!-- RViz可视化 -->
    <node name="rviz_semantic_navigation" 
          pkg="rviz" 
          type="rviz" 
          args="-d $(find semantic_navigation)/config/semantic_navigation.rviz"
          output="screen"
          if="$(arg enable_visualization)"/>
  </group>

  <!-- 安全监控节点 (可选) -->
  <group if="$(arg enable_safety_monitoring)">
    <node name="safety_monitor" 
          pkg="semantic_navigation" 
          type="safety_monitor.py" 
          output="screen"
          ns="semantic_navigation">
      
      <!-- 话题重映射 -->
      <remap from="navigation_status" to="/semantic_navigation/status"/>
      <remap from="fire_detections" to="/semantic_perception/enhanced_fire_detections"/>
      <remap from="emergency_alert" to="/semantic_navigation/emergency_alert"/>
    </node>
  </group>

  <!-- 诊断和监控 -->
  <node name="diagnostic_aggregator" 
        pkg="diagnostic_aggregator" 
        type="aggregator_node"
        output="screen">
    <rosparam>
      pub_rate: 1.0
      base_path: ''
      analyzers:
        semantic_navigation:
          type: diagnostic_aggregator/GenericAnalyzer
          path: Semantic Navigation
          contains: ['/semantic_navigation']
        fire_detection:
          type: diagnostic_aggregator/GenericAnalyzer  
          path: Fire Detection
          contains: ['/semantic_perception/fire']
    </rosparam>
  </node>

</launch>
