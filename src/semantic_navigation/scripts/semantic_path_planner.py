#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语义路径规划器
功能：基于语义地图的智能路径规划
支持火焰避让和应急导航
作者：语义SLAM增强项目 - 阶段2扩展
版本：2.1
"""

import rospy
import numpy as np
import heapq
import threading
from collections import defaultdict
import tf2_ros
import tf2_geometry_msgs

from geometry_msgs.msg import PoseStamped, Point, Quaternion
from nav_msgs.msg import Path, OccupancyGrid
from sensor_msgs.msg import PointCloud2
from visualization_msgs.msg import MarkerArray, Marker
from std_msgs.msg import Header, ColorRGBA

from semantic_navigation.msg import SemanticPath, NavigationStatus
from semantic_navigation.srv import GetSafePath, GetSafePathResponse
import sensor_msgs.point_cloud2 as pc2

class SemanticAStarPlanner:
    """语义增强A*路径规划器"""
    
    def __init__(self):
        # 语义代价权重
        self.semantic_costs = {
            'fire': 1000.0,      # 火焰区域禁入
            'smoke': 100.0,      # 烟雾区域高代价
            'person': 50.0,      # 人员区域需避让
            'chair': 10.0,       # 椅子可推开
            'table': 20.0,       # 桌子需绕行
            'wall': 1000.0,      # 墙壁不可通过
            'door': 1.0,         # 门口优先通过
            'corridor': 1.0,     # 走廊优先通过
            'unknown': 50.0,     # 未知区域
            'free': 1.0,         # 自由空间
        }
        
        # 规划参数
        self.grid_resolution = 0.1  # 栅格分辨率 (米)
        self.robot_radius = 0.3     # 机器人半径 (米)
        self.safety_margin = 0.2    # 安全边距 (米)
        
        # 地图数据
        self.semantic_grid = None
        self.occupancy_grid = None
        self.grid_width = 0
        self.grid_height = 0
        self.grid_origin = None
        
        # 火焰位置
        self.fire_locations = []
        self.fire_safety_radius = 2.0  # 火焰安全半径
        
    def set_semantic_costs(self, costs_dict):
        """设置语义代价权重"""
        self.semantic_costs.update(costs_dict)
    
    def update_semantic_map(self, pointcloud_msg):
        """更新语义地图"""
        try:
            # 解析点云数据
            points = []
            for point in pc2.read_points(pointcloud_msg, skip_nans=True):
                x, y, z = point[:3]
                # 假设语义信息在第4个字段
                semantic_label = int(point[3]) if len(point) > 3 else 0
                points.append((x, y, z, semantic_label))
            
            if not points:
                return
            
            # 转换为栅格地图
            self.points_to_grid(points)
            
        except Exception as e:
            rospy.logerr(f"更新语义地图失败: {e}")
    
    def points_to_grid(self, points):
        """将点云转换为栅格地图"""
        if not points:
            return
        
        # 计算边界
        xs = [p[0] for p in points]
        ys = [p[1] for p in points]
        
        min_x, max_x = min(xs), max(xs)
        min_y, max_y = min(ys), max(ys)
        
        # 扩展边界
        margin = 5.0  # 5米边距
        min_x -= margin
        max_x += margin
        min_y -= margin
        max_y += margin
        
        # 计算栅格尺寸
        self.grid_width = int((max_x - min_x) / self.grid_resolution) + 1
        self.grid_height = int((max_y - min_y) / self.grid_resolution) + 1
        self.grid_origin = (min_x, min_y)
        
        # 初始化栅格
        self.semantic_grid = np.full((self.grid_height, self.grid_width), 'unknown', dtype=object)
        self.occupancy_grid = np.zeros((self.grid_height, self.grid_width), dtype=np.float32)
        
        # 填充栅格
        for x, y, z, semantic_label in points:
            grid_x = int((x - min_x) / self.grid_resolution)
            grid_y = int((y - min_y) / self.grid_resolution)
            
            if 0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height:
                # 简化的语义标签映射
                if semantic_label == 0:
                    label = 'free'
                elif semantic_label == 1:
                    label = 'wall'
                elif semantic_label == 2:
                    label = 'chair'
                elif semantic_label == 3:
                    label = 'table'
                elif semantic_label == 4:
                    label = 'person'
                elif semantic_label == 5:
                    label = 'fire'
                elif semantic_label == 6:
                    label = 'smoke'
                else:
                    label = 'unknown'
                
                self.semantic_grid[grid_y, grid_x] = label
                
                # 设置占用概率
                if label in ['wall', 'fire']:
                    self.occupancy_grid[grid_y, grid_x] = 1.0  # 完全占用
                elif label in ['table', 'chair']:
                    self.occupancy_grid[grid_y, grid_x] = 0.8  # 高占用
                elif label in ['smoke', 'person']:
                    self.occupancy_grid[grid_y, grid_x] = 0.6  # 中等占用
                else:
                    self.occupancy_grid[grid_y, grid_x] = 0.0  # 自由空间
    
    def update_fire_locations(self, fire_locations):
        """更新火焰位置"""
        self.fire_locations = fire_locations
    
    def world_to_grid(self, world_x, world_y):
        """世界坐标转栅格坐标"""
        if self.grid_origin is None:
            return None, None
        
        grid_x = int((world_x - self.grid_origin[0]) / self.grid_resolution)
        grid_y = int((world_y - self.grid_origin[1]) / self.grid_resolution)
        
        return grid_x, grid_y
    
    def grid_to_world(self, grid_x, grid_y):
        """栅格坐标转世界坐标"""
        if self.grid_origin is None:
            return None, None
        
        world_x = grid_x * self.grid_resolution + self.grid_origin[0]
        world_y = grid_y * self.grid_resolution + self.grid_origin[1]
        
        return world_x, world_y
    
    def is_valid_position(self, grid_x, grid_y):
        """检查位置是否有效"""
        if (grid_x < 0 or grid_x >= self.grid_width or 
            grid_y < 0 or grid_y >= self.grid_height):
            return False
        
        # 检查占用状态
        if self.occupancy_grid[grid_y, grid_x] > 0.5:
            return False
        
        # 检查火焰安全距离
        world_x, world_y = self.grid_to_world(grid_x, grid_y)
        for fire_loc in self.fire_locations:
            fire_x, fire_y = fire_loc['position'][:2]
            distance = np.sqrt((world_x - fire_x)**2 + (world_y - fire_y)**2)
            if distance < self.fire_safety_radius:
                return False
        
        return True
    
    def get_semantic_cost(self, grid_x, grid_y):
        """获取位置的语义代价"""
        if (grid_x < 0 or grid_x >= self.grid_width or 
            grid_y < 0 or grid_y >= self.grid_height):
            return float('inf')
        
        semantic_label = self.semantic_grid[grid_y, grid_x]
        base_cost = self.semantic_costs.get(semantic_label, self.semantic_costs['unknown'])
        
        # 火焰区域特殊处理
        if semantic_label == 'fire':
            return float('inf')
        
        # 距离火焰的代价
        world_x, world_y = self.grid_to_world(grid_x, grid_y)
        fire_cost = 0.0
        for fire_loc in self.fire_locations:
            fire_x, fire_y = fire_loc['position'][:2]
            distance = np.sqrt((world_x - fire_x)**2 + (world_y - fire_y)**2)
            if distance < self.fire_safety_radius:
                # 距离越近代价越高
                fire_cost += 100.0 * (self.fire_safety_radius - distance) / self.fire_safety_radius
        
        return base_cost + fire_cost
    
    def heuristic(self, pos1, pos2):
        """启发式函数（曼哈顿距离）"""
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])
    
    def get_neighbors(self, pos):
        """获取邻居节点"""
        x, y = pos
        neighbors = []
        
        # 8方向移动
        directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), 
                     (0, 1), (1, -1), (1, 0), (1, 1)]
        
        for dx, dy in directions:
            new_x, new_y = x + dx, y + dy
            if self.is_valid_position(new_x, new_y):
                # 对角线移动的代价更高
                move_cost = 1.414 if abs(dx) + abs(dy) == 2 else 1.0
                neighbors.append(((new_x, new_y), move_cost))
        
        return neighbors
    
    def plan_path(self, start_pose, goal_pose, emergency_mode=False):
        """A*路径规划"""
        if self.semantic_grid is None:
            rospy.logwarn("语义地图未初始化")
            return None
        
        # 转换为栅格坐标
        start_x, start_y = self.world_to_grid(start_pose.pose.position.x, 
                                             start_pose.pose.position.y)
        goal_x, goal_y = self.world_to_grid(goal_pose.pose.position.x, 
                                           goal_pose.pose.position.y)
        
        if start_x is None or goal_x is None:
            rospy.logerr("起点或终点超出地图范围")
            return None
        
        if not self.is_valid_position(start_x, start_y):
            rospy.logerr("起点位置无效")
            return None
        
        if not self.is_valid_position(goal_x, goal_y):
            rospy.logerr("终点位置无效")
            return None
        
        # A*算法
        start = (start_x, start_y)
        goal = (goal_x, goal_y)
        
        open_set = [(0, start)]
        came_from = {}
        g_score = defaultdict(lambda: float('inf'))
        g_score[start] = 0
        f_score = defaultdict(lambda: float('inf'))
        f_score[start] = self.heuristic(start, goal)
        
        while open_set:
            current = heapq.heappop(open_set)[1]
            
            if current == goal:
                # 重构路径
                path = []
                while current in came_from:
                    path.append(current)
                    current = came_from[current]
                path.append(start)
                path.reverse()
                
                return self.grid_path_to_world_path(path, start_pose.header)
            
            for neighbor, move_cost in self.get_neighbors(current):
                # 计算语义代价
                semantic_cost = self.get_semantic_cost(neighbor[0], neighbor[1])
                if semantic_cost == float('inf'):
                    continue
                
                # 应急模式下调整代价
                if emergency_mode:
                    semantic_cost *= 0.5  # 降低语义代价，优先速度
                
                tentative_g_score = g_score[current] + move_cost + semantic_cost * 0.1
                
                if tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = tentative_g_score + self.heuristic(neighbor, goal)
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))
        
        rospy.logwarn("未找到有效路径")
        return None
    
    def grid_path_to_world_path(self, grid_path, header):
        """将栅格路径转换为世界坐标路径"""
        path_msg = Path()
        path_msg.header = header
        
        for grid_x, grid_y in grid_path:
            world_x, world_y = self.grid_to_world(grid_x, grid_y)
            
            pose_stamped = PoseStamped()
            pose_stamped.header = header
            pose_stamped.pose.position.x = world_x
            pose_stamped.pose.position.y = world_y
            pose_stamped.pose.position.z = 0.0
            pose_stamped.pose.orientation.w = 1.0
            
            path_msg.poses.append(pose_stamped)
        
        return path_msg
    
    def smooth_path(self, path):
        """路径平滑"""
        if not path or len(path.poses) < 3:
            return path
        
        smoothed_path = Path()
        smoothed_path.header = path.header
        
        # 简单的路径平滑：移除不必要的中间点
        smoothed_path.poses.append(path.poses[0])  # 起点
        
        for i in range(1, len(path.poses) - 1):
            prev_pose = smoothed_path.poses[-1]
            curr_pose = path.poses[i]
            next_pose = path.poses[i + 1]
            
            # 检查是否可以直接连接前一个点和下一个点
            if not self.is_line_clear(prev_pose, next_pose):
                smoothed_path.poses.append(curr_pose)
        
        smoothed_path.poses.append(path.poses[-1])  # 终点
        
        return smoothed_path
    
    def is_line_clear(self, pose1, pose2):
        """检查两点之间的直线是否无障碍"""
        x1, y1 = pose1.pose.position.x, pose1.pose.position.y
        x2, y2 = pose2.pose.position.x, pose2.pose.position.y
        
        # 简单的直线检查
        steps = int(np.sqrt((x2 - x1)**2 + (y2 - y1)**2) / self.grid_resolution)
        if steps == 0:
            return True
        
        for i in range(steps + 1):
            t = i / steps
            x = x1 + t * (x2 - x1)
            y = y1 + t * (y2 - y1)
            
            grid_x, grid_y = self.world_to_grid(x, y)
            if not self.is_valid_position(grid_x, grid_y):
                return False
        
        return True


class SemanticPathPlannerNode:
    """语义路径规划器节点"""
    
    def __init__(self):
        rospy.init_node('semantic_path_planner', anonymous=True)
        
        # 初始化规划器
        self.planner = SemanticAStarPlanner()
        
        # 参数配置
        self.load_parameters()
        
        # TF监听器
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)
        
        # 数据缓存
        self.current_pose = None
        self.semantic_map = None
        self.fire_locations = []
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 设置订阅器和发布器
        self.setup_subscribers()
        self.setup_publishers()
        self.setup_services()
        
        rospy.loginfo("🗺️ 语义路径规划器初始化完成")
    
    def load_parameters(self):
        """加载参数"""
        # 从参数服务器加载语义代价
        semantic_costs = rospy.get_param('~semantic_costs', {})
        if semantic_costs:
            self.planner.set_semantic_costs(semantic_costs)
        
        # 其他参数
        self.planner.fire_safety_radius = rospy.get_param('~fire_safety_distance', 2.0)
        self.planner.grid_resolution = rospy.get_param('~grid_resolution', 0.1)
        self.planner.robot_radius = rospy.get_param('~robot_radius', 0.3)
    
    def setup_subscribers(self):
        """设置订阅器"""
        # 语义地图订阅
        self.semantic_map_sub = rospy.Subscriber(
            '/semantic_mapping/enhanced_pointcloud',
            PointCloud2,
            self.semantic_map_callback,
            queue_size=1
        )
        
        # 机器人位姿订阅
        self.pose_sub = rospy.Subscriber(
            '/pose_center/odom',
            PoseStamped,
            self.pose_callback,
            queue_size=1
        )
        
        # 火焰位置订阅
        self.fire_locations_sub = rospy.Subscriber(
            '/semantic_perception/fire_locations_3d',
            MarkerArray,
            self.fire_locations_callback,
            queue_size=10
        )
    
    def setup_publishers(self):
        """设置发布器"""
        # 路径发布
        self.path_pub = rospy.Publisher('planned_path', Path, queue_size=10)
        
        # 语义路径发布
        self.semantic_path_pub = rospy.Publisher('semantic_path', SemanticPath, queue_size=10)
        
        # 可视化发布
        self.visualization_pub = rospy.Publisher('path_visualization', MarkerArray, queue_size=10)
    
    def setup_services(self):
        """设置服务"""
        # 路径规划服务
        self.plan_service = rospy.Service(
            'plan_semantic_path',
            GetSafePath,
            self.plan_path_service_callback
        )
    
    def semantic_map_callback(self, msg):
        """语义地图回调"""
        with self.data_lock:
            self.semantic_map = msg
            self.planner.update_semantic_map(msg)
    
    def pose_callback(self, msg):
        """位姿回调"""
        with self.data_lock:
            self.current_pose = msg
    
    def fire_locations_callback(self, msg):
        """火焰位置回调"""
        try:
            fire_locations = []
            for marker in msg.markers:
                if marker.ns == "fire_locations_3d":
                    fire_location = {
                        'position': [marker.pose.position.x, 
                                   marker.pose.position.y, 
                                   marker.pose.position.z],
                        'confidence': marker.color.a
                    }
                    fire_locations.append(fire_location)
            
            with self.data_lock:
                self.fire_locations = fire_locations
                self.planner.update_fire_locations(fire_locations)
                
        except Exception as e:
            rospy.logerr(f"火焰位置回调失败: {e}")
    
    def plan_path_service_callback(self, req):
        """路径规划服务回调"""
        try:
            response = GetSafePathResponse()
            
            # 规划路径
            path = self.planner.plan_path(
                req.start_pose, 
                req.goal_pose, 
                emergency_mode=req.emergency_mode
            )
            
            if path:
                # 路径平滑
                smoothed_path = self.planner.smooth_path(path)
                
                # 创建语义路径消息
                semantic_path = SemanticPath()
                semantic_path.header = smoothed_path.header
                semantic_path.path = smoothed_path
                semantic_path.total_distance = self.calculate_path_distance(smoothed_path)
                semantic_path.safety_rating = self.calculate_safety_rating(smoothed_path)
                semantic_path.contains_fire_risk = len(self.fire_locations) > 0
                semantic_path.risk_level = self.assess_risk_level(smoothed_path)
                
                response.success = True
                response.message = "路径规划成功"
                response.safe_path = semantic_path
                response.safety_score = semantic_path.safety_rating
                
                # 发布路径
                self.path_pub.publish(smoothed_path)
                self.semantic_path_pub.publish(semantic_path)
                
                rospy.loginfo(f"✅ 路径规划成功，距离: {semantic_path.total_distance:.2f}m")
                
            else:
                response.success = False
                response.message = "未找到有效路径"
                response.safety_score = 0.0
            
            return response
            
        except Exception as e:
            rospy.logerr(f"路径规划服务失败: {e}")
            response = GetSafePathResponse()
            response.success = False
            response.message = f"路径规划失败: {str(e)}"
            return response
    
    def calculate_path_distance(self, path):
        """计算路径总距离"""
        if not path.poses or len(path.poses) < 2:
            return 0.0
        
        total_distance = 0.0
        for i in range(1, len(path.poses)):
            p1 = path.poses[i-1].pose.position
            p2 = path.poses[i].pose.position
            distance = np.sqrt((p2.x - p1.x)**2 + (p2.y - p1.y)**2)
            total_distance += distance
        
        return total_distance
    
    def calculate_safety_rating(self, path):
        """计算路径安全评级"""
        if not path.poses:
            return 0.0
        
        # 基于火焰距离的安全评级
        min_fire_distance = float('inf')
        for pose in path.poses:
            for fire_loc in self.fire_locations:
                distance = np.sqrt(
                    (pose.pose.position.x - fire_loc['position'][0])**2 +
                    (pose.pose.position.y - fire_loc['position'][1])**2
                )
                min_fire_distance = min(min_fire_distance, distance)
        
        if min_fire_distance == float('inf'):
            return 1.0  # 无火焰威胁
        
        # 距离越远安全评级越高
        safety_rating = min(1.0, min_fire_distance / 5.0)  # 5米外为满分
        return safety_rating
    
    def assess_risk_level(self, path):
        """评估路径风险等级"""
        safety_rating = self.calculate_safety_rating(path)
        
        if safety_rating > 0.8:
            return "LOW"
        elif safety_rating > 0.5:
            return "MEDIUM"
        elif safety_rating > 0.2:
            return "HIGH"
        else:
            return "CRITICAL"


if __name__ == '__main__':
    try:
        node = SemanticPathPlannerNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
