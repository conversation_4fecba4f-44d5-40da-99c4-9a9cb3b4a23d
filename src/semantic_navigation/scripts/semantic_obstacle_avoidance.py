#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语义避障控制器
功能：基于语义信息的实时避障和运动控制
支持动态窗口法和语义感知避障
作者：语义SLAM增强项目 - 阶段2扩展
版本：2.1
"""

import rospy
import numpy as np
import threading
import tf2_ros
import tf2_geometry_msgs
from collections import deque

from geometry_msgs.msg import Twist, PoseStamped, Point
from sensor_msgs.msg import LaserScan, PointCloud2
from nav_msgs.msg import Path, Odometry
from visualization_msgs.msg import MarkerArray, Marker
from std_msgs.msg import Header, ColorRGBA, Bool

from semantic_navigation.msg import NavigationStatus, EmergencyAlert
import sensor_msgs.point_cloud2 as pc2

class DynamicWindowApproach:
    """动态窗口法避障算法"""
    
    def __init__(self):
        # 机器人参数
        self.max_speed = 1.0        # 最大线速度 (m/s)
        self.max_angular_speed = 1.0 # 最大角速度 (rad/s)
        self.max_accel = 0.5        # 最大线加速度 (m/s²)
        self.max_angular_accel = 1.0 # 最大角加速度 (rad/s²)
        self.robot_radius = 0.3     # 机器人半径 (m)
        
        # DWA参数
        self.dt = 0.1              # 时间步长 (s)
        self.predict_time = 2.0    # 预测时间 (s)
        self.speed_resolution = 0.1 # 速度分辨率
        self.angular_resolution = 0.1 # 角速度分辨率
        
        # 评价函数权重
        self.alpha = 1.0           # 目标方向权重
        self.beta = 1.0            # 速度权重
        self.gamma = 2.0           # 障碍物距离权重
        self.delta = 1.5           # 语义代价权重
        
        # 安全参数
        self.safe_distance = 0.5   # 安全距离 (m)
        self.fire_safety_distance = 2.0  # 火焰安全距离 (m)
        
        # 当前状态
        self.current_velocity = [0.0, 0.0]  # [v, w]
        self.current_pose = None
        
        # 语义代价权重
        self.semantic_costs = {
            'fire': 1000.0,
            'smoke': 100.0,
            'person': 50.0,
            'chair': 10.0,
            'table': 20.0,
            'wall': 1000.0,
            'door': 1.0,
            'corridor': 1.0,
            'unknown': 50.0,
            'free': 1.0,
        }
    
    def set_robot_params(self, max_speed, max_angular_speed, robot_radius):
        """设置机器人参数"""
        self.max_speed = max_speed
        self.max_angular_speed = max_angular_speed
        self.robot_radius = robot_radius
    
    def set_semantic_costs(self, costs):
        """设置语义代价权重"""
        self.semantic_costs.update(costs)
    
    def get_dynamic_window(self):
        """计算动态窗口"""
        # 速度约束
        v_min = max(0.0, self.current_velocity[0] - self.max_accel * self.dt)
        v_max = min(self.max_speed, self.current_velocity[0] + self.max_accel * self.dt)
        
        # 角速度约束
        w_min = max(-self.max_angular_speed, 
                   self.current_velocity[1] - self.max_angular_accel * self.dt)
        w_max = min(self.max_angular_speed, 
                   self.current_velocity[1] + self.max_angular_accel * self.dt)
        
        return [v_min, v_max, w_min, w_max]
    
    def predict_trajectory(self, v, w):
        """预测轨迹"""
        trajectory = []
        x, y, theta = 0.0, 0.0, 0.0  # 相对于当前位置
        
        steps = int(self.predict_time / self.dt)
        for i in range(steps):
            x += v * np.cos(theta) * self.dt
            y += v * np.sin(theta) * self.dt
            theta += w * self.dt
            trajectory.append([x, y, theta])
        
        return trajectory
    
    def calculate_obstacle_cost(self, trajectory, obstacles):
        """计算障碍物代价"""
        min_distance = float('inf')
        
        for point in trajectory:
            x, y = point[:2]
            
            # 检查与障碍物的距离
            for obs in obstacles:
                obs_x, obs_y = obs[:2]
                distance = np.sqrt((x - obs_x)**2 + (y - obs_y)**2)
                min_distance = min(min_distance, distance)
        
        if min_distance < self.safe_distance:
            return float('inf')  # 碰撞风险
        
        # 距离越近代价越高
        return 1.0 / min_distance
    
    def calculate_semantic_cost(self, trajectory, semantic_map):
        """计算语义代价"""
        total_cost = 0.0
        
        for point in trajectory:
            x, y = point[:2]
            
            # 获取该点的语义标签（简化实现）
            semantic_label = self.get_semantic_label_at_position(x, y, semantic_map)
            semantic_cost = self.semantic_costs.get(semantic_label, self.semantic_costs['unknown'])
            
            if semantic_cost == float('inf'):
                return float('inf')
            
            total_cost += semantic_cost
        
        return total_cost / len(trajectory)
    
    def get_semantic_label_at_position(self, x, y, semantic_map):
        """获取指定位置的语义标签"""
        # 简化实现：返回默认标签
        # 实际应该根据语义地图查询
        return 'free'
    
    def calculate_goal_cost(self, trajectory, goal_position):
        """计算目标代价"""
        if not trajectory:
            return float('inf')
        
        # 轨迹终点
        end_point = trajectory[-1]
        end_x, end_y = end_point[:2]
        
        # 到目标的距离
        goal_x, goal_y = goal_position[:2]
        distance_to_goal = np.sqrt((end_x - goal_x)**2 + (end_y - goal_y)**2)
        
        # 方向差异
        goal_angle = np.arctan2(goal_y - end_y, goal_x - end_x)
        angle_diff = abs(goal_angle - end_point[2])
        angle_diff = min(angle_diff, 2*np.pi - angle_diff)  # 归一化到[0, π]
        
        return distance_to_goal + angle_diff
    
    def calculate_speed_cost(self, v):
        """计算速度代价（鼓励高速度）"""
        return self.max_speed - v
    
    def evaluate_trajectory(self, v, w, goal_position, obstacles, semantic_map, fire_locations):
        """评价轨迹"""
        trajectory = self.predict_trajectory(v, w)
        
        # 检查火焰安全
        for point in trajectory:
            x, y = point[:2]
            for fire_loc in fire_locations:
                fire_x, fire_y = fire_loc['position'][:2]
                distance = np.sqrt((x - fire_x)**2 + (y - fire_y)**2)
                if distance < self.fire_safety_distance:
                    return float('inf')  # 火焰威胁
        
        # 计算各项代价
        obstacle_cost = self.calculate_obstacle_cost(trajectory, obstacles)
        semantic_cost = self.calculate_semantic_cost(trajectory, semantic_map)
        goal_cost = self.calculate_goal_cost(trajectory, goal_position)
        speed_cost = self.calculate_speed_cost(v)
        
        # 检查是否有无穷大代价
        if (obstacle_cost == float('inf') or 
            semantic_cost == float('inf')):
            return float('inf')
        
        # 综合评价
        total_cost = (self.alpha * goal_cost + 
                     self.beta * speed_cost + 
                     self.gamma * obstacle_cost + 
                     self.delta * semantic_cost)
        
        return total_cost
    
    def compute_velocity(self, goal_position, obstacles, semantic_map, fire_locations):
        """计算最优速度"""
        dw = self.get_dynamic_window()
        v_min, v_max, w_min, w_max = dw
        
        best_v, best_w = 0.0, 0.0
        min_cost = float('inf')
        
        # 遍历动态窗口
        v = v_min
        while v <= v_max:
            w = w_min
            while w <= w_max:
                cost = self.evaluate_trajectory(v, w, goal_position, obstacles, 
                                              semantic_map, fire_locations)
                
                if cost < min_cost:
                    min_cost = cost
                    best_v, best_w = v, w
                
                w += self.angular_resolution
            v += self.speed_resolution
        
        # 更新当前速度
        self.current_velocity = [best_v, best_w]
        
        return best_v, best_w


class SemanticObstacleAvoidanceNode:
    """语义避障控制器节点"""
    
    def __init__(self):
        rospy.init_node('semantic_obstacle_avoidance', anonymous=True)
        
        # 初始化DWA算法
        self.dwa = DynamicWindowApproach()
        
        # 参数配置
        self.load_parameters()
        
        # TF监听器
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)
        
        # 数据缓存
        self.current_pose = None
        self.current_goal = None
        self.current_path = None
        self.obstacles = []
        self.fire_locations = []
        self.semantic_map = None
        self.laser_scan = None
        
        # 控制状态
        self.navigation_active = False
        self.emergency_stop = False
        self.path_following_index = 0
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 性能监控
        self.velocity_history = deque(maxlen=100)
        self.last_control_time = rospy.Time.now()
        
        # 设置订阅器和发布器
        self.setup_subscribers()
        self.setup_publishers()
        
        # 控制循环定时器
        self.control_timer = rospy.Timer(rospy.Duration(0.1), self.control_loop)
        
        rospy.loginfo("🚗 语义避障控制器初始化完成")
    
    def load_parameters(self):
        """加载参数"""
        # DWA参数
        max_speed = rospy.get_param('~max_speed', 1.0)
        max_angular_speed = rospy.get_param('~max_angular_speed', 1.0)
        robot_radius = rospy.get_param('~robot_radius', 0.3)
        self.dwa.set_robot_params(max_speed, max_angular_speed, robot_radius)
        
        # 语义代价
        semantic_costs = rospy.get_param('~semantic_costs', {})
        if semantic_costs:
            self.dwa.set_semantic_costs(semantic_costs)
        
        # 安全参数
        self.dwa.fire_safety_distance = rospy.get_param('~fire_safety_distance', 2.0)
        self.dwa.safe_distance = rospy.get_param('~min_obstacle_distance', 0.5)
        
        # 控制参数
        self.goal_tolerance = rospy.get_param('~goal_tolerance', 0.5)
        self.path_lookahead_distance = rospy.get_param('~path_lookahead_distance', 1.0)
    
    def setup_subscribers(self):
        """设置订阅器"""
        # 路径订阅
        self.path_sub = rospy.Subscriber(
            'planned_path',
            Path,
            self.path_callback,
            queue_size=1
        )
        
        # 位姿订阅
        self.pose_sub = rospy.Subscriber(
            '/pose_center/odom',
            PoseStamped,
            self.pose_callback,
            queue_size=1
        )
        
        # 激光雷达订阅
        self.laser_sub = rospy.Subscriber(
            '/scan',
            LaserScan,
            self.laser_callback,
            queue_size=1
        )
        
        # 语义地图订阅
        self.semantic_map_sub = rospy.Subscriber(
            '/semantic_mapping/enhanced_pointcloud',
            PointCloud2,
            self.semantic_map_callback,
            queue_size=1
        )
        
        # 火焰位置订阅
        self.fire_locations_sub = rospy.Subscriber(
            '/semantic_perception/fire_locations_3d',
            MarkerArray,
            self.fire_locations_callback,
            queue_size=10
        )
        
        # 应急停止订阅
        self.emergency_stop_sub = rospy.Subscriber(
            'emergency_stop',
            Bool,
            self.emergency_stop_callback,
            queue_size=1
        )
    
    def setup_publishers(self):
        """设置发布器"""
        # 速度命令发布
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
        
        # 导航状态发布
        self.status_pub = rospy.Publisher('navigation_status', NavigationStatus, queue_size=10)
        
        # 应急警报发布
        self.alert_pub = rospy.Publisher('emergency_alert', EmergencyAlert, queue_size=10)
        
        # 可视化发布
        self.visualization_pub = rospy.Publisher('avoidance_visualization', MarkerArray, queue_size=10)
    
    def path_callback(self, msg):
        """路径回调"""
        with self.data_lock:
            self.current_path = msg
            self.path_following_index = 0
            self.navigation_active = True
            rospy.loginfo(f"📍 接收到新路径，包含 {len(msg.poses)} 个路径点")
    
    def pose_callback(self, msg):
        """位姿回调"""
        with self.data_lock:
            self.current_pose = msg
            self.dwa.current_pose = msg
    
    def laser_callback(self, msg):
        """激光雷达回调"""
        try:
            obstacles = []
            angle = msg.angle_min
            
            for i, range_val in enumerate(msg.ranges):
                if msg.range_min <= range_val <= msg.range_max:
                    # 转换为笛卡尔坐标
                    x = range_val * np.cos(angle)
                    y = range_val * np.sin(angle)
                    obstacles.append([x, y])
                
                angle += msg.angle_increment
            
            with self.data_lock:
                self.obstacles = obstacles
                self.laser_scan = msg
                
        except Exception as e:
            rospy.logerr(f"激光雷达数据处理失败: {e}")
    
    def semantic_map_callback(self, msg):
        """语义地图回调"""
        with self.data_lock:
            self.semantic_map = msg
    
    def fire_locations_callback(self, msg):
        """火焰位置回调"""
        try:
            fire_locations = []
            for marker in msg.markers:
                if marker.ns == "fire_locations_3d":
                    fire_location = {
                        'position': [marker.pose.position.x, 
                                   marker.pose.position.y, 
                                   marker.pose.position.z],
                        'confidence': marker.color.a
                    }
                    fire_locations.append(fire_location)
            
            with self.data_lock:
                self.fire_locations = fire_locations
                
        except Exception as e:
            rospy.logerr(f"火焰位置回调失败: {e}")
    
    def emergency_stop_callback(self, msg):
        """应急停止回调"""
        with self.data_lock:
            self.emergency_stop = msg.data
            if msg.data:
                rospy.logwarn("🚨 接收到应急停止信号")
                self.publish_emergency_alert("应急停止激活", "CRITICAL")
    
    def get_current_goal(self):
        """获取当前目标点"""
        if not self.current_path or not self.current_pose:
            return None
        
        # 寻找前瞻距离内的目标点
        current_x = self.current_pose.pose.position.x
        current_y = self.current_pose.pose.position.y
        
        for i in range(self.path_following_index, len(self.current_path.poses)):
            pose = self.current_path.poses[i]
            distance = np.sqrt(
                (pose.pose.position.x - current_x)**2 + 
                (pose.pose.position.y - current_y)**2
            )
            
            if distance >= self.path_lookahead_distance:
                self.path_following_index = i
                return [pose.pose.position.x, pose.pose.position.y]
        
        # 如果没有找到合适的点，返回路径终点
        if self.current_path.poses:
            end_pose = self.current_path.poses[-1]
            return [end_pose.pose.position.x, end_pose.pose.position.y]
        
        return None
    
    def check_goal_reached(self):
        """检查是否到达目标"""
        if not self.current_path or not self.current_pose:
            return False
        
        goal_pose = self.current_path.poses[-1]
        current_x = self.current_pose.pose.position.x
        current_y = self.current_pose.pose.position.y
        goal_x = goal_pose.pose.position.x
        goal_y = goal_pose.pose.position.y
        
        distance = np.sqrt((goal_x - current_x)**2 + (goal_y - current_y)**2)
        return distance < self.goal_tolerance
    
    def control_loop(self, event):
        """主控制循环"""
        try:
            with self.data_lock:
                # 检查基本条件
                if (not self.navigation_active or 
                    not self.current_pose or 
                    self.emergency_stop):
                    self.publish_zero_velocity()
                    return
                
                # 检查是否到达目标
                if self.check_goal_reached():
                    self.navigation_active = False
                    self.publish_zero_velocity()
                    rospy.loginfo("🎯 已到达目标位置")
                    return
                
                # 获取当前目标
                goal_position = self.get_current_goal()
                if goal_position is None:
                    self.publish_zero_velocity()
                    return
                
                # 检查火焰威胁
                if self.check_fire_threat():
                    self.publish_zero_velocity()
                    self.publish_emergency_alert("检测到火焰威胁", "HIGH")
                    return
                
                # 计算避障速度
                v, w = self.dwa.compute_velocity(
                    goal_position, 
                    self.obstacles, 
                    self.semantic_map, 
                    self.fire_locations
                )
                
                # 发布速度命令
                self.publish_velocity(v, w)
                
                # 记录性能数据
                self.velocity_history.append([v, w])
                self.last_control_time = rospy.Time.now()
                
                # 发布状态
                self.publish_navigation_status()
                
        except Exception as e:
            rospy.logerr(f"控制循环错误: {e}")
            self.publish_zero_velocity()
    
    def check_fire_threat(self):
        """检查火焰威胁"""
        if not self.current_pose or not self.fire_locations:
            return False
        
        current_x = self.current_pose.pose.position.x
        current_y = self.current_pose.pose.position.y
        
        for fire_loc in self.fire_locations:
            fire_x, fire_y = fire_loc['position'][:2]
            distance = np.sqrt((current_x - fire_x)**2 + (current_y - fire_y)**2)
            
            if distance < self.dwa.fire_safety_distance:
                return True
        
        return False
    
    def publish_velocity(self, v, w):
        """发布速度命令"""
        cmd_vel = Twist()
        cmd_vel.linear.x = v
        cmd_vel.angular.z = w
        self.cmd_vel_pub.publish(cmd_vel)
    
    def publish_zero_velocity(self):
        """发布零速度"""
        self.publish_velocity(0.0, 0.0)
    
    def publish_navigation_status(self):
        """发布导航状态"""
        status = NavigationStatus()
        status.header.stamp = rospy.Time.now()
        status.header.frame_id = "map"
        
        status.is_active = self.navigation_active
        status.emergency_stop = self.emergency_stop
        status.current_speed = self.dwa.current_velocity[0]
        status.distance_to_goal = self.calculate_distance_to_goal()
        status.fire_threat_detected = self.check_fire_threat()
        status.obstacle_count = len(self.obstacles)
        
        self.status_pub.publish(status)
    
    def publish_emergency_alert(self, message, severity):
        """发布应急警报"""
        alert = EmergencyAlert()
        alert.header.stamp = rospy.Time.now()
        alert.header.frame_id = "map"
        alert.alert_type = "FIRE_THREAT"
        alert.severity = severity
        alert.message = message
        alert.requires_immediate_action = (severity in ["HIGH", "CRITICAL"])
        
        self.alert_pub.publish(alert)
    
    def calculate_distance_to_goal(self):
        """计算到目标的距离"""
        if not self.current_path or not self.current_pose:
            return 0.0
        
        goal_pose = self.current_path.poses[-1]
        current_x = self.current_pose.pose.position.x
        current_y = self.current_pose.pose.position.y
        goal_x = goal_pose.pose.position.x
        goal_y = goal_pose.pose.position.y
        
        return np.sqrt((goal_x - current_x)**2 + (goal_y - current_y)**2)


if __name__ == '__main__':
    try:
        node = SemanticObstacleAvoidanceNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
