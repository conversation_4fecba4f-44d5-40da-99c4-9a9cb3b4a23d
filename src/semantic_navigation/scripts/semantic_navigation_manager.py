#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语义导航管理器
功能：统一管理语义导航系统的各个组件
基于TSDF+语义检测的智能导航协调
作者：语义SLAM增强项目 - 阶段2
版本：2.0
"""

import rospy
import threading
import time
from enum import Enum
from collections import deque

# ROS消息类型
from std_msgs.msg import Bool, Header
from geometry_msgs.msg import PoseStamped, Twist
from nav_msgs.msg import Path
from visualization_msgs.msg import MarkerArray
from sensor_msgs.msg import PointCloud2

# 自定义消息类型
from semantic_navigation.msg import (
    SemanticNavigationGoal, SemanticPath, NavigationStatus, EmergencyAlert
)
from semantic_navigation.srv import (
    SetNavigationGoal, SetNavigationGoalResponse,
    GetSafePath, GetSafePathResponse,
    EmergencyStop, EmergencyStopResponse
)

# 语义感知消息
from semantic_perception.msg import SemanticObject

class NavigationState(Enum):
    """导航状态枚举"""
    IDLE = "IDLE"
    PLANNING = "PLANNING"
    NAVIGATING = "NAVIGATING"
    EMERGENCY = "EMERGENCY"
    STOPPED = "STOPPED"
    FAILED = "FAILED"

class NavigationMode(Enum):
    """导航模式枚举"""
    NORMAL = "NORMAL"
    EMERGENCY = "EMERGENCY"
    FIRE_ESCAPE = "FIRE_ESCAPE"

class SemanticNavigationManager:
    """语义导航管理器主类"""
    
    def __init__(self):
        rospy.init_node('semantic_navigation_manager', anonymous=True)
        
        # 导航状态管理
        self.navigation_state = NavigationState.IDLE
        self.navigation_mode = NavigationMode.NORMAL
        self.current_goal = None
        self.current_path = None
        
        # 安全监控
        self.fire_detected = False
        self.emergency_active = False
        self.detected_hazards = []
        self.fire_locations = []
        
        # 性能监控
        self.navigation_start_time = None
        self.path_deviation = 0.0
        self.current_speed = 0.0
        
        # 数据缓存
        self.current_pose = None
        self.semantic_map_data = None
        self.fire_detection_history = deque(maxlen=10)
        
        # 线程锁
        self.state_lock = threading.Lock()
        self.data_lock = threading.Lock()
        
        # 参数配置
        self.load_parameters()
        
        # 初始化组件
        self.setup_subscribers()
        self.setup_publishers()
        self.setup_services()
        
        # 启动监控线程
        self.monitoring_thread = threading.Thread(target=self.monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
        rospy.loginfo("🎯 语义导航管理器初始化完成")
        rospy.loginfo(f"   导航状态: {self.navigation_state.value}")
        rospy.loginfo(f"   导航模式: {self.navigation_mode.value}")
    
    def load_parameters(self):
        """加载参数配置"""
        # 安全参数
        self.fire_safety_distance = rospy.get_param('~fire_safety_distance', 2.0)
        self.emergency_stop_distance = rospy.get_param('~emergency_stop_distance', 1.0)
        self.max_navigation_time = rospy.get_param('~max_navigation_time', 300.0)  # 5分钟
        
        # 性能参数
        self.status_publish_rate = rospy.get_param('~status_publish_rate', 2.0)  # 2Hz
        self.monitoring_rate = rospy.get_param('~monitoring_rate', 10.0)  # 10Hz
        self.path_deviation_threshold = rospy.get_param('~path_deviation_threshold', 1.0)
        
        # 应急参数
        self.fire_detection_threshold = rospy.get_param('~fire_detection_threshold', 0.8)
        self.consecutive_fire_detections = rospy.get_param('~consecutive_fire_detections', 3)
        self.emergency_timeout = rospy.get_param('~emergency_timeout', 30.0)  # 30秒
        
        rospy.loginfo(f"参数加载完成 - 火焰安全距离: {self.fire_safety_distance}m")
    
    def setup_subscribers(self):
        """设置订阅器"""
        # 机器人位姿订阅
        self.pose_sub = rospy.Subscriber('/robot_pose', PoseStamped, self.pose_callback, queue_size=1)
        
        # 火焰检测订阅
        self.fire_detection_sub = rospy.Subscriber(
            '/semantic_perception/enhanced_fire_detections', 
            SemanticObject, 
            self.fire_detection_callback, 
            queue_size=10
        )
        
        # 应急警报订阅
        self.emergency_alert_sub = rospy.Subscriber(
            '/semantic_perception/emergency_alert', 
            Bool, 
            self.emergency_alert_callback, 
            queue_size=10
        )
        
        # 3D火焰位置订阅
        self.fire_locations_sub = rospy.Subscriber(
            '/semantic_perception/fire_locations_3d', 
            MarkerArray, 
            self.fire_locations_callback, 
            queue_size=10
        )
        
        # 语义地图订阅
        self.semantic_map_sub = rospy.Subscriber(
            '/semantic_mapping/enhanced_pointcloud', 
            PointCloud2, 
            self.semantic_map_callback, 
            queue_size=1
        )
        
        # 速度命令监控
        self.cmd_vel_sub = rospy.Subscriber('/cmd_vel', Twist, self.cmd_vel_callback, queue_size=1)
    
    def setup_publishers(self):
        """设置发布器"""
        # 导航状态发布
        self.status_pub = rospy.Publisher('navigation_status', NavigationStatus, queue_size=10)
        
        # 应急警报发布
        self.emergency_pub = rospy.Publisher('emergency_alert', EmergencyAlert, queue_size=10)
        
        # 路径可视化发布
        self.path_vis_pub = rospy.Publisher('planned_path_visualization', Path, queue_size=10)
        
        # 安全区域可视化
        self.safety_zones_pub = rospy.Publisher('safety_zones', MarkerArray, queue_size=10)
        
        # 速度命令发布（用于应急停止）
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
    
    def setup_services(self):
        """设置服务"""
        # 设置导航目标服务
        self.set_goal_service = rospy.Service(
            'set_navigation_goal', 
            SetNavigationGoal, 
            self.set_navigation_goal_callback
        )
        
        # 获取安全路径服务
        self.get_safe_path_service = rospy.Service(
            'get_safe_path', 
            GetSafePath, 
            self.get_safe_path_callback
        )
        
        # 应急停止服务
        self.emergency_stop_service = rospy.Service(
            'emergency_stop', 
            EmergencyStop, 
            self.emergency_stop_callback
        )
    
    def pose_callback(self, msg):
        """机器人位姿回调"""
        with self.data_lock:
            self.current_pose = msg
    
    def fire_detection_callback(self, msg):
        """火焰检测回调"""
        try:
            with self.data_lock:
                # 记录火焰检测历史
                detection_data = {
                    'timestamp': rospy.Time.now(),
                    'class_name': msg.class_name,
                    'confidence': msg.confidence,
                    'bbox': (msg.bbox_x, msg.bbox_y, msg.bbox_width, msg.bbox_height)
                }
                self.fire_detection_history.append(detection_data)
                
                # 更新火焰检测状态
                if msg.class_name == 'fire' and msg.confidence >= self.fire_detection_threshold:
                    self.fire_detected = True
                    
                    # 检查连续检测
                    recent_fires = [d for d in self.fire_detection_history 
                                  if d['class_name'] == 'fire' and 
                                     (rospy.Time.now() - d['timestamp']).to_sec() < 5.0]
                    
                    if len(recent_fires) >= self.consecutive_fire_detections:
                        self.trigger_emergency_mode("连续火焰检测")
                        
        except Exception as e:
            rospy.logerr(f"火焰检测回调失败: {e}")
    
    def emergency_alert_callback(self, msg):
        """应急警报回调"""
        try:
            with self.state_lock:
                if msg.data and not self.emergency_active:
                    self.trigger_emergency_mode("外部应急警报")
                elif not msg.data and self.emergency_active:
                    self.clear_emergency_mode()
                    
        except Exception as e:
            rospy.logerr(f"应急警报回调失败: {e}")
    
    def fire_locations_callback(self, msg):
        """3D火焰位置回调"""
        try:
            with self.data_lock:
                self.fire_locations = []
                for marker in msg.markers:
                    if marker.ns == "fire_locations_3d":
                        fire_location = {
                            'position': [marker.pose.position.x, 
                                       marker.pose.position.y, 
                                       marker.pose.position.z],
                            'confidence': marker.color.a,  # 使用透明度表示置信度
                            'timestamp': rospy.Time.now()
                        }
                        self.fire_locations.append(fire_location)
                        
        except Exception as e:
            rospy.logerr(f"火焰位置回调失败: {e}")
    
    def semantic_map_callback(self, msg):
        """语义地图回调"""
        with self.data_lock:
            self.semantic_map_data = msg
    
    def cmd_vel_callback(self, msg):
        """速度命令回调"""
        # 计算当前速度
        self.current_speed = (msg.linear.x**2 + msg.linear.y**2)**0.5

    def trigger_emergency_mode(self, reason):
        """触发应急模式"""
        try:
            with self.state_lock:
                if not self.emergency_active:
                    self.emergency_active = True
                    self.navigation_mode = NavigationMode.EMERGENCY

                    # 立即停止当前导航
                    self.emergency_stop_robot()

                    # 发布应急警报
                    alert = EmergencyAlert()
                    alert.header.stamp = rospy.Time.now()
                    alert.alert_type = "FIRE"
                    alert.severity = "HIGH"
                    alert.description = f"应急模式激活: {reason}"
                    alert.recommended_action = "立即停止并重新规划安全路径"
                    alert.detection_time = rospy.Time.now()
                    alert.confidence = 1.0

                    self.emergency_pub.publish(alert)

                    rospy.logwarn(f"🚨 应急模式激活: {reason}")

        except Exception as e:
            rospy.logerr(f"触发应急模式失败: {e}")

    def clear_emergency_mode(self):
        """清除应急模式"""
        try:
            with self.state_lock:
                if self.emergency_active:
                    self.emergency_active = False
                    self.navigation_mode = NavigationMode.NORMAL
                    self.fire_detected = False

                    rospy.loginfo("✅ 应急模式已清除")

        except Exception as e:
            rospy.logerr(f"清除应急模式失败: {e}")

    def emergency_stop_robot(self):
        """应急停止机器人"""
        try:
            # 发送停止命令
            stop_cmd = Twist()
            for _ in range(5):  # 发送5次确保收到
                self.cmd_vel_pub.publish(stop_cmd)
                rospy.sleep(0.1)

            # 更新导航状态
            self.navigation_state = NavigationState.STOPPED

            rospy.logwarn("🛑 机器人已应急停止")

        except Exception as e:
            rospy.logerr(f"应急停止失败: {e}")

    def set_navigation_goal_callback(self, req):
        """设置导航目标服务回调"""
        try:
            response = SetNavigationGoalResponse()

            with self.state_lock:
                # 检查当前状态
                if self.emergency_active and req.goal.navigation_mode != "EMERGENCY":
                    response.success = False
                    response.message = "系统处于应急状态，只能接受应急导航目标"
                    return response

                # 保存目标
                self.current_goal = req.goal
                self.navigation_start_time = rospy.Time.now()

                # 更新导航模式
                if req.goal.navigation_mode == "EMERGENCY":
                    self.navigation_mode = NavigationMode.EMERGENCY
                elif req.goal.navigation_mode == "FIRE_ESCAPE":
                    self.navigation_mode = NavigationMode.FIRE_ESCAPE
                else:
                    self.navigation_mode = NavigationMode.NORMAL

                # 开始路径规划
                self.navigation_state = NavigationState.PLANNING

                # 生成目标ID
                goal_id = f"goal_{int(time.time())}"

                response.success = True
                response.message = f"导航目标已设置: {self.navigation_mode.value}模式"
                response.goal_id = goal_id
                response.estimated_time = self.estimate_navigation_time(req.goal)

                rospy.loginfo(f"🎯 新导航目标: {self.navigation_mode.value}模式")

                return response

        except Exception as e:
            rospy.logerr(f"设置导航目标失败: {e}")
            response = SetNavigationGoalResponse()
            response.success = False
            response.message = f"设置导航目标失败: {str(e)}"
            return response

    def get_safe_path_callback(self, req):
        """获取安全路径服务回调"""
        try:
            response = GetSafePathResponse()

            # 这里应该调用路径规划器
            # 暂时返回基本响应
            response.success = True
            response.message = "安全路径规划功能开发中"
            response.safety_score = 0.8
            response.warnings = ["路径规划器尚未完全实现"]

            return response

        except Exception as e:
            rospy.logerr(f"获取安全路径失败: {e}")
            response = GetSafePathResponse()
            response.success = False
            response.message = f"获取安全路径失败: {str(e)}"
            return response

    def emergency_stop_callback(self, req):
        """应急停止服务回调"""
        try:
            response = EmergencyStopResponse()

            # 执行应急停止
            self.emergency_stop_robot()

            # 触发应急模式
            self.trigger_emergency_mode(req.reason)

            response.success = True
            response.message = f"应急停止执行成功: {req.reason}"
            response.stop_time = rospy.Time.now()

            if self.current_pose:
                response.final_pose = self.current_pose

            rospy.logwarn(f"🚨 应急停止执行: {req.reason}")

            return response

        except Exception as e:
            rospy.logerr(f"应急停止服务失败: {e}")
            response = EmergencyStopResponse()
            response.success = False
            response.message = f"应急停止失败: {str(e)}"
            return response

    def estimate_navigation_time(self, goal):
        """估计导航时间"""
        try:
            if not self.current_pose:
                return 60.0  # 默认1分钟

            # 简单的距离估算
            dx = goal.target_pose.pose.position.x - self.current_pose.pose.position.x
            dy = goal.target_pose.pose.position.y - self.current_pose.pose.position.y
            distance = (dx**2 + dy**2)**0.5

            # 假设平均速度0.5m/s
            estimated_time = distance / 0.5

            # 应急模式下增加时间
            if self.navigation_mode == NavigationMode.EMERGENCY:
                estimated_time *= 1.5

            return max(estimated_time, 10.0)  # 最少10秒

        except Exception as e:
            rospy.logerr(f"估计导航时间失败: {e}")
            return 60.0

    def monitoring_loop(self):
        """监控主循环"""
        rate = rospy.Rate(self.monitoring_rate)

        while not rospy.is_shutdown():
            try:
                # 发布导航状态
                self.publish_navigation_status()

                # 检查导航超时
                self.check_navigation_timeout()

                # 检查路径偏差
                self.check_path_deviation()

                # 检查火焰威胁
                self.check_fire_threats()

                rate.sleep()

            except Exception as e:
                rospy.logerr(f"监控循环错误: {e}")
                rate.sleep()

    def publish_navigation_status(self):
        """发布导航状态"""
        try:
            status = NavigationStatus()
            status.header.stamp = rospy.Time.now()

            with self.state_lock:
                status.state = self.navigation_state.value
                status.mode = self.navigation_mode.value
                status.fire_detected = self.fire_detected
                status.emergency_active = self.emergency_active
                status.detected_hazards = self.detected_hazards

            with self.data_lock:
                if self.current_pose:
                    status.current_pose = self.current_pose

                if self.current_goal:
                    status.target_pose = self.current_goal.target_pose

                    # 计算进度
                    if self.current_pose:
                        total_distance = self.calculate_distance(
                            self.current_pose, self.current_goal.target_pose)
                        if total_distance > 0:
                            status.distance_remaining = total_distance
                            status.progress = max(0, 1.0 - total_distance / 10.0)  # 假设总距离10m

                status.current_speed = self.current_speed
                status.path_deviation = self.path_deviation

            self.status_pub.publish(status)

        except Exception as e:
            rospy.logerr(f"发布导航状态失败: {e}")

    def calculate_distance(self, pose1, pose2):
        """计算两个位姿之间的距离"""
        try:
            dx = pose1.pose.position.x - pose2.pose.position.x
            dy = pose1.pose.position.y - pose2.pose.position.y
            return (dx**2 + dy**2)**0.5
        except:
            return 0.0

    def check_navigation_timeout(self):
        """检查导航超时"""
        try:
            if (self.navigation_state == NavigationState.NAVIGATING and
                self.navigation_start_time and
                (rospy.Time.now() - self.navigation_start_time).to_sec() > self.max_navigation_time):

                rospy.logwarn("⏰ 导航超时，停止导航")
                self.navigation_state = NavigationState.FAILED

        except Exception as e:
            rospy.logerr(f"检查导航超时失败: {e}")

    def check_path_deviation(self):
        """检查路径偏差"""
        # 这里应该实现路径偏差检查逻辑
        # 暂时跳过
        pass

    def check_fire_threats(self):
        """检查火焰威胁"""
        try:
            if not self.fire_locations or not self.current_pose:
                return

            current_pos = [
                self.current_pose.pose.position.x,
                self.current_pose.pose.position.y,
                self.current_pose.pose.position.z
            ]

            # 检查是否有火焰过近
            for fire_loc in self.fire_locations:
                distance = ((current_pos[0] - fire_loc['position'][0])**2 +
                           (current_pos[1] - fire_loc['position'][1])**2 +
                           (current_pos[2] - fire_loc['position'][2])**2)**0.5

                if distance < self.emergency_stop_distance:
                    self.trigger_emergency_mode(f"火焰过近: {distance:.2f}m")
                    break
                elif distance < self.fire_safety_distance:
                    rospy.logwarn(f"⚠️ 火焰警告: 距离 {distance:.2f}m")

        except Exception as e:
            rospy.logerr(f"检查火焰威胁失败: {e}")


if __name__ == '__main__':
    try:
        manager = SemanticNavigationManager()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
