#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语义导航控制器
功能：统一控制语义导航系统的各个组件
集成路径规划、避障控制和运动执行
作者：语义SLAM增强项目 - 阶段2扩展
版本：2.1
"""

import rospy
import actionlib
import threading
import numpy as np
from enum import Enum

from geometry_msgs.msg import PoseStamped, Twist, Point
from nav_msgs.msg import Path
from move_base_msgs.msg import MoveBaseAction, MoveBaseGoal, MoveBaseResult
from actionlib_msgs.msg import GoalStatus
from std_msgs.msg import Bool, String
from visualization_msgs.msg import MarkerArray, Marker

from semantic_navigation.msg import (
    SemanticNavigationGoal, SemanticPath, NavigationStatus, EmergencyAlert
)
from semantic_navigation.srv import (
    SetNavigationGoal, SetNavigationGoalResponse,
    GetSafePath, GetSafePathRequest,
    EmergencyStop, EmergencyStopResponse
)

class NavigationState(Enum):
    """导航状态枚举"""
    IDLE = "IDLE"
    PLANNING = "PLANNING"
    NAVIGATING = "NAVIGATING"
    AVOIDING = "AVOIDING"
    EMERGENCY_STOP = "EMERGENCY_STOP"
    GOAL_REACHED = "GOAL_REACHED"
    FAILED = "FAILED"

class SemanticNavigationController:
    """语义导航控制器"""
    
    def __init__(self):
        rospy.init_node('semantic_navigation_controller', anonymous=True)
        
        # 初始化状态
        self.current_state = NavigationState.IDLE
        self.current_goal = None
        self.current_path = None
        self.emergency_stop_active = False
        
        # 参数配置
        self.load_parameters()
        
        # 线程锁
        self.state_lock = threading.Lock()
        
        # 数据缓存
        self.current_pose = None
        self.fire_locations = []
        self.navigation_status = None
        
        # 性能监控
        self.start_time = None
        self.goal_reached_count = 0
        self.emergency_stop_count = 0
        
        # 设置客户端、订阅器和发布器
        self.setup_clients()
        self.setup_subscribers()
        self.setup_publishers()
        self.setup_services()
        
        # 状态监控定时器
        self.status_timer = rospy.Timer(rospy.Duration(0.5), self.update_status)
        
        rospy.loginfo("🎮 语义导航控制器初始化完成")
    
    def load_parameters(self):
        """加载参数"""
        # 导航参数
        self.goal_tolerance = rospy.get_param('~goal_tolerance', 0.5)
        self.planning_timeout = rospy.get_param('~planning_timeout', 10.0)
        self.max_navigation_time = rospy.get_param('~max_navigation_time', 300.0)
        
        # 安全参数
        self.fire_safety_distance = rospy.get_param('~fire_safety_distance', 2.0)
        self.emergency_stop_distance = rospy.get_param('~emergency_stop_distance', 1.0)
        
        # 重规划参数
        self.replanning_frequency = rospy.get_param('~replanning_frequency', 1.0)
        self.path_deviation_threshold = rospy.get_param('~path_deviation_threshold', 1.0)
    
    def setup_clients(self):
        """设置服务客户端"""
        # 路径规划服务客户端
        rospy.loginfo("等待路径规划服务...")
        rospy.wait_for_service('plan_semantic_path')
        self.path_planner_client = rospy.ServiceProxy('plan_semantic_path', GetSafePath)
        
        # move_base动作客户端（备用）
        self.move_base_client = actionlib.SimpleActionClient('move_base', MoveBaseAction)
        rospy.loginfo("等待move_base服务...")
        self.move_base_client.wait_for_server(timeout=rospy.Duration(10.0))
    
    def setup_subscribers(self):
        """设置订阅器"""
        # 位姿订阅
        self.pose_sub = rospy.Subscriber(
            '/pose_center/odom',
            PoseStamped,
            self.pose_callback,
            queue_size=1
        )
        
        # 火焰位置订阅
        self.fire_locations_sub = rospy.Subscriber(
            '/semantic_perception/fire_locations_3d',
            MarkerArray,
            self.fire_locations_callback,
            queue_size=10
        )
        
        # 导航状态订阅
        self.nav_status_sub = rospy.Subscriber(
            'navigation_status',
            NavigationStatus,
            self.navigation_status_callback,
            queue_size=1
        )
        
        # 应急警报订阅
        self.alert_sub = rospy.Subscriber(
            'emergency_alert',
            EmergencyAlert,
            self.emergency_alert_callback,
            queue_size=10
        )
    
    def setup_publishers(self):
        """设置发布器"""
        # 导航目标发布
        self.goal_pub = rospy.Publisher('navigation_goal', SemanticNavigationGoal, queue_size=1)
        
        # 路径发布
        self.path_pub = rospy.Publisher('planned_path', Path, queue_size=1)
        
        # 应急停止发布
        self.emergency_stop_pub = rospy.Publisher('emergency_stop', Bool, queue_size=1)
        
        # 状态发布
        self.state_pub = rospy.Publisher('navigation_state', String, queue_size=1)
        
        # 可视化发布
        self.visualization_pub = rospy.Publisher('navigation_visualization', MarkerArray, queue_size=1)
    
    def setup_services(self):
        """设置服务"""
        # 设置导航目标服务
        self.set_goal_service = rospy.Service(
            'set_navigation_goal',
            SetNavigationGoal,
            self.set_navigation_goal_callback
        )
        
        # 应急停止服务
        self.emergency_stop_service = rospy.Service(
            'emergency_stop_service',
            EmergencyStop,
            self.emergency_stop_callback
        )
    
    def pose_callback(self, msg):
        """位姿回调"""
        self.current_pose = msg
    
    def fire_locations_callback(self, msg):
        """火焰位置回调"""
        try:
            fire_locations = []
            for marker in msg.markers:
                if marker.ns == "fire_locations_3d":
                    fire_location = {
                        'position': [marker.pose.position.x, 
                                   marker.pose.position.y, 
                                   marker.pose.position.z],
                        'confidence': marker.color.a
                    }
                    fire_locations.append(fire_location)
            
            self.fire_locations = fire_locations
            
            # 检查火焰威胁
            if self.current_pose and self.current_state == NavigationState.NAVIGATING:
                if self.check_fire_threat():
                    self.trigger_emergency_stop("检测到火焰威胁")
                    
        except Exception as e:
            rospy.logerr(f"火焰位置回调失败: {e}")
    
    def navigation_status_callback(self, msg):
        """导航状态回调"""
        self.navigation_status = msg
        
        # 检查应急情况
        if msg.fire_threat_detected and self.current_state == NavigationState.NAVIGATING:
            self.trigger_emergency_stop("导航过程中检测到火焰威胁")
    
    def emergency_alert_callback(self, msg):
        """应急警报回调"""
        if msg.severity in ["HIGH", "CRITICAL"] and msg.requires_immediate_action:
            self.trigger_emergency_stop(f"应急警报: {msg.message}")
    
    def set_navigation_goal_callback(self, req):
        """设置导航目标服务回调"""
        try:
            response = SetNavigationGoalResponse()
            
            with self.state_lock:
                if self.current_state in [NavigationState.NAVIGATING, NavigationState.PLANNING]:
                    response.success = False
                    response.message = "导航正在进行中，请先停止当前导航"
                    return response
                
                # 设置新目标
                self.current_goal = req.goal
                self.current_state = NavigationState.PLANNING
                self.start_time = rospy.Time.now()
                
                rospy.loginfo(f"🎯 设置新的导航目标: ({req.goal.pose.position.x:.2f}, {req.goal.pose.position.y:.2f})")
                
                # 开始路径规划
                success = self.plan_path_to_goal()
                
                if success:
                    response.success = True
                    response.message = "导航目标设置成功，开始导航"
                    self.current_state = NavigationState.NAVIGATING
                else:
                    response.success = False
                    response.message = "路径规划失败"
                    self.current_state = NavigationState.FAILED
            
            return response
            
        except Exception as e:
            rospy.logerr(f"设置导航目标失败: {e}")
            response = SetNavigationGoalResponse()
            response.success = False
            response.message = f"设置导航目标失败: {str(e)}"
            return response
    
    def emergency_stop_callback(self, req):
        """应急停止服务回调"""
        try:
            response = EmergencyStopResponse()
            
            if req.stop:
                self.trigger_emergency_stop(req.reason)
                response.success = True
                response.message = "应急停止激活"
            else:
                self.resume_navigation()
                response.success = True
                response.message = "应急停止解除"
            
            return response
            
        except Exception as e:
            rospy.logerr(f"应急停止服务失败: {e}")
            response = EmergencyStopResponse()
            response.success = False
            response.message = f"应急停止服务失败: {str(e)}"
            return response
    
    def plan_path_to_goal(self):
        """规划到目标的路径"""
        if not self.current_pose or not self.current_goal:
            rospy.logerr("缺少当前位姿或目标位姿")
            return False
        
        try:
            # 创建路径规划请求
            request = GetSafePathRequest()
            request.start_pose = self.current_pose
            request.goal_pose = self.current_goal
            request.emergency_mode = False
            request.tolerance = self.goal_tolerance
            
            rospy.loginfo("🗺️ 开始路径规划...")
            
            # 调用路径规划服务
            response = self.path_planner_client(request)
            
            if response.success:
                self.current_path = response.safe_path.path
                
                # 发布路径
                self.path_pub.publish(self.current_path)
                
                rospy.loginfo(f"✅ 路径规划成功，路径长度: {response.safe_path.total_distance:.2f}m")
                rospy.loginfo(f"🛡️ 安全评级: {response.safety_score:.2f}")
                
                return True
            else:
                rospy.logerr(f"❌ 路径规划失败: {response.message}")
                return False
                
        except Exception as e:
            rospy.logerr(f"路径规划异常: {e}")
            return False
    
    def check_fire_threat(self):
        """检查火焰威胁"""
        if not self.current_pose or not self.fire_locations:
            return False
        
        current_x = self.current_pose.pose.position.x
        current_y = self.current_pose.pose.position.y
        
        for fire_loc in self.fire_locations:
            fire_x, fire_y = fire_loc['position'][:2]
            distance = np.sqrt((current_x - fire_x)**2 + (current_y - fire_y)**2)
            
            if distance < self.fire_safety_distance:
                return True
        
        return False
    
    def check_goal_reached(self):
        """检查是否到达目标"""
        if not self.current_pose or not self.current_goal:
            return False
        
        current_x = self.current_pose.pose.position.x
        current_y = self.current_pose.pose.position.y
        goal_x = self.current_goal.pose.position.x
        goal_y = self.current_goal.pose.position.y
        
        distance = np.sqrt((goal_x - current_x)**2 + (goal_y - current_y)**2)
        return distance < self.goal_tolerance
    
    def trigger_emergency_stop(self, reason):
        """触发应急停止"""
        with self.state_lock:
            if not self.emergency_stop_active:
                self.emergency_stop_active = True
                self.current_state = NavigationState.EMERGENCY_STOP
                self.emergency_stop_count += 1
                
                # 发布应急停止信号
                stop_msg = Bool()
                stop_msg.data = True
                self.emergency_stop_pub.publish(stop_msg)
                
                rospy.logwarn(f"🚨 应急停止激活: {reason}")
    
    def resume_navigation(self):
        """恢复导航"""
        with self.state_lock:
            if self.emergency_stop_active:
                self.emergency_stop_active = False
                
                # 发布应急停止解除信号
                stop_msg = Bool()
                stop_msg.data = False
                self.emergency_stop_pub.publish(stop_msg)
                
                # 重新规划路径
                if self.current_goal:
                    self.current_state = NavigationState.PLANNING
                    if self.plan_path_to_goal():
                        self.current_state = NavigationState.NAVIGATING
                        rospy.loginfo("✅ 导航恢复，重新规划路径成功")
                    else:
                        self.current_state = NavigationState.FAILED
                        rospy.logerr("❌ 导航恢复失败，路径重规划失败")
                else:
                    self.current_state = NavigationState.IDLE
                    rospy.loginfo("🏁 导航恢复，但无活动目标")
    
    def update_status(self, event):
        """更新状态"""
        try:
            with self.state_lock:
                # 检查目标到达
                if (self.current_state == NavigationState.NAVIGATING and 
                    self.check_goal_reached()):
                    self.current_state = NavigationState.GOAL_REACHED
                    self.goal_reached_count += 1
                    rospy.loginfo("🎯 目标到达！")
                
                # 检查导航超时
                if (self.current_state == NavigationState.NAVIGATING and 
                    self.start_time and 
                    (rospy.Time.now() - self.start_time).to_sec() > self.max_navigation_time):
                    self.current_state = NavigationState.FAILED
                    rospy.logwarn("⏰ 导航超时")
                
                # 发布状态
                state_msg = String()
                state_msg.data = self.current_state.value
                self.state_pub.publish(state_msg)
                
                # 发布可视化
                self.publish_visualization()
                
        except Exception as e:
            rospy.logerr(f"状态更新失败: {e}")
    
    def publish_visualization(self):
        """发布可视化标记"""
        try:
            marker_array = MarkerArray()
            
            # 当前目标标记
            if self.current_goal:
                goal_marker = Marker()
                goal_marker.header.frame_id = "map"
                goal_marker.header.stamp = rospy.Time.now()
                goal_marker.ns = "navigation_goal"
                goal_marker.id = 0
                goal_marker.type = Marker.ARROW
                goal_marker.action = Marker.ADD
                
                goal_marker.pose = self.current_goal.pose
                goal_marker.scale.x = 1.0
                goal_marker.scale.y = 0.2
                goal_marker.scale.z = 0.2
                
                # 根据状态设置颜色
                if self.current_state == NavigationState.GOAL_REACHED:
                    goal_marker.color.r = 0.0
                    goal_marker.color.g = 1.0
                    goal_marker.color.b = 0.0
                elif self.current_state == NavigationState.EMERGENCY_STOP:
                    goal_marker.color.r = 1.0
                    goal_marker.color.g = 0.0
                    goal_marker.color.b = 0.0
                else:
                    goal_marker.color.r = 0.0
                    goal_marker.color.g = 0.0
                    goal_marker.color.b = 1.0
                
                goal_marker.color.a = 0.8
                goal_marker.lifetime = rospy.Duration(1.0)
                
                marker_array.markers.append(goal_marker)
            
            # 状态文本标记
            if self.current_pose:
                text_marker = Marker()
                text_marker.header.frame_id = "map"
                text_marker.header.stamp = rospy.Time.now()
                text_marker.ns = "navigation_status"
                text_marker.id = 1
                text_marker.type = Marker.TEXT_VIEW_FACING
                text_marker.action = Marker.ADD
                
                text_marker.pose.position.x = self.current_pose.pose.position.x
                text_marker.pose.position.y = self.current_pose.pose.position.y
                text_marker.pose.position.z = 2.0
                text_marker.pose.orientation.w = 1.0
                
                text_marker.scale.z = 0.5
                text_marker.color.r = 1.0
                text_marker.color.g = 1.0
                text_marker.color.b = 1.0
                text_marker.color.a = 1.0
                
                text_marker.text = f"状态: {self.current_state.value}"
                text_marker.lifetime = rospy.Duration(1.0)
                
                marker_array.markers.append(text_marker)
            
            self.visualization_pub.publish(marker_array)
            
        except Exception as e:
            rospy.logerr(f"发布可视化失败: {e}")
    
    def get_navigation_statistics(self):
        """获取导航统计信息"""
        stats = {
            'current_state': self.current_state.value,
            'goal_reached_count': self.goal_reached_count,
            'emergency_stop_count': self.emergency_stop_count,
            'navigation_time': 0.0
        }
        
        if self.start_time:
            stats['navigation_time'] = (rospy.Time.now() - self.start_time).to_sec()
        
        return stats


if __name__ == '__main__':
    try:
        controller = SemanticNavigationController()
        
        rospy.loginfo("🚀 语义导航控制器启动完成")
        rospy.loginfo("📋 可用服务:")
        rospy.loginfo("  - /set_navigation_goal: 设置导航目标")
        rospy.loginfo("  - /emergency_stop_service: 应急停止控制")
        rospy.loginfo("📡 发布话题:")
        rospy.loginfo("  - /navigation_state: 导航状态")
        rospy.loginfo("  - /planned_path: 规划路径")
        rospy.loginfo("  - /emergency_stop: 应急停止信号")
        
        rospy.spin()
        
    except rospy.ROSInterruptException:
        pass
