#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语义导航系统测试脚本
功能：测试语义导航系统的各项功能
作者：语义SLAM增强项目 - 阶段2
版本：2.0
"""

import rospy
import time
from geometry_msgs.msg import PoseStamped, Point
from std_msgs.msg import Bool
from semantic_navigation.msg import SemanticNavigationGoal, NavigationStatus
from semantic_navigation.srv import SetNavigationGoal, EmergencyStop

class SemanticNavigationTester:
    """语义导航系统测试器"""
    
    def __init__(self):
        rospy.init_node('semantic_navigation_tester', anonymous=True)
        
        # 等待服务可用
        rospy.loginfo("🔍 等待语义导航服务...")
        rospy.wait_for_service('/semantic_navigation/set_navigation_goal', timeout=30)
        rospy.wait_for_service('/semantic_navigation/emergency_stop', timeout=30)
        
        # 创建服务客户端
        self.set_goal_client = rospy.ServiceProxy('/semantic_navigation/set_navigation_goal', SetNavigationGoal)
        self.emergency_stop_client = rospy.ServiceProxy('/semantic_navigation/emergency_stop', EmergencyStop)
        
        # 订阅状态
        self.status_sub = rospy.Subscriber('/semantic_navigation/status', NavigationStatus, self.status_callback)
        self.current_status = None
        
        rospy.loginfo("✅ 语义导航测试器初始化完成")
    
    def status_callback(self, msg):
        """状态回调"""
        self.current_status = msg
    
    def test_normal_navigation(self):
        """测试正常导航"""
        rospy.loginfo("🎯 测试正常导航模式...")
        
        try:
            # 创建导航目标
            goal = SemanticNavigationGoal()
            goal.header.stamp = rospy.Time.now()
            goal.header.frame_id = "map"
            
            # 设置目标位置
            goal.target_pose.header = goal.header
            goal.target_pose.pose.position.x = 2.0
            goal.target_pose.pose.position.y = 1.0
            goal.target_pose.pose.position.z = 0.0
            goal.target_pose.pose.orientation.w = 1.0
            
            # 设置导航参数
            goal.navigation_mode = "NORMAL"
            goal.avoid_classes = ["fire", "smoke"]
            goal.prefer_classes = ["door", "corridor"]
            goal.safety_distance = 1.0
            goal.fire_avoidance_distance = 2.0
            goal.priority = 5
            goal.emergency_mode = False
            goal.timeout = rospy.Duration(60.0)
            
            # 发送目标
            response = self.set_goal_client(goal)
            
            if response.success:
                rospy.loginfo(f"✅ 正常导航目标设置成功: {response.message}")
                rospy.loginfo(f"   目标ID: {response.goal_id}")
                rospy.loginfo(f"   预计时间: {response.estimated_time:.1f}秒")
                return True
            else:
                rospy.logerr(f"❌ 正常导航目标设置失败: {response.message}")
                return False
                
        except Exception as e:
            rospy.logerr(f"❌ 正常导航测试异常: {e}")
            return False
    
    def test_emergency_navigation(self):
        """测试应急导航"""
        rospy.loginfo("🚨 测试应急导航模式...")
        
        try:
            # 创建应急导航目标
            goal = SemanticNavigationGoal()
            goal.header.stamp = rospy.Time.now()
            goal.header.frame_id = "map"
            
            # 设置安全位置
            goal.target_pose.header = goal.header
            goal.target_pose.pose.position.x = -2.0
            goal.target_pose.pose.position.y = -1.0
            goal.target_pose.pose.position.z = 0.0
            goal.target_pose.pose.orientation.w = 1.0
            
            # 设置应急参数
            goal.navigation_mode = "EMERGENCY"
            goal.avoid_classes = ["fire", "smoke", "person"]
            goal.prefer_classes = ["door", "corridor", "exit"]
            goal.safety_distance = 2.0
            goal.fire_avoidance_distance = 3.0
            goal.priority = 10
            goal.emergency_mode = True
            goal.timeout = rospy.Duration(30.0)
            
            # 发送目标
            response = self.set_goal_client(goal)
            
            if response.success:
                rospy.loginfo(f"✅ 应急导航目标设置成功: {response.message}")
                rospy.loginfo(f"   目标ID: {response.goal_id}")
                rospy.loginfo(f"   预计时间: {response.estimated_time:.1f}秒")
                return True
            else:
                rospy.logerr(f"❌ 应急导航目标设置失败: {response.message}")
                return False
                
        except Exception as e:
            rospy.logerr(f"❌ 应急导航测试异常: {e}")
            return False
    
    def test_emergency_stop(self):
        """测试应急停止"""
        rospy.loginfo("🛑 测试应急停止功能...")
        
        try:
            # 发送应急停止命令
            response = self.emergency_stop_client(
                reason="测试应急停止功能",
                immediate=True,
                hazard_location=Point(x=1.0, y=1.0, z=0.0)
            )
            
            if response.success:
                rospy.loginfo(f"✅ 应急停止成功: {response.message}")
                rospy.loginfo(f"   停止时间: {response.stop_time}")
                return True
            else:
                rospy.logerr(f"❌ 应急停止失败: {response.message}")
                return False
                
        except Exception as e:
            rospy.logerr(f"❌ 应急停止测试异常: {e}")
            return False
    
    def monitor_status(self, duration=10):
        """监控系统状态"""
        rospy.loginfo(f"📊 监控系统状态 {duration} 秒...")
        
        start_time = time.time()
        while time.time() - start_time < duration and not rospy.is_shutdown():
            if self.current_status:
                rospy.loginfo(f"状态: {self.current_status.state} | "
                            f"模式: {self.current_status.mode} | "
                            f"火焰检测: {self.current_status.fire_detected} | "
                            f"应急状态: {self.current_status.emergency_active}")
            time.sleep(2)
    
    def run_all_tests(self):
        """运行所有测试"""
        rospy.loginfo("🚀 开始语义导航系统全面测试")
        rospy.loginfo("=" * 50)
        
        test_results = []
        
        # 测试1: 正常导航
        result1 = self.test_normal_navigation()
        test_results.append(("正常导航", result1))
        time.sleep(3)
        
        # 监控状态
        self.monitor_status(5)
        
        # 测试2: 应急导航
        result2 = self.test_emergency_navigation()
        test_results.append(("应急导航", result2))
        time.sleep(3)
        
        # 监控状态
        self.monitor_status(5)
        
        # 测试3: 应急停止
        result3 = self.test_emergency_stop()
        test_results.append(("应急停止", result3))
        time.sleep(2)
        
        # 输出测试结果
        rospy.loginfo("=" * 50)
        rospy.loginfo("📋 测试结果汇总:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            rospy.loginfo(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        rospy.loginfo(f"总体结果: {passed}/{len(test_results)} 项测试通过")
        
        if passed == len(test_results):
            rospy.loginfo("🎉 所有测试通过！语义导航系统运行正常")
        else:
            rospy.logwarn("⚠️ 部分测试失败，请检查系统配置")
        
        return passed == len(test_results)


def main():
    """主函数"""
    try:
        tester = SemanticNavigationTester()
        
        # 等待系统稳定
        rospy.loginfo("⏳ 等待系统稳定...")
        time.sleep(5)
        
        # 运行测试
        success = tester.run_all_tests()
        
        if success:
            rospy.loginfo("🎯 语义导航系统测试完成 - 系统正常")
        else:
            rospy.logwarn("⚠️ 语义导航系统测试完成 - 发现问题")
            
    except rospy.ROSInterruptException:
        rospy.loginfo("测试被中断")
    except Exception as e:
        rospy.logerr(f"测试异常: {e}")


if __name__ == '__main__':
    main()
