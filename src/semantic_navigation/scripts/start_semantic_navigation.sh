#!/bin/bash

# 语义感知导航系统启动脚本
# 基于TSDF+语义检测的火灾应急导航
# 作者：语义SLAM增强项目 - 阶段2
# 版本：2.0

echo "🚀 启动语义感知导航系统"
echo "基于TSDF+语义检测的火灾应急导航"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查ROS环境
if [ -z "$ROS_MASTER_URI" ]; then
    echo -e "${RED}❌ ROS环境未设置，请先运行: source /opt/ros/melodic/setup.bash${NC}"
    exit 1
fi

# 检查工作空间
if [ ! -f "devel/setup.bash" ]; then
    echo -e "${RED}❌ 请在rtab_ws根目录下运行此脚本${NC}"
    exit 1
fi

# 检查前置系统
echo -e "${BLUE}🔍 检查前置系统状态...${NC}"

# 检查TSDF建图系统
if ! rostopic list 2>/dev/null | grep -q "/tsdf_fusion_node/tsdf_pointcloud"; then
    echo -e "${RED}❌ TSDF建图系统未运行${NC}"
    echo -e "${YELLOW}请先启动: ./stage_2_rtab_tsdf.sh${NC}"
    exit 1
fi
echo -e "${GREEN}✅ TSDF建图系统运行正常${NC}"

# 检查语义检测系统
if ! rostopic list 2>/dev/null | grep -q "/semantic_perception"; then
    echo -e "${RED}❌ 语义检测系统未运行${NC}"
    echo -e "${YELLOW}请先启动: ./quick_start_enhanced_semantic.sh${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 语义检测系统运行正常${NC}"

# 检查实时物体检测
if ! pgrep -f "real_object_detector.py" > /dev/null; then
    echo -e "${YELLOW}⚠️ 实时物体检测器未运行${NC}"
    echo -e "${YELLOW}建议启动: python3 real_object_detector.py${NC}"
fi

# 检查语义点云发布器
if ! pgrep -f "real_semantic_pointcloud_publisher.py" > /dev/null; then
    echo -e "${YELLOW}⚠️ 语义点云发布器未运行${NC}"
    echo -e "${YELLOW}建议启动: python3 real_semantic_pointcloud_publisher.py${NC}"
fi

# 编译检查
echo -e "${BLUE}🔧 检查编译状态...${NC}"
if [ ! -f "devel/lib/semantic_navigation/semantic_navigation_manager.py" ]; then
    echo -e "${YELLOW}⚠️ 语义导航包需要编译${NC}"
    echo -e "${BLUE}正在编译...${NC}"
    catkin_make --pkg semantic_navigation
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 编译失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 编译完成${NC}"
fi

# 设置环境
source devel/setup.bash

# 导航模式选择
echo -e "${BLUE}🎯 选择导航模式:${NC}"
echo "1) 正常导航模式 (NORMAL)"
echo "2) 应急导航模式 (EMERGENCY)" 
echo "3) 火灾逃生模式 (FIRE_ESCAPE)"
echo -n "请选择 [1-3]: "
read -r mode_choice

case $mode_choice in
    1)
        NAVIGATION_MODE="normal"
        LAUNCH_FILE="semantic_navigation.launch"
        echo -e "${GREEN}✅ 选择正常导航模式${NC}"
        ;;
    2)
        NAVIGATION_MODE="emergency"
        LAUNCH_FILE="fire_emergency_navigation.launch"
        echo -e "${YELLOW}⚠️ 选择应急导航模式${NC}"
        ;;
    3)
        NAVIGATION_MODE="fire_escape"
        LAUNCH_FILE="fire_emergency_navigation.launch"
        echo -e "${RED}🚨 选择火灾逃生模式${NC}"
        ;;
    *)
        echo -e "${YELLOW}使用默认正常导航模式${NC}"
        NAVIGATION_MODE="normal"
        LAUNCH_FILE="semantic_navigation.launch"
        ;;
esac

# 可视化选择
echo -e "${BLUE}📊 启用可视化? [y/N]: ${NC}"
read -r viz_choice
if [[ $viz_choice =~ ^[Yy]$ ]]; then
    ENABLE_VIZ="true"
    echo -e "${GREEN}✅ 启用可视化${NC}"
else
    ENABLE_VIZ="false"
    echo -e "${YELLOW}⚠️ 禁用可视化${NC}"
fi

# 启动系统
echo -e "${BLUE}🚀 启动语义导航系统...${NC}"
echo -e "${BLUE}模式: ${NAVIGATION_MODE}${NC}"
echo -e "${BLUE}启动文件: ${LAUNCH_FILE}${NC}"
echo -e "${BLUE}可视化: ${ENABLE_VIZ}${NC}"
echo "========================================"

# 启动roslaunch
if [ "$NAVIGATION_MODE" = "normal" ]; then
    roslaunch semantic_navigation $LAUNCH_FILE \
        navigation_mode:=$NAVIGATION_MODE \
        enable_visualization:=$ENABLE_VIZ
else
    roslaunch semantic_navigation $LAUNCH_FILE \
        emergency_mode:=true \
        fire_escape_mode:=true \
        enable_visualization:=$ENABLE_VIZ \
        safety_distance:=2.0 \
        emergency_speed:=0.5
fi

# 启动后提示
echo ""
echo -e "${GREEN}🎉 语义感知导航系统已启动！${NC}"
echo "========================================"
echo -e "${BLUE}📋 可用命令:${NC}"
echo "🎯 设置导航目标:"
echo "   rosservice call /semantic_navigation/set_navigation_goal ..."
echo ""
echo "🛑 应急停止:"
echo "   rosservice call /semantic_navigation/emergency_stop ..."
echo ""
echo "📊 查看导航状态:"
echo "   rostopic echo /semantic_navigation/status"
echo ""
echo "🔥 查看火焰检测:"
echo "   rostopic echo /semantic_perception/enhanced_fire_detections"
echo ""
echo "🗺️ 查看语义地图:"
echo "   rostopic echo /semantic_mapping/enhanced_pointcloud"
echo ""
echo -e "${YELLOW}⚠️ 注意: 系统处于${NAVIGATION_MODE}模式${NC}"
echo -e "${RED}🚨 应急情况下请立即调用emergency_stop服务${NC}"
echo "========================================"
