#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语义代价地图转换器
功能：将语义点云转换为ROS导航栈兼容的代价地图
支持语义代价权重和动态更新
作者：语义SLAM增强项目 - 阶段2扩展
版本：2.1
"""

import rospy
import numpy as np
import threading
from scipy import ndimage
import cv2

from nav_msgs.msg import OccupancyGrid
from sensor_msgs.msg import PointCloud2
from geometry_msgs.msg import PoseStamped
from visualization_msgs.msg import MarkerArray, Marker
from std_msgs.msg import Header, ColorRGBA

import sensor_msgs.point_cloud2 as pc2

class SemanticCostmapConverter:
    """语义代价地图转换器"""
    
    def __init__(self):
        # 地图参数
        self.resolution = 0.1       # 栅格分辨率 (米/像素)
        self.width = 1000          # 地图宽度 (像素)
        self.height = 1000         # 地图高度 (像素)
        self.origin_x = -50.0      # 地图原点X (米)
        self.origin_y = -50.0      # 地图原点Y (米)
        
        # 语义代价权重 (0-100)
        self.semantic_costs = {
            'free': 0,           # 自由空间
            'unknown': 50,       # 未知区域
            'wall': 100,         # 墙壁
            'table': 80,         # 桌子
            'chair': 60,         # 椅子
            'person': 70,        # 人员
            'door': 10,          # 门口
            'corridor': 5,       # 走廊
            'fire': 100,         # 火焰区域
            'smoke': 90,         # 烟雾区域
        }
        
        # 膨胀参数
        self.inflation_radius = 0.5  # 膨胀半径 (米)
        self.inflation_decay = 0.8   # 膨胀衰减因子
        
        # 火焰安全参数
        self.fire_safety_radius = 2.0  # 火焰安全半径 (米)
        self.fire_cost_decay = 0.9     # 火焰代价衰减
        
        # 地图数据
        self.costmap = np.zeros((self.height, self.width), dtype=np.uint8)
        self.semantic_map = np.full((self.height, self.width), 'unknown', dtype=object)
        self.fire_locations = []
        
        # 更新标志
        self.map_updated = False
        self.fire_updated = False
        
    def set_map_params(self, resolution, width, height, origin_x, origin_y):
        """设置地图参数"""
        self.resolution = resolution
        self.width = width
        self.height = height
        self.origin_x = origin_x
        self.origin_y = origin_y
        
        # 重新初始化地图
        self.costmap = np.zeros((self.height, self.width), dtype=np.uint8)
        self.semantic_map = np.full((self.height, self.width), 'unknown', dtype=object)
    
    def set_semantic_costs(self, costs_dict):
        """设置语义代价权重"""
        self.semantic_costs.update(costs_dict)
        self.map_updated = True
    
    def world_to_grid(self, world_x, world_y):
        """世界坐标转栅格坐标"""
        grid_x = int((world_x - self.origin_x) / self.resolution)
        grid_y = int((world_y - self.origin_y) / self.resolution)
        return grid_x, grid_y
    
    def grid_to_world(self, grid_x, grid_y):
        """栅格坐标转世界坐标"""
        world_x = grid_x * self.resolution + self.origin_x
        world_y = grid_y * self.resolution + self.origin_y
        return world_x, world_y
    
    def is_valid_grid(self, grid_x, grid_y):
        """检查栅格坐标是否有效"""
        return 0 <= grid_x < self.width and 0 <= grid_y < self.height
    
    def update_semantic_map(self, pointcloud_msg):
        """更新语义地图"""
        try:
            # 清空当前地图
            self.semantic_map.fill('unknown')
            
            # 解析点云数据
            for point in pc2.read_points(pointcloud_msg, skip_nans=True):
                x, y, z = point[:3]
                
                # 获取语义标签（假设在第4个字段）
                semantic_label = int(point[3]) if len(point) > 3 else 0
                
                # 转换为栅格坐标
                grid_x, grid_y = self.world_to_grid(x, y)
                
                if self.is_valid_grid(grid_x, grid_y):
                    # 映射语义标签
                    label = self.map_semantic_label(semantic_label)
                    self.semantic_map[grid_y, grid_x] = label
            
            self.map_updated = True
            
        except Exception as e:
            rospy.logerr(f"更新语义地图失败: {e}")
    
    def map_semantic_label(self, label_id):
        """映射语义标签ID到标签名称"""
        label_mapping = {
            0: 'free',
            1: 'wall',
            2: 'chair',
            3: 'table',
            4: 'person',
            5: 'fire',
            6: 'smoke',
            7: 'door',
            8: 'corridor',
        }
        return label_mapping.get(label_id, 'unknown')
    
    def update_fire_locations(self, fire_locations):
        """更新火焰位置"""
        self.fire_locations = fire_locations
        self.fire_updated = True
    
    def generate_base_costmap(self):
        """生成基础代价地图"""
        costmap = np.zeros((self.height, self.width), dtype=np.float32)
        
        # 遍历所有栅格
        for y in range(self.height):
            for x in range(self.width):
                semantic_label = self.semantic_map[y, x]
                cost = self.semantic_costs.get(semantic_label, self.semantic_costs['unknown'])
                costmap[y, x] = cost
        
        return costmap
    
    def apply_inflation(self, costmap):
        """应用膨胀算法"""
        # 计算膨胀半径（像素）
        inflation_pixels = int(self.inflation_radius / self.resolution)
        
        # 创建膨胀核
        kernel_size = 2 * inflation_pixels + 1
        kernel = np.zeros((kernel_size, kernel_size), dtype=np.float32)
        
        center = inflation_pixels
        for y in range(kernel_size):
            for x in range(kernel_size):
                distance = np.sqrt((x - center)**2 + (y - center)**2)
                if distance <= inflation_pixels:
                    # 指数衰减
                    decay_factor = np.exp(-distance * self.inflation_decay / inflation_pixels)
                    kernel[y, x] = decay_factor
        
        # 应用膨胀
        inflated_costmap = ndimage.maximum_filter(costmap, footprint=kernel)
        
        return inflated_costmap
    
    def apply_fire_safety_zones(self, costmap):
        """应用火焰安全区域"""
        if not self.fire_locations:
            return costmap
        
        fire_costmap = np.copy(costmap)
        safety_pixels = int(self.fire_safety_radius / self.resolution)
        
        for fire_loc in self.fire_locations:
            fire_x, fire_y = fire_loc['position'][:2]
            fire_grid_x, fire_grid_y = self.world_to_grid(fire_x, fire_y)
            
            if not self.is_valid_grid(fire_grid_x, fire_grid_y):
                continue
            
            # 在火焰周围创建安全区域
            for dy in range(-safety_pixels, safety_pixels + 1):
                for dx in range(-safety_pixels, safety_pixels + 1):
                    grid_x = fire_grid_x + dx
                    grid_y = fire_grid_y + dy
                    
                    if not self.is_valid_grid(grid_x, grid_y):
                        continue
                    
                    distance = np.sqrt(dx**2 + dy**2) * self.resolution
                    if distance <= self.fire_safety_radius:
                        # 距离火焰越近代价越高
                        fire_cost = 100 * np.exp(-distance * self.fire_cost_decay / self.fire_safety_radius)
                        fire_costmap[grid_y, grid_x] = max(fire_costmap[grid_y, grid_x], fire_cost)
        
        return fire_costmap
    
    def smooth_costmap(self, costmap):
        """平滑代价地图"""
        # 使用高斯滤波平滑
        smoothed = cv2.GaussianBlur(costmap.astype(np.float32), (5, 5), 1.0)
        return smoothed
    
    def generate_costmap(self):
        """生成完整的代价地图"""
        if not self.map_updated and not self.fire_updated:
            return self.costmap
        
        # 生成基础代价地图
        base_costmap = self.generate_base_costmap()
        
        # 应用膨胀
        inflated_costmap = self.apply_inflation(base_costmap)
        
        # 应用火焰安全区域
        fire_costmap = self.apply_fire_safety_zones(inflated_costmap)
        
        # 平滑处理
        smoothed_costmap = self.smooth_costmap(fire_costmap)
        
        # 归一化到0-100范围
        normalized_costmap = np.clip(smoothed_costmap, 0, 100).astype(np.uint8)
        
        self.costmap = normalized_costmap
        self.map_updated = False
        self.fire_updated = False
        
        return self.costmap
    
    def create_occupancy_grid_msg(self, frame_id="map"):
        """创建OccupancyGrid消息"""
        # 生成代价地图
        costmap = self.generate_costmap()
        
        # 创建消息
        grid_msg = OccupancyGrid()
        grid_msg.header.stamp = rospy.Time.now()
        grid_msg.header.frame_id = frame_id
        
        # 设置地图信息
        grid_msg.info.resolution = self.resolution
        grid_msg.info.width = self.width
        grid_msg.info.height = self.height
        grid_msg.info.origin.position.x = self.origin_x
        grid_msg.info.origin.position.y = self.origin_y
        grid_msg.info.origin.position.z = 0.0
        grid_msg.info.origin.orientation.w = 1.0
        
        # 转换代价地图格式
        # ROS OccupancyGrid: -1=unknown, 0=free, 100=occupied
        # 我们的代价地图: 0=free, 50=unknown, 100=occupied
        ros_data = []
        for y in range(self.height):
            for x in range(self.width):
                cost = costmap[y, x]
                if cost == 50:  # unknown
                    ros_data.append(-1)
                elif cost >= 90:  # high cost = occupied
                    ros_data.append(100)
                else:  # scale to 0-99
                    ros_data.append(int(cost * 0.99))
        
        grid_msg.data = ros_data
        
        return grid_msg
    
    def get_cost_at_position(self, world_x, world_y):
        """获取指定世界坐标的代价值"""
        grid_x, grid_y = self.world_to_grid(world_x, world_y)
        
        if not self.is_valid_grid(grid_x, grid_y):
            return 100  # 超出地图范围视为高代价
        
        return self.costmap[grid_y, grid_x]
    
    def is_position_safe(self, world_x, world_y, safety_threshold=80):
        """检查位置是否安全"""
        cost = self.get_cost_at_position(world_x, world_y)
        return cost < safety_threshold
    
    def find_safe_positions_near(self, world_x, world_y, search_radius=2.0):
        """在指定位置附近寻找安全位置"""
        safe_positions = []
        search_pixels = int(search_radius / self.resolution)
        
        center_grid_x, center_grid_y = self.world_to_grid(world_x, world_y)
        
        for dy in range(-search_pixels, search_pixels + 1):
            for dx in range(-search_pixels, search_pixels + 1):
                grid_x = center_grid_x + dx
                grid_y = center_grid_y + dy
                
                if not self.is_valid_grid(grid_x, grid_y):
                    continue
                
                distance = np.sqrt(dx**2 + dy**2) * self.resolution
                if distance <= search_radius:
                    if self.costmap[grid_y, grid_x] < 50:  # 安全阈值
                        safe_world_x, safe_world_y = self.grid_to_world(grid_x, grid_y)
                        safe_positions.append((safe_world_x, safe_world_y, distance))
        
        # 按距离排序
        safe_positions.sort(key=lambda x: x[2])
        
        return safe_positions


class SemanticCostmapConverterNode:
    """语义代价地图转换器节点"""
    
    def __init__(self):
        rospy.init_node('semantic_costmap_converter', anonymous=True)
        
        # 初始化转换器
        self.converter = SemanticCostmapConverter()
        
        # 参数配置
        self.load_parameters()
        
        # 数据缓存
        self.semantic_map = None
        self.fire_locations = []
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 设置订阅器和发布器
        self.setup_subscribers()
        self.setup_publishers()
        
        # 定时发布代价地图
        self.publish_timer = rospy.Timer(rospy.Duration(1.0), self.publish_costmap)
        
        rospy.loginfo("🗺️ 语义代价地图转换器初始化完成")
    
    def load_parameters(self):
        """加载参数"""
        # 地图参数
        resolution = rospy.get_param('~resolution', 0.1)
        width = rospy.get_param('~width', 1000)
        height = rospy.get_param('~height', 1000)
        origin_x = rospy.get_param('~origin_x', -50.0)
        origin_y = rospy.get_param('~origin_y', -50.0)
        
        self.converter.set_map_params(resolution, width, height, origin_x, origin_y)
        
        # 语义代价权重
        semantic_costs = rospy.get_param('~semantic_costs', {})
        if semantic_costs:
            self.converter.set_semantic_costs(semantic_costs)
        
        # 膨胀参数
        self.converter.inflation_radius = rospy.get_param('~inflation_radius', 0.5)
        self.converter.fire_safety_radius = rospy.get_param('~fire_safety_radius', 2.0)
    
    def setup_subscribers(self):
        """设置订阅器"""
        # 语义地图订阅
        self.semantic_map_sub = rospy.Subscriber(
            '/semantic_mapping/enhanced_pointcloud',
            PointCloud2,
            self.semantic_map_callback,
            queue_size=1
        )
        
        # 火焰位置订阅
        self.fire_locations_sub = rospy.Subscriber(
            '/semantic_perception/fire_locations_3d',
            MarkerArray,
            self.fire_locations_callback,
            queue_size=10
        )
    
    def setup_publishers(self):
        """设置发布器"""
        # 代价地图发布
        self.costmap_pub = rospy.Publisher('semantic_costmap', OccupancyGrid, queue_size=1)
        
        # 全局代价地图发布（用于move_base）
        self.global_costmap_pub = rospy.Publisher('/move_base/global_costmap/costmap', OccupancyGrid, queue_size=1)
        
        # 可视化发布
        self.visualization_pub = rospy.Publisher('costmap_visualization', MarkerArray, queue_size=1)
    
    def semantic_map_callback(self, msg):
        """语义地图回调"""
        with self.data_lock:
            self.semantic_map = msg
            self.converter.update_semantic_map(msg)
    
    def fire_locations_callback(self, msg):
        """火焰位置回调"""
        try:
            fire_locations = []
            for marker in msg.markers:
                if marker.ns == "fire_locations_3d":
                    fire_location = {
                        'position': [marker.pose.position.x, 
                                   marker.pose.position.y, 
                                   marker.pose.position.z],
                        'confidence': marker.color.a
                    }
                    fire_locations.append(fire_location)
            
            with self.data_lock:
                self.fire_locations = fire_locations
                self.converter.update_fire_locations(fire_locations)
                
        except Exception as e:
            rospy.logerr(f"火焰位置回调失败: {e}")
    
    def publish_costmap(self, event):
        """发布代价地图"""
        try:
            with self.data_lock:
                # 生成代价地图消息
                costmap_msg = self.converter.create_occupancy_grid_msg()
                
                # 发布代价地图
                self.costmap_pub.publish(costmap_msg)
                self.global_costmap_pub.publish(costmap_msg)
                
                # 发布可视化
                self.publish_visualization()
                
        except Exception as e:
            rospy.logerr(f"发布代价地图失败: {e}")
    
    def publish_visualization(self):
        """发布可视化标记"""
        try:
            marker_array = MarkerArray()
            
            # 火焰安全区域标记
            for i, fire_loc in enumerate(self.fire_locations):
                marker = Marker()
                marker.header.frame_id = "map"
                marker.header.stamp = rospy.Time.now()
                marker.ns = "fire_safety_zones"
                marker.id = i
                marker.type = Marker.CYLINDER
                marker.action = Marker.ADD
                
                marker.pose.position.x = fire_loc['position'][0]
                marker.pose.position.y = fire_loc['position'][1]
                marker.pose.position.z = 0.1
                marker.pose.orientation.w = 1.0
                
                marker.scale.x = self.converter.fire_safety_radius * 2
                marker.scale.y = self.converter.fire_safety_radius * 2
                marker.scale.z = 0.2
                
                marker.color.r = 1.0
                marker.color.g = 0.0
                marker.color.b = 0.0
                marker.color.a = 0.3
                
                marker.lifetime = rospy.Duration(2.0)
                
                marker_array.markers.append(marker)
            
            self.visualization_pub.publish(marker_array)
            
        except Exception as e:
            rospy.logerr(f"发布可视化失败: {e}")


if __name__ == '__main__':
    try:
        node = SemanticCostmapConverterNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
