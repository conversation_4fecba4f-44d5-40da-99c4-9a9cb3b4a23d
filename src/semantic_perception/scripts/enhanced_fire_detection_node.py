#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强火焰检测节点
功能：集成深度信息、多模态融合的高精度火焰检测
基于TSDF算法级融合建图的3D火焰定位
作者：语义SLAM增强项目 - 阶段2
版本：2.0
"""

import rospy
import cv2
import numpy as np
import torch
import torch.nn.functional as F
from cv_bridge import CvBridge
import time
from collections import deque
import threading
import tf2_ros
import tf2_geometry_msgs

from sensor_msgs.msg import Image, CameraInfo, PointCloud2
from semantic_perception.msg import SemanticObject
from std_msgs.msg import Header, Bool
from geometry_msgs.msg import Point, PointStamped, TransformStamped
from visualization_msgs.msg import Marker, MarkerArray
import sensor_msgs.point_cloud2 as pc2

class EnhancedFireDetector:
    """增强火焰检测器 - 集成深度信息和多模态融合"""
    
    def __init__(self):
        # 多模态融合权重
        self.fusion_weights = {
            'color': 0.4,      # 颜色特征权重
            'motion': 0.3,     # 运动特征权重
            'depth': 0.2,      # 深度特征权重
            'texture': 0.1     # 纹理特征权重
        }
        
        # 火焰检测参数 - 增强版
        self.fire_color_ranges = [
            # 橙红色火焰 (HSV)
            {'lower': np.array([0, 50, 50]), 'upper': np.array([15, 255, 255])},
            # 黄色火焰
            {'lower': np.array([15, 50, 50]), 'upper': np.array([35, 255, 255])},
            # 深红色火焰
            {'lower': np.array([160, 50, 50]), 'upper': np.array([180, 255, 255])},
        ]
        
        # 烟雾检测参数 - 增强版
        self.smoke_color_range = {'lower': np.array([0, 0, 50]), 'upper': np.array([180, 30, 200])}
        
        # 深度约束参数
        self.depth_constrained_detection = True
        self.min_fire_depth = 0.3  # 最小火焰深度 (米)
        self.max_fire_depth = 10.0  # 最大火焰深度 (米)
        self.depth_consistency_threshold = 0.5  # 深度一致性阈值
        
        # 动态特征检测参数
        self.motion_threshold = 15  # 提高运动阈值
        self.flicker_threshold = 0.4  # 提高闪烁阈值
        self.temporal_window = 8  # 时序窗口大小
        
        # 形状特征参数 - 增强版
        self.min_fire_area = 150  # 最小火焰面积
        self.min_smoke_area = 300  # 最小烟雾面积
        self.fire_aspect_ratio_range = (0.3, 3.0)  # 火焰纵横比范围
        self.fire_solidity_threshold = 0.3  # 火焰实心度阈值
        
        # 历史帧缓存用于动态分析
        self.frame_history = deque(maxlen=self.temporal_window)
        self.depth_history = deque(maxlen=self.temporal_window)
        
        # 3D火焰位置跟踪
        self.fire_locations_3d = []
        self.fire_tracking_threshold = 1.0  # 火焰跟踪距离阈值 (米)
        
    def detect_fire_with_depth(self, image, depth_image):
        """集成深度信息的火焰检测"""
        # 基础颜色检测
        fire_color_mask = self.detect_fire_by_color(image)
        
        if not self.depth_constrained_detection or depth_image is None:
            return fire_color_mask
        
        # 深度约束
        depth_mask = self.apply_depth_constraints(depth_image)
        
        # 结合颜色和深度信息
        depth_constrained_mask = cv2.bitwise_and(fire_color_mask, depth_mask)
        
        # 深度一致性检查
        consistent_mask = self.check_depth_consistency(depth_constrained_mask, depth_image)
        
        return consistent_mask
    
    def apply_depth_constraints(self, depth_image):
        """应用深度约束"""
        # 创建深度掩码
        depth_mask = np.zeros(depth_image.shape[:2], dtype=np.uint8)
        
        # 有效深度范围
        valid_depth = (depth_image >= self.min_fire_depth) & (depth_image <= self.max_fire_depth)
        depth_mask[valid_depth] = 255
        
        # 去除深度噪声
        kernel = np.ones((3, 3), np.uint8)
        depth_mask = cv2.morphologyEx(depth_mask, cv2.MORPH_OPEN, kernel)
        
        return depth_mask
    
    def check_depth_consistency(self, fire_mask, depth_image):
        """检查深度一致性"""
        if np.sum(fire_mask) == 0:
            return fire_mask
        
        # 获取火焰区域的深度值
        fire_depths = depth_image[fire_mask > 0]
        fire_depths = fire_depths[fire_depths > 0]  # 过滤无效深度
        
        if len(fire_depths) == 0:
            return np.zeros_like(fire_mask)
        
        # 计算深度统计
        median_depth = np.median(fire_depths)
        depth_std = np.std(fire_depths)
        
        # 深度一致性检查
        if depth_std > self.depth_consistency_threshold:
            # 深度变化太大，可能是误检
            return np.zeros_like(fire_mask)
        
        return fire_mask
    
    def detect_fire_by_color(self, image):
        """增强的颜色火焰检测"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        fire_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
        
        # 检测多种火焰颜色
        for color_range in self.fire_color_ranges:
            mask = cv2.inRange(hsv, color_range['lower'], color_range['upper'])
            fire_mask = cv2.bitwise_or(fire_mask, mask)
        
        # 增强形态学操作
        kernel_open = np.ones((3, 3), np.uint8)
        kernel_close = np.ones((7, 7), np.uint8)
        fire_mask = cv2.morphologyEx(fire_mask, cv2.MORPH_OPEN, kernel_open)
        fire_mask = cv2.morphologyEx(fire_mask, cv2.MORPH_CLOSE, kernel_close)
        
        return fire_mask
    
    def detect_smoke_by_color(self, image):
        """增强的烟雾检测"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 烟雾颜色特征
        smoke_mask = cv2.inRange(hsv, self.smoke_color_range['lower'], self.smoke_color_range['upper'])
        
        # 增强纹理特征：烟雾区域纹理较弱
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        texture_mask = np.abs(laplacian) < 8  # 降低阈值，更敏感
        
        # 边缘密度特征：烟雾区域边缘较少
        edges = cv2.Canny(gray, 50, 150)
        edge_density = cv2.blur(edges.astype(np.float32), (15, 15))
        low_edge_mask = edge_density < 10
        
        # 结合多种特征
        combined_mask = smoke_mask.astype(bool) & texture_mask & low_edge_mask
        combined_mask = combined_mask.astype(np.uint8) * 255
        
        # 增强形态学操作
        kernel = np.ones((9, 9), np.uint8)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
        
        return combined_mask
    
    def detect_fire_motion(self, current_frame):
        """增强的火焰动态特征检测"""
        if len(self.frame_history) < 3:
            return np.zeros(current_frame.shape[:2], dtype=np.uint8)
        
        # 多帧差分
        motion_masks = []
        for i in range(-3, 0):
            if abs(i) <= len(self.frame_history):
                prev_frame = self.frame_history[i]
                frame_diff = cv2.absdiff(current_frame, prev_frame)
                gray_diff = cv2.cvtColor(frame_diff, cv2.COLOR_BGR2GRAY)
                _, motion_mask = cv2.threshold(gray_diff, self.motion_threshold, 255, cv2.THRESH_BINARY)
                motion_masks.append(motion_mask)
        
        # 融合多帧运动信息
        if motion_masks:
            combined_motion = np.zeros_like(motion_masks[0])
            for mask in motion_masks:
                combined_motion = cv2.bitwise_or(combined_motion, mask)
        else:
            combined_motion = np.zeros(current_frame.shape[:2], dtype=np.uint8)
        
        # 火焰闪烁检测
        flicker_mask = self.detect_flicker_pattern(current_frame)
        
        # 结合运动和闪烁特征
        dynamic_mask = cv2.bitwise_and(combined_motion, flicker_mask)
        
        return dynamic_mask
    
    def detect_flicker_pattern(self, current_frame):
        """增强的火焰闪烁模式检测"""
        if len(self.frame_history) < 5:
            return np.zeros(current_frame.shape[:2], dtype=np.uint8)
        
        # 计算亮度变化
        gray_current = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
        brightness_changes = []
        
        for i in range(-5, 0):
            if abs(i) <= len(self.frame_history):
                prev_gray = cv2.cvtColor(self.frame_history[i], cv2.COLOR_BGR2GRAY)
                diff = cv2.absdiff(gray_current, prev_gray)
                brightness_changes.append(diff)
        
        if not brightness_changes:
            return np.zeros(current_frame.shape[:2], dtype=np.uint8)
        
        # 计算亮度变化的标准差（闪烁特征）
        brightness_stack = np.stack(brightness_changes, axis=2)
        brightness_std = np.std(brightness_stack, axis=2)
        
        # 闪烁阈值化
        flicker_mask = (brightness_std > self.flicker_threshold * 255).astype(np.uint8) * 255
        
        # 形态学操作
        kernel = np.ones((5, 5), np.uint8)
        flicker_mask = cv2.morphologyEx(flicker_mask, cv2.MORPH_OPEN, kernel)
        
        return flicker_mask
    
    def analyze_shape_features(self, mask, detection_type='fire'):
        """增强的形状特征分析"""
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        detected_objects = []
        
        min_area = self.min_fire_area if detection_type == 'fire' else self.min_smoke_area
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue
            
            # 基础几何特征
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = float(w) / h if h > 0 else 0
            
            # 高级几何特征
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            solidity = area / hull_area if hull_area > 0 else 0
            
            perimeter = cv2.arcLength(contour, True)
            circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
            
            # 火焰特有的形状检查
            if detection_type == 'fire':
                # 火焰通常不规则，实心度较低
                if (self.fire_aspect_ratio_range[0] <= aspect_ratio <= self.fire_aspect_ratio_range[1] and
                    solidity >= self.fire_solidity_threshold):
                    
                    confidence = self.calculate_enhanced_fire_confidence(
                        area, aspect_ratio, circularity, solidity, w, h)
                    
                    detected_objects.append({
                        'bbox': (x, y, w, h),
                        'area': area,
                        'confidence': confidence,
                        'aspect_ratio': aspect_ratio,
                        'solidity': solidity,
                        'circularity': circularity,
                        'contour': contour
                    })
            else:  # smoke
                # 烟雾通常面积较大，形状不规则
                confidence = self.calculate_smoke_confidence(area, aspect_ratio, solidity)
                detected_objects.append({
                    'bbox': (x, y, w, h),
                    'area': area,
                    'confidence': confidence,
                    'aspect_ratio': aspect_ratio,
                    'solidity': solidity,
                    'contour': contour
                })
        
        return detected_objects
    
    def calculate_enhanced_fire_confidence(self, area, aspect_ratio, circularity, solidity, width, height):
        """计算增强的火焰检测置信度"""
        # 面积得分 (归一化)
        area_score = min(area / 2000.0, 1.0)
        
        # 形状不规则性得分 (火焰应该不规则)
        irregularity_score = 1.0 - circularity
        
        # 实心度得分 (火焰通常不完全实心)
        solidity_score = 1.0 - abs(solidity - 0.7)  # 最优实心度约0.7
        
        # 纵横比得分 (火焰通常偏高)
        optimal_ratio = 1.5  # 火焰的理想纵横比
        ratio_score = 1.0 - abs(aspect_ratio - optimal_ratio) / optimal_ratio
        ratio_score = max(0, ratio_score)
        
        # 尺寸合理性得分
        size_score = 1.0
        if width < 20 or height < 20:  # 太小
            size_score *= 0.5
        if width > 300 or height > 300:  # 太大
            size_score *= 0.7
        
        # 加权平均
        confidence = (area_score * 0.25 + irregularity_score * 0.25 + 
                     solidity_score * 0.2 + ratio_score * 0.2 + size_score * 0.1)
        
        return min(confidence, 1.0)
    
    def calculate_smoke_confidence(self, area, aspect_ratio, solidity):
        """计算烟雾检测置信度"""
        # 面积得分 (烟雾通常面积较大)
        area_score = min(area / 5000.0, 1.0)
        
        # 形状得分 (烟雾形状不规则)
        shape_score = 1.0 - abs(solidity - 0.5)  # 烟雾实心度约0.5
        
        # 纵横比得分 (烟雾可以是各种形状)
        ratio_score = 0.8  # 烟雾形状变化大，给固定分数
        
        # 加权平均
        confidence = area_score * 0.5 + shape_score * 0.3 + ratio_score * 0.2
        
        return min(confidence, 1.0)

    def multimodal_fire_detection(self, image, depth_image):
        """多模态火焰检测融合"""
        # 添加当前帧到历史
        self.frame_history.append(image.copy())
        if depth_image is not None:
            self.depth_history.append(depth_image.copy())

        # 各模态检测
        color_fire_mask = self.detect_fire_by_color(image)
        color_smoke_mask = self.detect_smoke_by_color(image)
        motion_mask = self.detect_fire_motion(image)

        # 深度约束火焰检测
        if self.depth_constrained_detection and depth_image is not None:
            depth_fire_mask = self.detect_fire_with_depth(image, depth_image)
        else:
            depth_fire_mask = color_fire_mask

        # 多模态融合
        # 火焰检测：颜色 + 运动 + 深度
        enhanced_fire_mask = self.fuse_detection_modalities(
            color_fire_mask, motion_mask, depth_fire_mask, 'fire')

        # 烟雾检测：主要基于颜色和纹理
        enhanced_smoke_mask = color_smoke_mask

        # 形状分析
        fire_objects = self.analyze_shape_features(enhanced_fire_mask, 'fire')
        smoke_objects = self.analyze_shape_features(enhanced_smoke_mask, 'smoke')

        # 添加检测类型标签
        for obj in fire_objects:
            obj['class_name'] = 'fire'
            obj['detection_method'] = 'multimodal_fusion'

        for obj in smoke_objects:
            obj['class_name'] = 'smoke'
            obj['detection_method'] = 'color_texture'

        return fire_objects, smoke_objects

    def fuse_detection_modalities(self, color_mask, motion_mask, depth_mask, detection_type):
        """融合多种检测模态"""
        if detection_type == 'fire':
            # 火焰检测的模态融合
            weights = self.fusion_weights

            # 加权融合
            fused_mask = np.zeros_like(color_mask, dtype=np.float32)
            fused_mask += color_mask.astype(np.float32) * weights['color']
            fused_mask += motion_mask.astype(np.float32) * weights['motion']
            fused_mask += depth_mask.astype(np.float32) * weights['depth']

            # 归一化并阈值化
            fused_mask = fused_mask / 255.0
            threshold = 0.6  # 融合阈值
            final_mask = (fused_mask > threshold).astype(np.uint8) * 255

            # 形态学后处理
            kernel = np.ones((5, 5), np.uint8)
            final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
            final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_CLOSE, kernel)

            return final_mask
        else:
            return color_mask

    def convert_to_3d_position(self, bbox, depth_image, camera_info):
        """将2D检测框转换为3D位置"""
        if camera_info is None or depth_image is None:
            return None

        x, y, w, h = bbox
        center_u = x + w // 2
        center_v = y + h // 2

        # 获取中心区域的深度值
        depth_region = depth_image[max(0, center_v-5):min(depth_image.shape[0], center_v+5),
                                  max(0, center_u-5):min(depth_image.shape[1], center_u+5)]

        # 过滤有效深度值
        valid_depths = depth_region[(depth_region > self.min_fire_depth) &
                                   (depth_region < self.max_fire_depth) &
                                   (~np.isnan(depth_region)) &
                                   (~np.isinf(depth_region))]

        if len(valid_depths) == 0:
            return None

        # 使用中位数深度
        depth = np.median(valid_depths)

        # 相机内参
        fx = camera_info.K[0]
        fy = camera_info.K[4]
        cx = camera_info.K[2]
        cy = camera_info.K[5]

        # 转换为3D坐标 (相机坐标系)
        x_3d = (center_u - cx) * depth / fx
        y_3d = (center_v - cy) * depth / fy
        z_3d = depth

        return np.array([x_3d, y_3d, z_3d])

    def track_fire_locations_3d(self, fire_objects, depth_image, camera_info):
        """跟踪3D火焰位置"""
        current_fire_locations = []

        for obj in fire_objects:
            # 转换为3D位置
            pos_3d = self.convert_to_3d_position(obj['bbox'], depth_image, camera_info)
            if pos_3d is not None:
                fire_location = {
                    'position_3d': pos_3d,
                    'confidence': obj['confidence'],
                    'area': obj['area'],
                    'detection_time': rospy.Time.now(),
                    'bbox_2d': obj['bbox']
                }
                current_fire_locations.append(fire_location)

        # 更新火焰位置跟踪
        self.update_fire_tracking(current_fire_locations)

        return current_fire_locations

    def update_fire_tracking(self, current_detections):
        """更新火焰跟踪"""
        # 简单的最近邻跟踪
        updated_locations = []

        for current in current_detections:
            matched = False
            for i, existing in enumerate(self.fire_locations_3d):
                # 计算3D距离
                distance = np.linalg.norm(current['position_3d'] - existing['position_3d'])

                if distance < self.fire_tracking_threshold:
                    # 更新现有火焰位置
                    existing['position_3d'] = current['position_3d']
                    existing['confidence'] = max(existing['confidence'], current['confidence'])
                    existing['detection_time'] = current['detection_time']
                    updated_locations.append(existing)
                    matched = True
                    break

            if not matched:
                # 新的火焰位置
                current['fire_id'] = len(self.fire_locations_3d)
                updated_locations.append(current)

        # 移除过期的火焰位置
        current_time = rospy.Time.now()
        self.fire_locations_3d = [
            fire for fire in updated_locations
            if (current_time - fire['detection_time']).to_sec() < 10.0  # 10秒超时
        ]

    def estimate_fire_spread_risk(self, fire_locations):
        """估计火焰蔓延风险"""
        if len(fire_locations) < 2:
            return "LOW"

        # 计算火焰点之间的距离
        distances = []
        for i in range(len(fire_locations)):
            for j in range(i+1, len(fire_locations)):
                dist = np.linalg.norm(
                    fire_locations[i]['position_3d'] - fire_locations[j]['position_3d'])
                distances.append(dist)

        if not distances:
            return "LOW"

        min_distance = min(distances)
        avg_distance = np.mean(distances)

        # 风险评估
        if min_distance < 1.0:  # 火焰点很近
            return "HIGH"
        elif min_distance < 2.0 or avg_distance < 3.0:
            return "MEDIUM"
        else:
            return "LOW"


class EnhancedFireDetectionNode:
    """增强火焰检测节点主类"""

    def __init__(self):
        rospy.init_node('enhanced_fire_detection_node', anonymous=True)

        # 初始化组件
        self.bridge = CvBridge()
        self.fire_detector = EnhancedFireDetector()
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)

        # 参数配置
        self.processing_rate = rospy.get_param('~processing_rate', 8.0)  # 8Hz，平衡精度和性能
        self.confidence_threshold = rospy.get_param('~confidence_threshold', 0.65)  # 提高阈值
        self.enable_visualization = rospy.get_param('~enable_visualization', True)
        self.enable_emergency_mode = rospy.get_param('~enable_emergency_mode', True)
        self.enable_3d_tracking = rospy.get_param('~enable_3d_tracking', True)

        # 应急模式参数
        self.fire_alert_threshold = rospy.get_param('~fire_alert_threshold', 0.85)  # 提高阈值
        self.consecutive_detection_threshold = rospy.get_param('~consecutive_detection_threshold', 2)  # 降低阈值，更快响应
        self.emergency_distance_threshold = rospy.get_param('~emergency_distance_threshold', 3.0)  # 3米内为紧急

        # 数据缓存
        self.current_image = None
        self.current_depth = None
        self.camera_info = None
        self.processing_lock = threading.Lock()

        # 应急状态跟踪
        self.fire_detection_count = 0
        self.emergency_state = False
        self.emergency_level = "NORMAL"  # NORMAL, WARNING, EMERGENCY, CRITICAL

        # 订阅器
        self.image_sub = rospy.Subscriber('image_raw', Image, self.image_callback, queue_size=1)
        self.depth_sub = rospy.Subscriber('depth_image', Image, self.depth_callback, queue_size=1)
        self.camera_info_sub = rospy.Subscriber('camera_info', CameraInfo, self.camera_info_callback, queue_size=1)

        # 发布器
        self.fire_detection_pub = rospy.Publisher('enhanced_fire_detections', SemanticObject, queue_size=10)
        self.emergency_alert_pub = rospy.Publisher('emergency_alert', Bool, queue_size=10)
        self.fire_locations_3d_pub = rospy.Publisher('fire_locations_3d', MarkerArray, queue_size=10)
        self.detection_visualization_pub = rospy.Publisher('enhanced_fire_visualization', Image, queue_size=10)
        self.fire_risk_pub = rospy.Publisher('fire_spread_risk', Header, queue_size=10)  # 使用Header传递风险等级

        rospy.loginfo("🔥 增强火焰检测节点初始化完成")
        rospy.loginfo(f"   处理频率: {self.processing_rate} Hz")
        rospy.loginfo(f"   置信度阈值: {self.confidence_threshold}")
        rospy.loginfo(f"   3D跟踪: {'启用' if self.enable_3d_tracking else '禁用'}")

        # 启动处理线程
        self.processing_thread = threading.Thread(target=self.processing_loop)
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def image_callback(self, msg):
        """图像回调"""
        try:
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            with self.processing_lock:
                self.current_image = {
                    'image': cv_image,
                    'header': msg.header,
                    'timestamp': time.time()
                }
        except Exception as e:
            rospy.logerr(f"图像转换失败: {e}")

    def depth_callback(self, msg):
        """深度图像回调"""
        try:
            depth_image = self.bridge.imgmsg_to_cv2(msg, desired_encoding="passthrough")
            with self.processing_lock:
                self.current_depth = {
                    'image': depth_image,
                    'header': msg.header,
                    'timestamp': time.time()
                }
        except Exception as e:
            rospy.logerr(f"深度图像转换失败: {e}")

    def camera_info_callback(self, msg):
        """相机信息回调"""
        self.camera_info = msg

    def processing_loop(self):
        """处理主循环"""
        rate = rospy.Rate(self.processing_rate)

        while not rospy.is_shutdown():
            try:
                with self.processing_lock:
                    if self.current_image is None:
                        rate.sleep()
                        continue

                    image_data = self.current_image.copy()
                    depth_data = self.current_depth.copy() if self.current_depth else None

                # 执行增强火灾检测
                self.process_enhanced_fire_detection(image_data, depth_data)

                rate.sleep()

            except Exception as e:
                rospy.logerr(f"处理循环错误: {e}")
                rate.sleep()

    def process_enhanced_fire_detection(self, image_data, depth_data):
        """处理增强火灾检测"""
        try:
            cv_image = image_data['image']
            header = image_data['header']

            # 获取深度图像
            depth_image = depth_data['image'] if depth_data else None

            # 执行多模态火灾检测
            fire_objects, smoke_objects = self.fire_detector.multimodal_fire_detection(cv_image, depth_image)

            # 过滤低置信度检测
            fire_objects = [obj for obj in fire_objects if obj['confidence'] >= self.confidence_threshold]
            smoke_objects = [obj for obj in smoke_objects if obj['confidence'] >= self.confidence_threshold]

            # 3D位置跟踪
            if self.enable_3d_tracking and depth_image is not None:
                fire_locations_3d = self.fire_detector.track_fire_locations_3d(
                    fire_objects, depth_image, self.camera_info)

                # 发布3D火焰位置
                self.publish_fire_locations_3d(fire_locations_3d, header)

                # 评估火焰蔓延风险
                risk_level = self.fire_detector.estimate_fire_spread_risk(fire_locations_3d)
                self.publish_fire_risk(risk_level, header)

            # 应急状态评估
            if self.enable_emergency_mode:
                self.evaluate_enhanced_emergency_state(fire_objects, smoke_objects)

            # 发布检测结果
            self.publish_enhanced_detections(fire_objects + smoke_objects, header)

            # 发布可视化
            if self.enable_visualization:
                self.publish_enhanced_visualization(cv_image, fire_objects, smoke_objects, header)

            # 日志输出
            if fire_objects or smoke_objects:
                rospy.loginfo(f"🔥 检测结果: {len(fire_objects)} 个火焰, {len(smoke_objects)} 个烟雾")

        except Exception as e:
            rospy.logerr(f"增强火灾检测处理失败: {e}")

    def evaluate_enhanced_emergency_state(self, fire_objects, smoke_objects):
        """评估增强应急状态"""
        try:
            # 检查是否有高置信度的火灾检测
            high_confidence_fires = [obj for obj in fire_objects
                                   if obj['confidence'] >= self.fire_alert_threshold]

            # 检查火焰距离
            close_fires = []
            if self.enable_3d_tracking and self.fire_detector.fire_locations_3d:
                for fire_loc in self.fire_detector.fire_locations_3d:
                    distance = np.linalg.norm(fire_loc['position_3d'])  # 距离相机的距离
                    if distance < self.emergency_distance_threshold:
                        close_fires.append(fire_loc)

            # 更新检测计数
            if high_confidence_fires or close_fires:
                self.fire_detection_count += 1
            else:
                self.fire_detection_count = max(0, self.fire_detection_count - 1)

            # 确定应急等级
            new_emergency_level = self.determine_emergency_level(
                high_confidence_fires, close_fires, smoke_objects)

            # 连续检测到火灾时触发应急状态
            new_emergency_state = (self.fire_detection_count >= self.consecutive_detection_threshold)

            # 状态变化处理
            if new_emergency_state != self.emergency_state or new_emergency_level != self.emergency_level:
                self.emergency_state = new_emergency_state
                self.emergency_level = new_emergency_level

                self.emergency_alert_pub.publish(Bool(data=self.emergency_state))

                if self.emergency_state:
                    rospy.logwarn(f"🚨 应急状态激活：{self.emergency_level} - 检测到火灾！")
                    if close_fires:
                        rospy.logwarn(f"⚠️ 危险：{len(close_fires)} 个火焰点距离过近！")
                else:
                    rospy.loginfo("✅ 应急状态解除")

        except Exception as e:
            rospy.logerr(f"应急状态评估失败: {e}")

    def determine_emergency_level(self, high_confidence_fires, close_fires, smoke_objects):
        """确定应急等级"""
        if close_fires and len(close_fires) >= 2:
            return "CRITICAL"  # 多个近距离火焰点
        elif close_fires:
            return "EMERGENCY"  # 单个近距离火焰点
        elif high_confidence_fires and len(high_confidence_fires) >= 2:
            return "WARNING"  # 多个高置信度火焰
        elif high_confidence_fires or len(smoke_objects) >= 3:
            return "WARNING"  # 单个火焰或多个烟雾
        else:
            return "NORMAL"

    def publish_enhanced_detections(self, detected_objects, header):
        """发布增强检测结果"""
        try:
            for obj in detected_objects:
                # 创建语义对象消息
                semantic_obj = SemanticObject()
                semantic_obj.header = header
                semantic_obj.class_name = obj['class_name']
                semantic_obj.confidence = obj['confidence']

                # 设置边界框
                x, y, w, h = obj['bbox']
                semantic_obj.bbox_x = x
                semantic_obj.bbox_y = y
                semantic_obj.bbox_width = w
                semantic_obj.bbox_height = h

                # 添加额外信息
                semantic_obj.area = obj['area']

                self.fire_detection_pub.publish(semantic_obj)

        except Exception as e:
            rospy.logerr(f"发布检测结果失败: {e}")

    def publish_fire_locations_3d(self, fire_locations, header):
        """发布3D火焰位置"""
        try:
            marker_array = MarkerArray()

            for i, fire_loc in enumerate(fire_locations):
                # 火焰位置标记
                marker = Marker()
                marker.header = header
                marker.header.frame_id = "zed_left_camera_optical_frame"  # 相机坐标系
                marker.ns = "fire_locations_3d"
                marker.id = i
                marker.type = Marker.SPHERE
                marker.action = Marker.ADD

                # 位置
                marker.pose.position.x = fire_loc['position_3d'][0]
                marker.pose.position.y = fire_loc['position_3d'][1]
                marker.pose.position.z = fire_loc['position_3d'][2]
                marker.pose.orientation.w = 1.0

                # 尺寸（根据置信度调整）
                size = 0.2 + fire_loc['confidence'] * 0.3
                marker.scale.x = size
                marker.scale.y = size
                marker.scale.z = size

                # 颜色（红色，透明度根据置信度）
                marker.color.r = 1.0
                marker.color.g = 0.0
                marker.color.b = 0.0
                marker.color.a = 0.7 + fire_loc['confidence'] * 0.3

                marker.lifetime = rospy.Duration(2.0)
                marker_array.markers.append(marker)

                # 危险区域标记
                danger_marker = Marker()
                danger_marker.header = header
                danger_marker.header.frame_id = "zed_left_camera_optical_frame"
                danger_marker.ns = "danger_zones"
                danger_marker.id = i + 1000
                danger_marker.type = Marker.SPHERE
                danger_marker.action = Marker.ADD

                danger_marker.pose.position.x = fire_loc['position_3d'][0]
                danger_marker.pose.position.y = fire_loc['position_3d'][1]
                danger_marker.pose.position.z = fire_loc['position_3d'][2]
                danger_marker.pose.orientation.w = 1.0

                # 危险区域半径
                danger_radius = 1.5  # 1.5米危险半径
                danger_marker.scale.x = danger_radius * 2
                danger_marker.scale.y = danger_radius * 2
                danger_marker.scale.z = danger_radius * 2

                # 半透明红色
                danger_marker.color.r = 1.0
                danger_marker.color.g = 0.0
                danger_marker.color.b = 0.0
                danger_marker.color.a = 0.2

                danger_marker.lifetime = rospy.Duration(2.0)
                marker_array.markers.append(danger_marker)

            self.fire_locations_3d_pub.publish(marker_array)

        except Exception as e:
            rospy.logerr(f"发布3D火焰位置失败: {e}")

    def publish_fire_risk(self, risk_level, header):
        """发布火焰蔓延风险"""
        try:
            risk_msg = Header()
            risk_msg.stamp = header.stamp
            risk_msg.frame_id = risk_level  # 使用frame_id传递风险等级
            self.fire_risk_pub.publish(risk_msg)

        except Exception as e:
            rospy.logerr(f"发布火焰风险失败: {e}")

    def publish_enhanced_visualization(self, image, fire_objects, smoke_objects, header):
        """发布增强可视化"""
        try:
            vis_image = image.copy()

            # 绘制火焰检测
            for obj in fire_objects:
                x, y, w, h = obj['bbox']
                confidence = obj['confidence']

                # 绘制边界框（红色）
                color = (0, 0, 255)  # BGR格式
                thickness = 2
                cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, thickness)

                # 绘制标签
                label = f"Fire: {confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(vis_image, (x, y - label_size[1] - 10),
                             (x + label_size[0], y), color, -1)
                cv2.putText(vis_image, label, (x, y - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 绘制烟雾检测
            for obj in smoke_objects:
                x, y, w, h = obj['bbox']
                confidence = obj['confidence']

                # 绘制边界框（灰色）
                color = (128, 128, 128)  # BGR格式
                thickness = 2
                cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, thickness)

                # 绘制标签
                label = f"Smoke: {confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(vis_image, (x, y - label_size[1] - 10),
                             (x + label_size[0], y), color, -1)
                cv2.putText(vis_image, label, (x, y - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 添加状态信息
            status_text = f"Emergency: {self.emergency_level} | Fires: {len(fire_objects)} | Smoke: {len(smoke_objects)}"
            cv2.putText(vis_image, status_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # 发布可视化图像
            vis_msg = self.bridge.cv2_to_imgmsg(vis_image, "bgr8")
            vis_msg.header = header
            self.detection_visualization_pub.publish(vis_msg)

        except Exception as e:
            rospy.logerr(f"发布可视化失败: {e}")


if __name__ == '__main__':
    try:
        node = EnhancedFireDetectionNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
