# 语义导航避障系统使用指南

## 系统概述

语义导航避障系统是基于RTAB-Map TSDF建图和语义检测的智能导航解决方案，专为火灾应急场景设计。系统集成了语义感知、路径规划、避障控制和应急响应功能。

### 主要特性

- 🗺️ **语义路径规划**: 基于语义地图的A*路径规划算法
- 🚗 **智能避障**: 动态窗口法结合语义感知的实时避障
- 🔥 **火焰安全**: 自动检测火焰并保持安全距离
- 🚨 **应急响应**: 快速应急停止和恢复机制
- 📊 **实时监控**: 完整的系统状态监控和可视化

## 系统架构

```
语义导航系统
├── 语义代价地图转换器 (semantic_costmap_converter.py)
├── 语义路径规划器 (semantic_path_planner.py)
├── 语义避障控制器 (semantic_obstacle_avoidance.py)
├── 语义导航控制器 (semantic_navigation_controller.py)
└── 工具集
    ├── 导航目标设置 (set_navigation_goal.py)
    ├── 应急停止控制 (emergency_stop.py)
    ├── 系统监控 (monitor_navigation.py)
    └── 功能测试 (test_semantic_navigation.py)
```

## 快速开始

### 1. 前置条件

确保以下系统正在运行：
- RTAB-Map TSDF建图系统 (`./stage_2_rtab_tsdf.sh`)
- 语义检测系统 (`./quick_start_enhanced_semantic.sh`)
- 火焰检测系统 (`python3 real_object_detector.py`)
- 语义点云发布器 (`python3 real_semantic_pointcloud_publisher.py`)

### 2. 启动语义导航系统

```bash
# 启动完整的语义导航系统
./quick_start_semantic_navigation.sh
```

这将自动启动以下组件：
- 语义代价地图转换器
- 语义路径规划器
- 语义避障控制器
- 语义导航控制器
- RViz可视化界面

### 3. 设置导航目标

```bash
# 基本用法
python3 set_navigation_goal.py 5.0 3.0

# 应急模式
python3 set_navigation_goal.py 2.0 1.0 --emergency

# 带朝向角度
python3 set_navigation_goal.py -1.0 -2.0 --yaw 1.57
```

### 4. 应急控制

```bash
# 应急停止
python3 emergency_stop.py stop

# 恢复导航
python3 emergency_stop.py resume

# 带原因说明
python3 emergency_stop.py stop -r "检测到火焰威胁"
```

### 5. 系统监控

```bash
# 实时监控
python3 monitor_navigation.py

# 交互式监控
python3 monitor_navigation.py interactive
```

## 详细使用说明

### 语义代价地图

系统自动将语义点云转换为导航代价地图，不同语义标签具有不同的代价权重：

- **火焰区域**: 代价1000 (禁入)
- **烟雾区域**: 代价100 (高代价)
- **墙壁**: 代价1000 (禁入)
- **桌子**: 代价80 (需绕行)
- **椅子**: 代价60 (可推开)
- **人员**: 代价70 (需避让)
- **门口**: 代价10 (优先通过)
- **走廊**: 代价5 (优先通过)

### 路径规划

语义路径规划器使用A*算法，考虑以下因素：
- 语义代价权重
- 火焰安全距离 (默认2米)
- 障碍物膨胀
- 路径平滑

### 避障控制

动态窗口法避障算法特性：
- 实时障碍物检测
- 语义感知避障策略
- 火焰威胁自动规避
- 平滑运动控制

### 应急响应

系统具备完整的应急响应机制：
- 自动火焰威胁检测
- 即时应急停止
- 安全状态保持
- 威胁解除后自动恢复

## RViz可视化

在RViz中可以查看：
- 语义点云地图
- 火焰位置标记
- 规划路径
- 语义代价地图
- 机器人位姿
- 导航目标
- 安全区域

### 推荐显示配置

1. **PointCloud2**: `/semantic_mapping/enhanced_pointcloud`
2. **MarkerArray**: `/semantic_perception/fire_locations_3d`
3. **Path**: `/planned_path`
4. **Map**: `/semantic_costmap`
5. **PoseStamped**: `/pose_center/odom`

## 服务接口

### 设置导航目标
```bash
rosservice call /set_navigation_goal "goal: {header: {frame_id: 'map'}, pose: {position: {x: 5.0, y: 3.0, z: 0.0}, orientation: {w: 1.0}}}"
```

### 应急停止
```bash
# 停止
rosservice call /emergency_stop_service "stop: true, reason: '手动停止'"

# 恢复
rosservice call /emergency_stop_service "stop: false, reason: '手动恢复'"
```

### 路径规划
```bash
rosservice call /plan_semantic_path "start_pose: {...}, goal_pose: {...}, emergency_mode: false"
```

## 话题监控

### 重要话题

- `/navigation_state`: 导航状态
- `/planned_path`: 规划路径
- `/cmd_vel`: 速度命令
- `/emergency_stop`: 应急停止信号
- `/navigation_status`: 详细导航状态
- `/emergency_alert`: 应急警报

### 监控命令

```bash
# 查看导航状态
rostopic echo /navigation_state

# 查看规划路径
rostopic echo /planned_path

# 查看速度命令
rostopic echo /cmd_vel

# 查看应急警报
rostopic echo /emergency_alert
```

## 参数配置

主要参数在 `src/semantic_navigation/config/semantic_navigation_params.yaml` 中配置：

### 安全参数
```yaml
fire_safety_distance: 2.0          # 火焰安全距离
emergency_stop_distance: 1.0       # 应急停止距离
max_navigation_time: 300.0          # 最大导航时间
```

### 性能参数
```yaml
max_speed: 1.0                      # 最大速度
max_angular_speed: 1.0              # 最大角速度
goal_tolerance: 0.5                 # 目标容忍度
```

### 语义代价权重
```yaml
semantic_costs:
  fire: 1000.0
  smoke: 100.0
  person: 50.0
  # ... 其他权重
```

## 故障排除

### 常见问题

1. **路径规划失败**
   - 检查目标位置是否在地图范围内
   - 确认目标位置不在火焰安全区域内
   - 验证语义地图数据是否正常

2. **机器人不移动**
   - 检查应急停止状态
   - 确认速度命令发布正常
   - 验证避障系统是否检测到障碍物

3. **火焰检测异常**
   - 确认火焰检测节点正在运行
   - 检查语义检测系统状态
   - 验证相机数据输入

### 调试命令

```bash
# 检查节点状态
rosnode list | grep semantic

# 检查话题状态
rostopic list | grep navigation

# 查看节点日志
rosnode info /semantic_navigation_controller

# 检查服务状态
rosservice list | grep navigation
```

## 性能优化

### 建议配置

1. **硬件要求**
   - GPU: RTX 4090 (已配置)
   - 内存: 16GB+
   - CPU: 8核+

2. **系统优化**
   - 使用VirtualGL加速
   - 启用GPU渲染
   - 优化ROS网络配置

3. **参数调优**
   - 根据环境调整语义代价权重
   - 优化路径规划频率
   - 调整避障参数

## 扩展功能

系统支持以下扩展：
- 多机器人协同导航
- 动态语义地图更新
- 自定义语义标签
- 高级路径优化算法
- 机器学习避障策略

## 技术支持

如遇问题，请：
1. 查看系统日志文件 (`logs/semantic_navigation/`)
2. 使用监控工具诊断系统状态
3. 检查ROS网络连接
4. 验证依赖系统运行状态

---

**版本**: 2.1  
**更新日期**: 2024年  
**作者**: 语义SLAM增强项目团队
