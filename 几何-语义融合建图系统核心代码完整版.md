# 几何-语义融合建图系统核心代码

## 1. 语义分割网络模块 (enhanced_semantic_segmentation_node.py)

```python
#!/usr/bin/env python3
import rospy
import cv2
import numpy as np
import torch
import torch.nn.functional as F
from cv_bridge import CvBridge
from sensor_msgs.msg import Image, CameraInfo
from semantic_perception.msg import SemanticSegmentation
import torchvision.transforms as transforms

try:
    from torchvision.models.segmentation import deeplabv3_resnet50, fcn_resnet50
    TORCHVISION_AVAILABLE = True
except ImportError:
    TORCHVISION_AVAILABLE = False
    rospy.logwarn("torchvision不可用，将使用简化模式")

try:
    import segmentation_models_pytorch as smp
    SMP_AVAILABLE = True
except ImportError:
    SMP_AVAILABLE = False

class MultiModelEnsemble:
    """多模型集成系统"""
    
    def __init__(self, device):
        self.device = device
        self.models = {}
        self.model_weights = {}
        self.load_models()
        
    def load_models(self):
        """加载多个语义分割模型"""
        try:
            if TORCHVISION_AVAILABLE:
                # DeepLabV3 ResNet50
                self.models['deeplabv3'] = deeplabv3_resnet50(pretrained=True)
                self.models['deeplabv3'].eval()
                self.models['deeplabv3'].to(self.device)
                self.model_weights['deeplabv3'] = 0.6
                
                # FCN ResNet50 (作为备用模型)
                self.models['fcn'] = fcn_resnet50(pretrained=True)
                self.models['fcn'].eval()
                self.models['fcn'].to(self.device)
                self.model_weights['fcn'] = 0.4
                
                rospy.loginfo("成功加载多个语义分割模型")
            else:
                rospy.logwarn("torchvision不可用，使用简化模型")
                
        except Exception as e:
            rospy.logerr(f"模型加载失败: {e}")
    
    def ensemble_predict(self, input_tensor):
        """集成预测"""
        if not self.models:
            return None
            
        ensemble_output = None
        total_weight = 0.0
        
        for model_name, model in self.models.items():
            try:
                with torch.no_grad():
                    output = model(input_tensor)
                    if isinstance(output, dict):
                        output = output['out']
                    
                    output = F.softmax(output, dim=1)
                    weight = self.model_weights.get(model_name, 1.0)
                    
                    if ensemble_output is None:
                        ensemble_output = output * weight
                    else:
                        ensemble_output += output * weight
                    
                    total_weight += weight
                    
            except Exception as e:
                rospy.logwarn(f"模型 {model_name} 预测失败: {e}")
                continue
        
        if ensemble_output is not None and total_weight > 0:
            ensemble_output /= total_weight
            
        return ensemble_output

class EnhancedSemanticSegmentationNode:
    """增强语义分割节点"""
    
    def __init__(self):
        rospy.init_node('enhanced_semantic_segmentation_node', anonymous=True)
        
        self.bridge = CvBridge()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 参数配置
        self.enable_ensemble = rospy.get_param('~enable_ensemble', True)
        self.confidence_threshold = rospy.get_param('~confidence_threshold', 0.7)
        self.enable_temporal_smoothing = rospy.get_param('~enable_temporal_smoothing', True)
        self.temporal_window_size = rospy.get_param('~temporal_window_size', 5)
        
        # 语义类别配置
        self.class_names = [
            'background', 'aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus',
            'car', 'cat', 'chair', 'cow', 'diningtable', 'dog', 'horse',
            'motorbike', 'person', 'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor'
        ]
        
        self.class_colors = self.generate_class_colors()
        
        # 模型初始化
        self.model_ensemble = MultiModelEnsemble(self.device)
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((480, 640)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # 时序平滑
        self.segmentation_history = []
        self.confidence_history = []
        
        # ROS通信
        self.setup_ros_communication()
        
        rospy.loginfo("增强语义分割节点初始化完成")
    
    def setup_ros_communication(self):
        """设置ROS通信"""
        # 订阅器
        self.image_sub = rospy.Subscriber(
            '/camera/color/image_raw', 
            Image, 
            self.image_callback, 
            queue_size=1
        )
        
        self.camera_info_sub = rospy.Subscriber(
            '/camera/color/camera_info',
            CameraInfo,
            self.camera_info_callback,
            queue_size=1
        )
        
        # 发布器
        self.seg_pub = rospy.Publisher(
            '/semantic_perception/segmentation_result',
            SemanticSegmentation,
            queue_size=1
        )
        
        self.vis_pub = rospy.Publisher(
            '/semantic_perception/segmentation_visualization',
            Image,
            queue_size=1
        )
        
        self.confidence_pub = rospy.Publisher(
            '/semantic_perception/confidence_map',
            Image,
            queue_size=1
        )
    
    def generate_class_colors(self):
        """生成类别颜色映射"""
        colors = []
        for i in range(len(self.class_names)):
            # 使用HSV色彩空间生成不同颜色
            hue = (i * 137.5) % 360  # 黄金角度分割
            saturation = 0.8
            value = 0.9
            
            # 转换为RGB
            c = value * saturation
            x = c * (1 - abs((hue / 60) % 2 - 1))
            m = value - c
            
            if 0 <= hue < 60:
                r, g, b = c, x, 0
            elif 60 <= hue < 120:
                r, g, b = x, c, 0
            elif 120 <= hue < 180:
                r, g, b = 0, c, x
            elif 180 <= hue < 240:
                r, g, b = 0, x, c
            elif 240 <= hue < 300:
                r, g, b = x, 0, c
            else:
                r, g, b = c, 0, x
            
            colors.append([
                int((r + m) * 255),
                int((g + m) * 255),
                int((b + m) * 255)
            ])
        
        return colors
    
    def camera_info_callback(self, msg):
        """相机信息回调"""
        self.camera_info = msg
    
    def image_callback(self, msg):
        """图像回调函数"""
        try:
            start_time = rospy.Time.now()
            
            # 转换图像
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            
            # 执行语义分割
            segmentation_mask, confidence_map = self.perform_segmentation(cv_image)
            
            if segmentation_mask is not None:
                # 时序平滑
                if self.enable_temporal_smoothing:
                    segmentation_mask, confidence_map = self.apply_temporal_smoothing(
                        segmentation_mask, confidence_map
                    )
                
                # 创建语义分割消息
                seg_msg = self.create_segmentation_message(
                    msg.header, cv_image, segmentation_mask, confidence_map
                )
                
                # 发布结果
                self.seg_pub.publish(seg_msg)
                
                # 发布可视化
                self.publish_visualization(msg.header, cv_image, segmentation_mask, confidence_map)
                
                processing_time = (rospy.Time.now() - start_time).to_sec()
                rospy.logdebug(f"语义分割处理时间: {processing_time:.3f}秒")
            
        except Exception as e:
            rospy.logerr(f"图像处理异常: {e}")
    
    def perform_segmentation(self, cv_image):
        """执行语义分割"""
        try:
            # 预处理
            image_rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
            input_tensor = self.transform(image_rgb).unsqueeze(0).to(self.device)

            # 模型推理
            if self.enable_ensemble and self.model_ensemble.models:
                output = self.model_ensemble.ensemble_predict(input_tensor)
            else:
                # 使用单一模型
                if 'deeplabv3' in self.model_ensemble.models:
                    with torch.no_grad():
                        output = self.model_ensemble.models['deeplabv3'](input_tensor)
                        if isinstance(output, dict):
                            output = output['out']
                        output = F.softmax(output, dim=1)
                else:
                    # 简化模式：返回空结果
                    return None, None

            if output is None:
                return None, None

            # 后处理
            segmentation_mask, confidence_map = self.postprocess_output(
                output, cv_image.shape[:2]
            )
            
            return segmentation_mask, confidence_map
            
        except Exception as e:
            rospy.logerr(f"语义分割执行失败: {e}")
            return None, None
    
    def postprocess_output(self, output, target_shape):
        """后处理输出"""
        # 获取预测结果
        predictions = torch.argmax(output, dim=1).squeeze(0)
        confidences = torch.max(output, dim=1)[0].squeeze(0)
        
        # 转换为numpy
        predictions_np = predictions.cpu().numpy().astype(np.uint8)
        confidences_np = confidences.cpu().numpy().astype(np.float32)
        
        # 调整尺寸到原始图像大小
        segmentation_mask = cv2.resize(
            predictions_np, 
            (target_shape[1], target_shape[0]), 
            interpolation=cv2.INTER_NEAREST
        )
        
        confidence_map = cv2.resize(
            confidences_np,
            (target_shape[1], target_shape[0]),
            interpolation=cv2.INTER_LINEAR
        )
        
        # 应用置信度阈值
        low_confidence_mask = confidence_map < self.confidence_threshold
        segmentation_mask[low_confidence_mask] = 0  # 设为背景类
        
        return segmentation_mask, confidence_map
    
    def apply_temporal_smoothing(self, segmentation_mask, confidence_map):
        """应用时序平滑"""
        # 添加到历史记录
        self.segmentation_history.append(segmentation_mask.copy())
        self.confidence_history.append(confidence_map.copy())
        
        # 保持窗口大小
        if len(self.segmentation_history) > self.temporal_window_size:
            self.segmentation_history.pop(0)
            self.confidence_history.pop(0)
        
        if len(self.segmentation_history) < 2:
            return segmentation_mask, confidence_map
        
        # 加权平均
        smoothed_segmentation = np.zeros_like(segmentation_mask, dtype=np.float32)
        smoothed_confidence = np.zeros_like(confidence_map, dtype=np.float32)
        total_weight = 0.0
        
        for i, (seg, conf) in enumerate(zip(self.segmentation_history, self.confidence_history)):
            # 时间权重：越新的帧权重越大
            weight = (i + 1) / len(self.segmentation_history)
            
            # 置信度权重
            weight *= np.mean(conf)
            
            smoothed_segmentation += seg.astype(np.float32) * weight
            smoothed_confidence += conf * weight
            total_weight += weight
        
        if total_weight > 0:
            smoothed_segmentation /= total_weight
            smoothed_confidence /= total_weight
        
        # 转换回整数类型
        smoothed_segmentation = np.round(smoothed_segmentation).astype(np.uint8)
        
        return smoothed_segmentation, smoothed_confidence

if __name__ == '__main__':
    try:
        node = EnhancedSemanticSegmentationNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```

## 2. 几何约束优化模块 (geometry_semantic_fusion_node.py)

```python
#!/usr/bin/env python3
import rospy
import numpy as np
import cv2
from cv_bridge import CvBridge
from sensor_msgs.msg import Image, PointCloud2, CameraInfo
from semantic_perception.msg import SemanticSegmentation
from semantic_mapping.msg import EnhancedSemanticSegmentation
import message_filters
from geometry_msgs.msg import Point
import sensor_msgs.point_cloud2 as pc2
from sklearn.cluster import DBSCAN
import open3d as o3d

class GeometrySemanticFusionNode:
    """几何-语义融合节点"""

    def __init__(self):
        rospy.init_node('geometry_semantic_fusion_node', anonymous=True)

        self.bridge = CvBridge()

        # 参数配置
        self.geometric_constraint_weight = rospy.get_param('~geometric_constraint_weight', 0.3)
        self.temporal_smoothing_weight = rospy.get_param('~temporal_smoothing_weight', 0.2)
        self.depth_consistency_threshold = rospy.get_param('~depth_consistency_threshold', 0.1)
        self.normal_consistency_threshold = rospy.get_param('~normal_consistency_threshold', 0.2)
        self.enable_temporal_smoothing = rospy.get_param('~enable_temporal_smoothing', True)
        self.enable_surface_smoothing = rospy.get_param('~enable_surface_smoothing', True)

        # 历史数据存储
        self.semantic_history = []
        self.geometric_history = []
        self.history_size = 5

        # 相机参数
        self.camera_info = None

        # 设置ROS通信
        self.setup_ros_communication()

        rospy.loginfo("几何-语义融合节点初始化完成")

    def setup_ros_communication(self):
        """设置ROS通信"""
        # 同步订阅器
        self.tsdf_pointcloud_sub = message_filters.Subscriber(
            '/tsdf_mapping/pointcloud', PointCloud2
        )
        self.semantic_seg_sub = message_filters.Subscriber(
            '/semantic_perception/segmentation_result', SemanticSegmentation
        )
        self.camera_info_sub = message_filters.Subscriber(
            '/camera/color/camera_info', CameraInfo
        )

        # 时间同步器
        self.sync = message_filters.ApproximateTimeSynchronizer(
            [self.tsdf_pointcloud_sub, self.semantic_seg_sub, self.camera_info_sub],
            queue_size=10,
            slop=0.1
        )
        self.sync.registerCallback(self.fusion_callback)

        # 发布器
        self.enhanced_semantic_pub = rospy.Publisher(
            '/semantic_mapping/enhanced_semantic_segmentation',
            EnhancedSemanticSegmentation,
            queue_size=1
        )

        self.fusion_visualization_pub = rospy.Publisher(
            '/semantic_mapping/fusion_visualization',
            Image,
            queue_size=1
        )

        self.geometric_features_pub = rospy.Publisher(
            '/semantic_mapping/geometric_features',
            PointCloud2,
            queue_size=1
        )

    def fusion_callback(self, tsdf_pointcloud, semantic_segmentation, camera_info):
        """融合回调函数"""
        try:
            start_time = rospy.Time.now()

            # 更新相机参数
            self.camera_info = camera_info

            # 执行几何-语义融合
            enhanced_semantic = self.perform_fusion(
                tsdf_pointcloud, semantic_segmentation, camera_info
            )

            if enhanced_semantic is not None:
                # 发布增强语义分割结果
                self.enhanced_semantic_pub.publish(enhanced_semantic)

                # 发布可视化
                self.publish_fusion_visualization(enhanced_semantic)

                processing_time = (rospy.Time.now() - start_time).to_sec()
                rospy.logdebug(f"几何-语义融合处理时间: {processing_time:.3f}秒")

        except Exception as e:
            rospy.logerr(f"几何-语义融合异常: {e}")

    def perform_fusion(self, tsdf_pointcloud, semantic_segmentation, camera_info):
        """执行几何-语义融合"""
        try:
            # 提取TSDF点云数据
            tsdf_points = self.extract_pointcloud_data(tsdf_pointcloud)
            if tsdf_points is None or len(tsdf_points) == 0:
                return None

            # 重构语义分割数据
            semantic_data = self.reconstruct_semantic_data(semantic_segmentation)
            if semantic_data is None:
                return None

            # 计算几何特征
            geometric_features = self.compute_geometric_features(tsdf_points)

            # 投影TSDF点到图像平面
            projected_points = self.project_points_to_image(tsdf_points, camera_info)

            # 几何约束的语义优化
            enhanced_semantic = self.apply_geometric_constraints(
                semantic_data, projected_points, geometric_features)

            # 时序平滑
            if self.enable_temporal_smoothing:
                enhanced_semantic = self.apply_temporal_smoothing(enhanced_semantic)

            # 表面平滑
            if self.enable_surface_smoothing:
                enhanced_semantic = self.apply_surface_smoothing(enhanced_semantic)

            # 创建增强语义分割消息
            enhanced_msg = self.create_enhanced_semantic_message(
                semantic_segmentation.header, enhanced_semantic, geometric_features
            )

            return enhanced_msg

        except Exception as e:
            rospy.logerr(f"几何-语义融合执行失败: {e}")
            return None

    def extract_pointcloud_data(self, pointcloud_msg):
        """提取点云数据"""
        try:
            points = []
            colors = []

            for point in pc2.read_points(pointcloud_msg,
                                       field_names=("x", "y", "z", "rgb"),
                                       skip_nans=True):
                points.append([point[0], point[1], point[2]])

                # 解析RGB颜色
                rgb = int(point[3])
                r = (rgb >> 16) & 0xFF
                g = (rgb >> 8) & 0xFF
                b = rgb & 0xFF
                colors.append([r, g, b])

            if len(points) == 0:
                return None

            return {
                'points': np.array(points, dtype=np.float32),
                'colors': np.array(colors, dtype=np.uint8)
            }

        except Exception as e:
            rospy.logerr(f"点云数据提取失败: {e}")
            return None

    def reconstruct_semantic_data(self, semantic_msg):
        """重构语义分割数据"""
        try:
            # 转换语义分割掩码
            semantic_mask = self.bridge.imgmsg_to_cv2(
                semantic_msg.segmentation_mask, "mono8"
            )

            # 转换置信度图
            confidence_map = self.bridge.imgmsg_to_cv2(
                semantic_msg.confidence_map, "32FC1"
            )

            return {
                'mask': semantic_mask,
                'confidence': confidence_map,
                'class_names': semantic_msg.class_names,
                'class_colors': semantic_msg.class_colors
            }

        except Exception as e:
            rospy.logerr(f"语义数据重构失败: {e}")
            return None

    def compute_geometric_features(self, tsdf_data):
        """计算几何特征"""
        try:
            points = tsdf_data['points']

            if len(points) < 10:
                return None

            # 使用Open3D计算几何特征
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)

            # 估计法向量
            pcd.estimate_normals(
                search_param=o3d.geometry.KDTreeSearchParamHybrid(
                    radius=0.1, max_nn=30
                )
            )

            normals = np.asarray(pcd.normals)

            # 计算曲率
            curvatures = self.compute_curvature(points, normals)

            # 计算深度梯度
            depth_gradients = self.compute_depth_gradients(points)

            return {
                'normals': normals,
                'curvatures': curvatures,
                'depth_gradients': depth_gradients,
                'points': points
            }

        except Exception as e:
            rospy.logerr(f"几何特征计算失败: {e}")
            return None

    def compute_curvature(self, points, normals):
        """计算曲率"""
        try:
            curvatures = np.zeros(len(points))

            # 使用KD树进行邻域搜索
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            pcd.normals = o3d.utility.Vector3dVector(normals)

            kdtree = o3d.geometry.KDTreeFlann(pcd)

            for i in range(len(points)):
                # 搜索邻域点
                [k, idx, _] = kdtree.search_radius_vector_3d(points[i], 0.05)

                if k > 3:
                    # 计算法向量变化
                    neighbor_normals = normals[idx[1:]]  # 排除自身
                    current_normal = normals[i]

                    # 计算法向量差异的平均值作为曲率估计
                    normal_diffs = np.linalg.norm(
                        neighbor_normals - current_normal, axis=1
                    )
                    curvatures[i] = np.mean(normal_diffs)

            return curvatures

        except Exception as e:
            rospy.logerr(f"曲率计算失败: {e}")
            return np.zeros(len(points))

    def compute_depth_gradients(self, points):
        """计算深度梯度"""
        try:
            # 计算每个点到原点的距离（深度）
            depths = np.linalg.norm(points, axis=1)

            # 使用邻域点计算梯度
            gradients = np.zeros(len(points))

            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            kdtree = o3d.geometry.KDTreeFlann(pcd)

            for i in range(len(points)):
                [k, idx, _] = kdtree.search_radius_vector_3d(points[i], 0.05)

                if k > 2:
                    neighbor_depths = depths[idx[1:]]  # 排除自身
                    current_depth = depths[i]

                    # 计算深度变化的标准差作为梯度估计
                    depth_diffs = np.abs(neighbor_depths - current_depth)
                    gradients[i] = np.std(depth_diffs)

            return gradients

        except Exception as e:
            rospy.logerr(f"深度梯度计算失败: {e}")
            return np.zeros(len(points))

    def project_points_to_image(self, tsdf_data, camera_info):
        """投影3D点到图像平面"""
        try:
            points_3d = tsdf_data['points']

            # 相机内参
            fx = camera_info.K[0]
            fy = camera_info.K[4]
            cx = camera_info.K[2]
            cy = camera_info.K[5]

            # 投影到图像平面
            projected_points = []
            valid_indices = []

            for i, point in enumerate(points_3d):
                x, y, z = point

                if z > 0:  # 确保点在相机前方
                    u = int(fx * x / z + cx)
                    v = int(fy * y / z + cy)

                    # 检查是否在图像范围内
                    if (0 <= u < camera_info.width and
                        0 <= v < camera_info.height):
                        projected_points.append([u, v, z])
                        valid_indices.append(i)

            return {
                'projected_points': np.array(projected_points),
                'valid_indices': np.array(valid_indices)
            }

        except Exception as e:
            rospy.logerr(f"点投影失败: {e}")
            return None

    def apply_geometric_constraints(self, semantic_data, projected_points, geometric_features):
        """应用几何约束优化语义分割"""
        try:
            if projected_points is None or geometric_features is None:
                return semantic_data

            semantic_mask = semantic_data['mask'].copy()
            confidence_map = semantic_data['confidence'].copy()

            proj_points = projected_points['projected_points']
            valid_indices = projected_points['valid_indices']

            normals = geometric_features['normals'][valid_indices]
            curvatures = geometric_features['curvatures'][valid_indices]
            depth_gradients = geometric_features['depth_gradients'][valid_indices]

            # 几何一致性检查
            for i, (u, v, depth) in enumerate(proj_points):
                u, v = int(u), int(v)

                if (0 <= u < semantic_mask.shape[1] and
                    0 <= v < semantic_mask.shape[0]):

                    current_class = semantic_mask[v, u]
                    current_confidence = confidence_map[v, u]

                    # 基于几何特征调整置信度
                    geometric_confidence = self.compute_geometric_confidence(
                        normals[i], curvatures[i], depth_gradients[i], current_class
                    )

                    # 融合几何和语义置信度
                    fused_confidence = (
                        (1 - self.geometric_constraint_weight) * current_confidence +
                        self.geometric_constraint_weight * geometric_confidence
                    )

                    confidence_map[v, u] = fused_confidence

                    # 如果几何约束置信度过低，重新分类
                    if geometric_confidence < 0.3:
                        semantic_mask[v, u] = self.reclassify_based_on_geometry(
                            normals[i], curvatures[i], depth_gradients[i]
                        )

            return {
                'mask': semantic_mask,
                'confidence': confidence_map,
                'class_names': semantic_data['class_names'],
                'class_colors': semantic_data['class_colors']
            }

        except Exception as e:
            rospy.logerr(f"几何约束应用失败: {e}")
            return semantic_data

    def compute_geometric_confidence(self, normal, curvature, depth_gradient, semantic_class):
        """基于几何特征计算置信度"""
        try:
            # 不同语义类别的几何特征期望
            class_geometric_priors = {
                0: {'curvature_range': (0.0, 0.1), 'normal_consistency': 0.8},  # 背景
                1: {'curvature_range': (0.0, 0.3), 'normal_consistency': 0.6},  # 飞机
                6: {'curvature_range': (0.0, 0.2), 'normal_consistency': 0.7},  # 公交车
                7: {'curvature_range': (0.0, 0.2), 'normal_consistency': 0.7},  # 汽车
                9: {'curvature_range': (0.1, 0.5), 'normal_consistency': 0.5},  # 椅子
                11: {'curvature_range': (0.0, 0.2), 'normal_consistency': 0.8}, # 餐桌
                15: {'curvature_range': (0.2, 0.8), 'normal_consistency': 0.4}, # 人
            }

            if semantic_class not in class_geometric_priors:
                return 0.5  # 默认置信度

            prior = class_geometric_priors[semantic_class]

            # 曲率一致性检查
            curvature_score = 1.0
            if curvature < prior['curvature_range'][0]:
                curvature_score = 0.3
            elif curvature > prior['curvature_range'][1]:
                curvature_score = 0.3

            # 法向量一致性检查（简化版）
            normal_score = min(1.0, np.linalg.norm(normal))

            # 深度梯度一致性
            gradient_score = 1.0 - min(1.0, depth_gradient)

            # 综合几何置信度
            geometric_confidence = (
                0.4 * curvature_score +
                0.3 * normal_score +
                0.3 * gradient_score
            )

            return geometric_confidence

        except Exception as e:
            rospy.logerr(f"几何置信度计算失败: {e}")
            return 0.5

    def reclassify_based_on_geometry(self, normal, curvature, depth_gradient):
        """基于几何特征重新分类"""
        try:
            # 简化的几何分类规则
            if curvature > 0.5:
                return 15  # 人（高曲率）
            elif curvature < 0.1 and depth_gradient < 0.05:
                return 11  # 餐桌（平面）
            elif 0.1 <= curvature <= 0.3:
                return 9   # 椅子（中等曲率）
            else:
                return 0   # 背景

        except Exception as e:
            rospy.logerr(f"几何重分类失败: {e}")
            return 0

if __name__ == '__main__':
    try:
        node = GeometrySemanticFusionNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```

## 3. 目标检测融合模块 (detection_fusion_node.py)

```python
#!/usr/bin/env python3
import rospy
import cv2
import numpy as np
from cv_bridge import CvBridge
from darknet_ros_msgs.msg import BoundingBoxes
from semantic_perception.msg import SemanticSegmentation, DetectedObject
from sensor_msgs.msg import Image
import message_filters

class DetectionFusionNode:
    """检测融合节点"""

    def __init__(self):
        rospy.init_node('detection_fusion_node', anonymous=True)

        self.bridge = CvBridge()

        # 参数配置
        self.enable_yolo_fusion = rospy.get_param('~enable_yolo_fusion', True)
        self.confidence_threshold = rospy.get_param('~confidence_threshold', 0.5)
        self.fusion_timeout = rospy.get_param('~fusion_timeout', 0.1)

        # 数据缓存
        self.latest_segmentation = None
        self.latest_detections = None

        # 设置ROS通信
        self.setup_ros_communication()

        rospy.loginfo("检测融合节点初始化完成")

    def setup_ros_communication(self):
        """设置ROS通信"""
        # 订阅器
        self.seg_sub = rospy.Subscriber(
            '/semantic_perception/segmentation_result',
            SemanticSegmentation,
            self.segmentation_callback,
            queue_size=1
        )

        # 仅在启用YOLO融合时订阅YOLO检测
        if self.enable_yolo_fusion:
            self.det_sub = rospy.Subscriber('yolo_detections', BoundingBoxes,
                                           self.detection_callback, queue_size=1)
            rospy.loginfo("检测融合节点已启动 (启用YOLO融合)")
        else:
            rospy.loginfo("检测融合节点已启动 (仅语义分割模式)")

        # 发布器
        self.fused_pub = rospy.Publisher(
            '/semantic_perception/fused_objects',
            DetectedObject,
            queue_size=10
        )

    def segmentation_callback(self, msg):
        """语义分割回调"""
        self.latest_segmentation = msg
        self.try_fusion()

    def detection_callback(self, msg):
        """YOLO检测回调"""
        self.latest_detections = msg
        self.try_fusion()

    def try_fusion(self):
        """尝试融合检测结果"""
        if self.latest_segmentation is None:
            return

        # 如果启用YOLO融合但没有YOLO检测数据，则等待
        if self.enable_yolo_fusion and self.latest_detections is None:
            return

        # 执行融合
        try:
            if self.enable_yolo_fusion and self.latest_detections is not None:
                # 检查时间同步
                seg_time = self.latest_segmentation.header.stamp
                det_time = self.latest_detections.header.stamp
                time_diff = abs((seg_time - det_time).to_sec())

                if time_diff > self.fusion_timeout:
                    rospy.logwarn(f"时间差过大: {time_diff:.3f}秒")
                    return

                fused_objects = self.fuse_detections(self.latest_segmentation, self.latest_detections)
            else:
                # 仅使用语义分割数据
                fused_objects = self.process_segmentation_only(self.latest_segmentation)

            # 发布融合结果
            for obj in fused_objects:
                self.fused_pub.publish(obj)

        except Exception as e:
            rospy.logerr(f"融合检测失败: {e}")

    def fuse_detections(self, segmentation, detections):
        """融合检测结果"""
        fused_objects = []

        # 获取分割掩码
        try:
            seg_mask = self.bridge.imgmsg_to_cv2(segmentation.segmentation_mask, "mono8")
        except Exception as e:
            rospy.logerr(f"分割掩码转换失败: {e}")
            return fused_objects

        # 处理每个YOLO检测
        for bbox in detections.bounding_boxes:
            if bbox.probability < self.confidence_threshold:
                continue

            # 创建融合对象
            obj = DetectedObject()
            obj.header = segmentation.header
            obj.class_name = bbox.Class
            obj.confidence = bbox.probability
            obj.bbox_xmin = bbox.xmin
            obj.bbox_ymin = bbox.ymin
            obj.bbox_xmax = bbox.xmax
            obj.bbox_ymax = bbox.ymax
            obj.bbox_width = bbox.xmax - bbox.xmin
            obj.bbox_height = bbox.ymax - bbox.ymin

            # 语义属性
            obj.is_fire = (bbox.Class.lower() in ['fire', 'flame', 'smoke'])
            obj.is_obstacle = (bbox.Class.lower() in ['person', 'car', 'chair', 'table'])
            obj.is_navigable = (bbox.Class.lower() in ['floor', 'ground', 'road'])
            obj.is_static = not (bbox.Class.lower() in ['person', 'car', 'bicycle'])
            obj.is_dangerous = obj.is_fire

            # 追踪信息
            obj.track_id = -1  # 暂未实现追踪
            obj.age = 1
            obj.velocity = 0.0

            # 时间戳
            obj.first_seen = rospy.Time.now().to_sec()
            obj.last_seen = rospy.Time.now().to_sec()

            # 分析分割区域
            self.analyze_segmentation_region(obj, seg_mask, segmentation)

            fused_objects.append(obj)

        return fused_objects

    def analyze_segmentation_region(self, obj, seg_mask, segmentation):
        """分析分割区域"""
        try:
            # 提取检测框区域的分割结果
            x1, y1 = int(obj.bbox_xmin), int(obj.bbox_ymin)
            x2, y2 = int(obj.bbox_xmax), int(obj.bbox_ymax)

            # 确保边界在图像范围内
            h, w = seg_mask.shape
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(w, x2), min(h, y2)

            if x2 <= x1 or y2 <= y1:
                return

            roi_mask = seg_mask[y1:y2, x1:x2]

            # 统计语义类别分布
            unique_classes, counts = np.unique(roi_mask, return_counts=True)

            if len(unique_classes) > 0:
                # 找到最主要的语义类别
                dominant_class_idx = np.argmax(counts)
                dominant_class = unique_classes[dominant_class_idx]
                dominant_ratio = counts[dominant_class_idx] / np.sum(counts)

                obj.dominant_semantic_class = int(dominant_class)
                obj.semantic_consistency = float(dominant_ratio)

                # 设置语义类别名称
                if dominant_class < len(segmentation.class_names):
                    obj.semantic_class_name = segmentation.class_names[dominant_class]
                else:
                    obj.semantic_class_name = "unknown"

        except Exception as e:
            rospy.logerr(f"分割区域分析失败: {e}")

if __name__ == '__main__':
    try:
        node = DetectionFusionNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```
