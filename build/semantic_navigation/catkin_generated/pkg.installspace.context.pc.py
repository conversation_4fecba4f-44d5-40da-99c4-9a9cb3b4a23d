# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "${prefix}/include".split(';') if "${prefix}/include" != "" else []
PROJECT_CATKIN_DEPENDS = "actionlib;geometry_msgs;move_base_msgs;nav_msgs;roscpp;rospy;sensor_msgs;std_msgs;tf2_ros;visualization_msgs;message_runtime".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "semantic_navigation"
PROJECT_SPACE_DIR = "/root/autodl-tmp/rtab_ws/install"
PROJECT_VERSION = "0.0.0"
