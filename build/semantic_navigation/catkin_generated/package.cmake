set(_CATKIN_CURRENT_PACKAGE "semantic_navigation")
set(semantic_navigation_VERSION "0.0.0")
set(semantic_navigation_MAINTAINER "root <<EMAIL>>")
set(semantic_navigation_PACKAGE_FORMAT "2")
set(semantic_navigation_BUILD_DEPENDS "actionlib" "geometry_msgs" "move_base_msgs" "nav_msgs" "roscpp" "rospy" "sensor_msgs" "std_msgs" "tf2_ros" "visualization_msgs" "message_generation")
set(semantic_navigation_BUILD_EXPORT_DEPENDS "actionlib" "geometry_msgs" "move_base_msgs" "nav_msgs" "roscpp" "rospy" "sensor_msgs" "std_msgs" "tf2_ros" "visualization_msgs")
set(semantic_navigation_BUILDTOOL_DEPENDS "catkin")
set(semantic_navigation_BUILDTOOL_EXPORT_DEPENDS )
set(semantic_navigation_EXEC_DEPENDS "actionlib" "geometry_msgs" "move_base_msgs" "nav_msgs" "roscpp" "rospy" "sensor_msgs" "std_msgs" "tf2_ros" "visualization_msgs" "message_runtime")
set(semantic_navigation_RUN_DEPENDS "actionlib" "geometry_msgs" "move_base_msgs" "nav_msgs" "roscpp" "rospy" "sensor_msgs" "std_msgs" "tf2_ros" "visualization_msgs" "message_runtime")
set(semantic_navigation_TEST_DEPENDS )
set(semantic_navigation_DOC_DEPENDS )
set(semantic_navigation_URL_WEBSITE "")
set(semantic_navigation_URL_BUGTRACKER "")
set(semantic_navigation_URL_REPOSITORY "")
set(semantic_navigation_DEPRECATED "")