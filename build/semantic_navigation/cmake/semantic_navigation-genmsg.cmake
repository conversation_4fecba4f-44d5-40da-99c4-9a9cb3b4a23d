# generated from genmsg/cmake/pkg-genmsg.cmake.em

message(STATUS "semantic_navigation: 4 messages, 3 services")

set(MSG_I_FLAGS "-Isemantic_navigation:/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg;-Igeometry_msgs:/opt/ros/noetic/share/geometry_msgs/cmake/../msg;-Inav_msgs:/opt/ros/noetic/share/nav_msgs/cmake/../msg;-Istd_msgs:/opt/ros/noetic/share/std_msgs/cmake/../msg;-Iactionlib_msgs:/opt/ros/noetic/share/actionlib_msgs/cmake/../msg")

# Find all generators
find_package(gencpp REQUIRED)
find_package(geneus REQUIRED)
find_package(genlisp REQUIRED)
find_package(gennodejs REQUIRED)
find_package(genpy REQUIRED)

add_custom_target(semantic_navigation_generate_messages ALL)

# verify that message/service dependencies have not changed since configure



get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg" NAME_WE)
add_custom_target(_semantic_navigation_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_navigation" "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg" "std_msgs/Header:geometry_msgs/Point:geometry_msgs/Quaternion:geometry_msgs/Pose:geometry_msgs/PoseStamped"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg" NAME_WE)
add_custom_target(_semantic_navigation_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_navigation" "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg" "std_msgs/Header:geometry_msgs/Point:geometry_msgs/Quaternion:geometry_msgs/Pose:geometry_msgs/PoseStamped:nav_msgs/Path"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg" NAME_WE)
add_custom_target(_semantic_navigation_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_navigation" "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg" "std_msgs/Header:geometry_msgs/Point:geometry_msgs/Quaternion:geometry_msgs/Pose:geometry_msgs/PoseStamped"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg" NAME_WE)
add_custom_target(_semantic_navigation_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_navigation" "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg" "geometry_msgs/Point:std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv" NAME_WE)
add_custom_target(_semantic_navigation_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_navigation" "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv" "std_msgs/Header:geometry_msgs/Point:geometry_msgs/Quaternion:geometry_msgs/Pose:semantic_navigation/SemanticPath:geometry_msgs/PoseStamped:semantic_navigation/SemanticNavigationGoal:nav_msgs/Path"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv" NAME_WE)
add_custom_target(_semantic_navigation_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_navigation" "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv" "std_msgs/Header:geometry_msgs/Point:geometry_msgs/Quaternion:geometry_msgs/Pose:semantic_navigation/SemanticPath:geometry_msgs/PoseStamped:nav_msgs/Path"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv" NAME_WE)
add_custom_target(_semantic_navigation_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_navigation" "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv" "std_msgs/Header:geometry_msgs/Point:geometry_msgs/Quaternion:geometry_msgs/Pose:geometry_msgs/PoseStamped"
)

#
#  langs = gencpp;geneus;genlisp;gennodejs;genpy
#

### Section generating for lang: gencpp
### Generating Messages
_generate_msg_cpp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_navigation
)
_generate_msg_cpp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_navigation
)
_generate_msg_cpp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_navigation
)
_generate_msg_cpp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_navigation
)

### Generating Services
_generate_srv_cpp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_navigation
)
_generate_srv_cpp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_navigation
)
_generate_srv_cpp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_navigation
)

### Generating Module File
_generate_module_cpp(semantic_navigation
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_navigation
  "${ALL_GEN_OUTPUT_FILES_cpp}"
)

add_custom_target(semantic_navigation_generate_messages_cpp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_cpp}
)
add_dependencies(semantic_navigation_generate_messages semantic_navigation_generate_messages_cpp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_cpp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_cpp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_cpp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_cpp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_cpp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_cpp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_cpp _semantic_navigation_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_navigation_gencpp)
add_dependencies(semantic_navigation_gencpp semantic_navigation_generate_messages_cpp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_navigation_generate_messages_cpp)

### Section generating for lang: geneus
### Generating Messages
_generate_msg_eus(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_navigation
)
_generate_msg_eus(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_navigation
)
_generate_msg_eus(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_navigation
)
_generate_msg_eus(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_navigation
)

### Generating Services
_generate_srv_eus(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_navigation
)
_generate_srv_eus(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_navigation
)
_generate_srv_eus(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_navigation
)

### Generating Module File
_generate_module_eus(semantic_navigation
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_navigation
  "${ALL_GEN_OUTPUT_FILES_eus}"
)

add_custom_target(semantic_navigation_generate_messages_eus
  DEPENDS ${ALL_GEN_OUTPUT_FILES_eus}
)
add_dependencies(semantic_navigation_generate_messages semantic_navigation_generate_messages_eus)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_eus _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_eus _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_eus _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_eus _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_eus _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_eus _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_eus _semantic_navigation_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_navigation_geneus)
add_dependencies(semantic_navigation_geneus semantic_navigation_generate_messages_eus)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_navigation_generate_messages_eus)

### Section generating for lang: genlisp
### Generating Messages
_generate_msg_lisp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_navigation
)
_generate_msg_lisp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_navigation
)
_generate_msg_lisp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_navigation
)
_generate_msg_lisp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_navigation
)

### Generating Services
_generate_srv_lisp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_navigation
)
_generate_srv_lisp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_navigation
)
_generate_srv_lisp(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_navigation
)

### Generating Module File
_generate_module_lisp(semantic_navigation
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_navigation
  "${ALL_GEN_OUTPUT_FILES_lisp}"
)

add_custom_target(semantic_navigation_generate_messages_lisp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_lisp}
)
add_dependencies(semantic_navigation_generate_messages semantic_navigation_generate_messages_lisp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_lisp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_lisp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_lisp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_lisp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_lisp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_lisp _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_lisp _semantic_navigation_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_navigation_genlisp)
add_dependencies(semantic_navigation_genlisp semantic_navigation_generate_messages_lisp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_navigation_generate_messages_lisp)

### Section generating for lang: gennodejs
### Generating Messages
_generate_msg_nodejs(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_navigation
)
_generate_msg_nodejs(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_navigation
)
_generate_msg_nodejs(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_navigation
)
_generate_msg_nodejs(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_navigation
)

### Generating Services
_generate_srv_nodejs(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_navigation
)
_generate_srv_nodejs(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_navigation
)
_generate_srv_nodejs(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_navigation
)

### Generating Module File
_generate_module_nodejs(semantic_navigation
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_navigation
  "${ALL_GEN_OUTPUT_FILES_nodejs}"
)

add_custom_target(semantic_navigation_generate_messages_nodejs
  DEPENDS ${ALL_GEN_OUTPUT_FILES_nodejs}
)
add_dependencies(semantic_navigation_generate_messages semantic_navigation_generate_messages_nodejs)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_nodejs _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_nodejs _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_nodejs _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_nodejs _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_nodejs _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_nodejs _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_nodejs _semantic_navigation_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_navigation_gennodejs)
add_dependencies(semantic_navigation_gennodejs semantic_navigation_generate_messages_nodejs)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_navigation_generate_messages_nodejs)

### Section generating for lang: genpy
### Generating Messages
_generate_msg_py(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation
)
_generate_msg_py(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation
)
_generate_msg_py(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation
)
_generate_msg_py(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation
)

### Generating Services
_generate_srv_py(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation
)
_generate_srv_py(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg;/opt/ros/noetic/share/nav_msgs/cmake/../msg/Path.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation
)
_generate_srv_py(semantic_navigation
  "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/PoseStamped.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation
)

### Generating Module File
_generate_module_py(semantic_navigation
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation
  "${ALL_GEN_OUTPUT_FILES_py}"
)

add_custom_target(semantic_navigation_generate_messages_py
  DEPENDS ${ALL_GEN_OUTPUT_FILES_py}
)
add_dependencies(semantic_navigation_generate_messages semantic_navigation_generate_messages_py)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_py _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_py _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_py _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_py _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_py _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_py _semantic_navigation_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv" NAME_WE)
add_dependencies(semantic_navigation_generate_messages_py _semantic_navigation_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_navigation_genpy)
add_dependencies(semantic_navigation_genpy semantic_navigation_generate_messages_py)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_navigation_generate_messages_py)



if(gencpp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_navigation)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_navigation
    DESTINATION ${gencpp_INSTALL_DIR}
  )
endif()
if(TARGET geometry_msgs_generate_messages_cpp)
  add_dependencies(semantic_navigation_generate_messages_cpp geometry_msgs_generate_messages_cpp)
endif()
if(TARGET nav_msgs_generate_messages_cpp)
  add_dependencies(semantic_navigation_generate_messages_cpp nav_msgs_generate_messages_cpp)
endif()
if(TARGET std_msgs_generate_messages_cpp)
  add_dependencies(semantic_navigation_generate_messages_cpp std_msgs_generate_messages_cpp)
endif()

if(geneus_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_navigation)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_navigation
    DESTINATION ${geneus_INSTALL_DIR}
  )
endif()
if(TARGET geometry_msgs_generate_messages_eus)
  add_dependencies(semantic_navigation_generate_messages_eus geometry_msgs_generate_messages_eus)
endif()
if(TARGET nav_msgs_generate_messages_eus)
  add_dependencies(semantic_navigation_generate_messages_eus nav_msgs_generate_messages_eus)
endif()
if(TARGET std_msgs_generate_messages_eus)
  add_dependencies(semantic_navigation_generate_messages_eus std_msgs_generate_messages_eus)
endif()

if(genlisp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_navigation)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_navigation
    DESTINATION ${genlisp_INSTALL_DIR}
  )
endif()
if(TARGET geometry_msgs_generate_messages_lisp)
  add_dependencies(semantic_navigation_generate_messages_lisp geometry_msgs_generate_messages_lisp)
endif()
if(TARGET nav_msgs_generate_messages_lisp)
  add_dependencies(semantic_navigation_generate_messages_lisp nav_msgs_generate_messages_lisp)
endif()
if(TARGET std_msgs_generate_messages_lisp)
  add_dependencies(semantic_navigation_generate_messages_lisp std_msgs_generate_messages_lisp)
endif()

if(gennodejs_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_navigation)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_navigation
    DESTINATION ${gennodejs_INSTALL_DIR}
  )
endif()
if(TARGET geometry_msgs_generate_messages_nodejs)
  add_dependencies(semantic_navigation_generate_messages_nodejs geometry_msgs_generate_messages_nodejs)
endif()
if(TARGET nav_msgs_generate_messages_nodejs)
  add_dependencies(semantic_navigation_generate_messages_nodejs nav_msgs_generate_messages_nodejs)
endif()
if(TARGET std_msgs_generate_messages_nodejs)
  add_dependencies(semantic_navigation_generate_messages_nodejs std_msgs_generate_messages_nodejs)
endif()

if(genpy_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation)
  install(CODE "execute_process(COMMAND \"/root/miniconda3/bin/python3\" -m compileall \"${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation\")")
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_navigation
    DESTINATION ${genpy_INSTALL_DIR}
  )
endif()
if(TARGET geometry_msgs_generate_messages_py)
  add_dependencies(semantic_navigation_generate_messages_py geometry_msgs_generate_messages_py)
endif()
if(TARGET nav_msgs_generate_messages_py)
  add_dependencies(semantic_navigation_generate_messages_py nav_msgs_generate_messages_py)
endif()
if(TARGET std_msgs_generate_messages_py)
  add_dependencies(semantic_navigation_generate_messages_py std_msgs_generate_messages_py)
endif()
