# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticNavigationGoal.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/SemanticPath.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/NavigationStatus.msg;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg/EmergencyAlert.msg"
services_str = "/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/SetNavigationGoal.srv;/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/GetSafePath.srv;/root/autodl-tmp/rtab_ws/src/semantic_navigation/srv/EmergencyStop.srv"
pkg_name = "semantic_navigation"
dependencies_str = "geometry_msgs;nav_msgs;std_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "semantic_navigation;/root/autodl-tmp/rtab_ws/src/semantic_navigation/msg;geometry_msgs;/opt/ros/noetic/share/geometry_msgs/cmake/../msg;nav_msgs;/opt/ros/noetic/share/nav_msgs/cmake/../msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg;actionlib_msgs;/opt/ros/noetic/share/actionlib_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/root/miniconda3/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
