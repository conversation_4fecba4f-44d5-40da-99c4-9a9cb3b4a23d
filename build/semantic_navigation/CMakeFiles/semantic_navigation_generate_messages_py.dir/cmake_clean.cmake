file(REMOVE_RECURSE
  "/root/autodl-tmp/rtab_ws/devel/lib/python3/dist-packages/semantic_navigation/msg/_EmergencyAlert.py"
  "/root/autodl-tmp/rtab_ws/devel/lib/python3/dist-packages/semantic_navigation/msg/_NavigationStatus.py"
  "/root/autodl-tmp/rtab_ws/devel/lib/python3/dist-packages/semantic_navigation/msg/_SemanticNavigationGoal.py"
  "/root/autodl-tmp/rtab_ws/devel/lib/python3/dist-packages/semantic_navigation/msg/_SemanticPath.py"
  "/root/autodl-tmp/rtab_ws/devel/lib/python3/dist-packages/semantic_navigation/msg/__init__.py"
  "/root/autodl-tmp/rtab_ws/devel/lib/python3/dist-packages/semantic_navigation/srv/_EmergencyStop.py"
  "/root/autodl-tmp/rtab_ws/devel/lib/python3/dist-packages/semantic_navigation/srv/_GetSafePath.py"
  "/root/autodl-tmp/rtab_ws/devel/lib/python3/dist-packages/semantic_navigation/srv/_SetNavigationGoal.py"
  "/root/autodl-tmp/rtab_ws/devel/lib/python3/dist-packages/semantic_navigation/srv/__init__.py"
  "CMakeFiles/semantic_navigation_generate_messages_py"
)

# Per-language clean rules from dependency scanning.
foreach(lang )
  include(CMakeFiles/semantic_navigation_generate_messages_py.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
