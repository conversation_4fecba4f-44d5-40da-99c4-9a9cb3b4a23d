# generated from genmsg/cmake/pkg-genmsg.cmake.em

message(STATUS "semantic_mapping: 4 messages, 2 services")

set(MSG_I_FLAGS "-Isemantic_mapping:/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg;-Istd_msgs:/opt/ros/noetic/share/std_msgs/cmake/../msg;-Isensor_msgs:/opt/ros/noetic/share/sensor_msgs/cmake/../msg;-Igeometry_msgs:/opt/ros/noetic/share/geometry_msgs/cmake/../msg;-Ivisualization_msgs:/opt/ros/noetic/share/visualization_msgs/cmake/../msg;-Igrid_map_msgs:/opt/ros/noetic/share/grid_map_msgs/cmake/../msg;-Ioctomap_msgs:/opt/ros/noetic/share/octomap_msgs/cmake/../msg;-Ivision_msgs:/opt/ros/noetic/share/vision_msgs/cmake/../msg")

# Find all generators
find_package(gencpp REQUIRED)
find_package(geneus REQUIRED)
find_package(genlisp REQUIRED)
find_package(gennodejs REQUIRED)
find_package(genpy REQUIRED)

add_custom_target(semantic_mapping_generate_messages ALL)

# verify that message/service dependencies have not changed since configure



get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg" NAME_WE)
add_custom_target(_semantic_mapping_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_mapping" "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg" ""
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg" NAME_WE)
add_custom_target(_semantic_mapping_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_mapping" "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg" "geometry_msgs/Pose:geometry_msgs/Point:geometry_msgs/Polygon:geometry_msgs/Point32:geometry_msgs/Vector3:geometry_msgs/Quaternion:std_msgs/Header:semantic_mapping/SemanticVoxel"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg" NAME_WE)
add_custom_target(_semantic_mapping_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_mapping" "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg" "geometry_msgs/Point:geometry_msgs/Vector3:geometry_msgs/Quaternion:std_msgs/Header:semantic_mapping/SemanticVoxel"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg" NAME_WE)
add_custom_target(_semantic_mapping_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_mapping" "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg" "geometry_msgs/Point:std_msgs/Header:geometry_msgs/Pose:geometry_msgs/Quaternion"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv" NAME_WE)
add_custom_target(_semantic_mapping_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_mapping" "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv" "geometry_msgs/Pose:semantic_mapping/SemanticMap:geometry_msgs/Point:geometry_msgs/Polygon:geometry_msgs/Point32:geometry_msgs/Vector3:semantic_mapping/ObjectInstance:geometry_msgs/Quaternion:std_msgs/Header:semantic_mapping/SemanticVoxel"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv" NAME_WE)
add_custom_target(_semantic_mapping_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_mapping" "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv" "geometry_msgs/Point:semantic_mapping/ObjectInstance:geometry_msgs/Vector3:geometry_msgs/Quaternion:std_msgs/Header:semantic_mapping/SemanticVoxel"
)

#
#  langs = gencpp;geneus;genlisp;gennodejs;genpy
#

### Section generating for lang: gencpp
### Generating Messages
_generate_msg_cpp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_mapping
)
_generate_msg_cpp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Polygon.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point32.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_mapping
)
_generate_msg_cpp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_mapping
)
_generate_msg_cpp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_mapping
)

### Generating Services
_generate_srv_cpp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Polygon.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point32.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_mapping
)
_generate_srv_cpp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_mapping
)

### Generating Module File
_generate_module_cpp(semantic_mapping
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_mapping
  "${ALL_GEN_OUTPUT_FILES_cpp}"
)

add_custom_target(semantic_mapping_generate_messages_cpp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_cpp}
)
add_dependencies(semantic_mapping_generate_messages semantic_mapping_generate_messages_cpp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_cpp _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_cpp _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_cpp _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_cpp _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_cpp _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_cpp _semantic_mapping_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_mapping_gencpp)
add_dependencies(semantic_mapping_gencpp semantic_mapping_generate_messages_cpp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_mapping_generate_messages_cpp)

### Section generating for lang: geneus
### Generating Messages
_generate_msg_eus(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_mapping
)
_generate_msg_eus(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Polygon.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point32.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_mapping
)
_generate_msg_eus(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_mapping
)
_generate_msg_eus(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_mapping
)

### Generating Services
_generate_srv_eus(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Polygon.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point32.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_mapping
)
_generate_srv_eus(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_mapping
)

### Generating Module File
_generate_module_eus(semantic_mapping
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_mapping
  "${ALL_GEN_OUTPUT_FILES_eus}"
)

add_custom_target(semantic_mapping_generate_messages_eus
  DEPENDS ${ALL_GEN_OUTPUT_FILES_eus}
)
add_dependencies(semantic_mapping_generate_messages semantic_mapping_generate_messages_eus)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_eus _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_eus _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_eus _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_eus _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_eus _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_eus _semantic_mapping_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_mapping_geneus)
add_dependencies(semantic_mapping_geneus semantic_mapping_generate_messages_eus)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_mapping_generate_messages_eus)

### Section generating for lang: genlisp
### Generating Messages
_generate_msg_lisp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_mapping
)
_generate_msg_lisp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Polygon.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point32.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_mapping
)
_generate_msg_lisp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_mapping
)
_generate_msg_lisp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_mapping
)

### Generating Services
_generate_srv_lisp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Polygon.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point32.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_mapping
)
_generate_srv_lisp(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_mapping
)

### Generating Module File
_generate_module_lisp(semantic_mapping
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_mapping
  "${ALL_GEN_OUTPUT_FILES_lisp}"
)

add_custom_target(semantic_mapping_generate_messages_lisp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_lisp}
)
add_dependencies(semantic_mapping_generate_messages semantic_mapping_generate_messages_lisp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_lisp _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_lisp _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_lisp _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_lisp _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_lisp _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_lisp _semantic_mapping_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_mapping_genlisp)
add_dependencies(semantic_mapping_genlisp semantic_mapping_generate_messages_lisp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_mapping_generate_messages_lisp)

### Section generating for lang: gennodejs
### Generating Messages
_generate_msg_nodejs(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_mapping
)
_generate_msg_nodejs(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Polygon.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point32.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_mapping
)
_generate_msg_nodejs(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_mapping
)
_generate_msg_nodejs(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_mapping
)

### Generating Services
_generate_srv_nodejs(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Polygon.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point32.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_mapping
)
_generate_srv_nodejs(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_mapping
)

### Generating Module File
_generate_module_nodejs(semantic_mapping
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_mapping
  "${ALL_GEN_OUTPUT_FILES_nodejs}"
)

add_custom_target(semantic_mapping_generate_messages_nodejs
  DEPENDS ${ALL_GEN_OUTPUT_FILES_nodejs}
)
add_dependencies(semantic_mapping_generate_messages semantic_mapping_generate_messages_nodejs)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_nodejs _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_nodejs _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_nodejs _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_nodejs _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_nodejs _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_nodejs _semantic_mapping_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_mapping_gennodejs)
add_dependencies(semantic_mapping_gennodejs semantic_mapping_generate_messages_nodejs)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_mapping_generate_messages_nodejs)

### Section generating for lang: genpy
### Generating Messages
_generate_msg_py(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_mapping
)
_generate_msg_py(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Polygon.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point32.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_mapping
)
_generate_msg_py(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_mapping
)
_generate_msg_py(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_mapping
)

### Generating Services
_generate_srv_py(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Pose.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Polygon.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point32.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_mapping
)
_generate_srv_py(semantic_mapping
  "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_mapping
)

### Generating Module File
_generate_module_py(semantic_mapping
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_mapping
  "${ALL_GEN_OUTPUT_FILES_py}"
)

add_custom_target(semantic_mapping_generate_messages_py
  DEPENDS ${ALL_GEN_OUTPUT_FILES_py}
)
add_dependencies(semantic_mapping_generate_messages semantic_mapping_generate_messages_py)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticVoxel.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_py _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticMap.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_py _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/ObjectInstance.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_py _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/msg/SemanticLayer.msg" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_py _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/GetSemanticMap.srv" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_py _semantic_mapping_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_mapping/srv/UpdateSemanticMap.srv" NAME_WE)
add_dependencies(semantic_mapping_generate_messages_py _semantic_mapping_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_mapping_genpy)
add_dependencies(semantic_mapping_genpy semantic_mapping_generate_messages_py)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_mapping_generate_messages_py)



if(gencpp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_mapping)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_mapping
    DESTINATION ${gencpp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_cpp)
  add_dependencies(semantic_mapping_generate_messages_cpp std_msgs_generate_messages_cpp)
endif()
if(TARGET sensor_msgs_generate_messages_cpp)
  add_dependencies(semantic_mapping_generate_messages_cpp sensor_msgs_generate_messages_cpp)
endif()
if(TARGET geometry_msgs_generate_messages_cpp)
  add_dependencies(semantic_mapping_generate_messages_cpp geometry_msgs_generate_messages_cpp)
endif()
if(TARGET visualization_msgs_generate_messages_cpp)
  add_dependencies(semantic_mapping_generate_messages_cpp visualization_msgs_generate_messages_cpp)
endif()
if(TARGET grid_map_msgs_generate_messages_cpp)
  add_dependencies(semantic_mapping_generate_messages_cpp grid_map_msgs_generate_messages_cpp)
endif()
if(TARGET octomap_msgs_generate_messages_cpp)
  add_dependencies(semantic_mapping_generate_messages_cpp octomap_msgs_generate_messages_cpp)
endif()
if(TARGET vision_msgs_generate_messages_cpp)
  add_dependencies(semantic_mapping_generate_messages_cpp vision_msgs_generate_messages_cpp)
endif()

if(geneus_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_mapping)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_mapping
    DESTINATION ${geneus_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_eus)
  add_dependencies(semantic_mapping_generate_messages_eus std_msgs_generate_messages_eus)
endif()
if(TARGET sensor_msgs_generate_messages_eus)
  add_dependencies(semantic_mapping_generate_messages_eus sensor_msgs_generate_messages_eus)
endif()
if(TARGET geometry_msgs_generate_messages_eus)
  add_dependencies(semantic_mapping_generate_messages_eus geometry_msgs_generate_messages_eus)
endif()
if(TARGET visualization_msgs_generate_messages_eus)
  add_dependencies(semantic_mapping_generate_messages_eus visualization_msgs_generate_messages_eus)
endif()
if(TARGET grid_map_msgs_generate_messages_eus)
  add_dependencies(semantic_mapping_generate_messages_eus grid_map_msgs_generate_messages_eus)
endif()
if(TARGET octomap_msgs_generate_messages_eus)
  add_dependencies(semantic_mapping_generate_messages_eus octomap_msgs_generate_messages_eus)
endif()
if(TARGET vision_msgs_generate_messages_eus)
  add_dependencies(semantic_mapping_generate_messages_eus vision_msgs_generate_messages_eus)
endif()

if(genlisp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_mapping)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_mapping
    DESTINATION ${genlisp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_lisp)
  add_dependencies(semantic_mapping_generate_messages_lisp std_msgs_generate_messages_lisp)
endif()
if(TARGET sensor_msgs_generate_messages_lisp)
  add_dependencies(semantic_mapping_generate_messages_lisp sensor_msgs_generate_messages_lisp)
endif()
if(TARGET geometry_msgs_generate_messages_lisp)
  add_dependencies(semantic_mapping_generate_messages_lisp geometry_msgs_generate_messages_lisp)
endif()
if(TARGET visualization_msgs_generate_messages_lisp)
  add_dependencies(semantic_mapping_generate_messages_lisp visualization_msgs_generate_messages_lisp)
endif()
if(TARGET grid_map_msgs_generate_messages_lisp)
  add_dependencies(semantic_mapping_generate_messages_lisp grid_map_msgs_generate_messages_lisp)
endif()
if(TARGET octomap_msgs_generate_messages_lisp)
  add_dependencies(semantic_mapping_generate_messages_lisp octomap_msgs_generate_messages_lisp)
endif()
if(TARGET vision_msgs_generate_messages_lisp)
  add_dependencies(semantic_mapping_generate_messages_lisp vision_msgs_generate_messages_lisp)
endif()

if(gennodejs_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_mapping)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_mapping
    DESTINATION ${gennodejs_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_nodejs)
  add_dependencies(semantic_mapping_generate_messages_nodejs std_msgs_generate_messages_nodejs)
endif()
if(TARGET sensor_msgs_generate_messages_nodejs)
  add_dependencies(semantic_mapping_generate_messages_nodejs sensor_msgs_generate_messages_nodejs)
endif()
if(TARGET geometry_msgs_generate_messages_nodejs)
  add_dependencies(semantic_mapping_generate_messages_nodejs geometry_msgs_generate_messages_nodejs)
endif()
if(TARGET visualization_msgs_generate_messages_nodejs)
  add_dependencies(semantic_mapping_generate_messages_nodejs visualization_msgs_generate_messages_nodejs)
endif()
if(TARGET grid_map_msgs_generate_messages_nodejs)
  add_dependencies(semantic_mapping_generate_messages_nodejs grid_map_msgs_generate_messages_nodejs)
endif()
if(TARGET octomap_msgs_generate_messages_nodejs)
  add_dependencies(semantic_mapping_generate_messages_nodejs octomap_msgs_generate_messages_nodejs)
endif()
if(TARGET vision_msgs_generate_messages_nodejs)
  add_dependencies(semantic_mapping_generate_messages_nodejs vision_msgs_generate_messages_nodejs)
endif()

if(genpy_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_mapping)
  install(CODE "execute_process(COMMAND \"/root/miniconda3/bin/python3\" -m compileall \"${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_mapping\")")
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_mapping
    DESTINATION ${genpy_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_py)
  add_dependencies(semantic_mapping_generate_messages_py std_msgs_generate_messages_py)
endif()
if(TARGET sensor_msgs_generate_messages_py)
  add_dependencies(semantic_mapping_generate_messages_py sensor_msgs_generate_messages_py)
endif()
if(TARGET geometry_msgs_generate_messages_py)
  add_dependencies(semantic_mapping_generate_messages_py geometry_msgs_generate_messages_py)
endif()
if(TARGET visualization_msgs_generate_messages_py)
  add_dependencies(semantic_mapping_generate_messages_py visualization_msgs_generate_messages_py)
endif()
if(TARGET grid_map_msgs_generate_messages_py)
  add_dependencies(semantic_mapping_generate_messages_py grid_map_msgs_generate_messages_py)
endif()
if(TARGET octomap_msgs_generate_messages_py)
  add_dependencies(semantic_mapping_generate_messages_py octomap_msgs_generate_messages_py)
endif()
if(TARGET vision_msgs_generate_messages_py)
  add_dependencies(semantic_mapping_generate_messages_py vision_msgs_generate_messages_py)
endif()
