# CMake generated Testfile for 
# Source directory: /root/autodl-tmp/rtab_ws/src
# Build directory: /root/autodl-tmp/rtab_ws/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
subdirs("gtest")
subdirs("rtabmap_ros/rtabmap_launch")
subdirs("rtabmap_ros/rtabmap_ros")
subdirs("turtlebot3_slam_3d")
subdirs("rtabmap_ros/rtabmap_python")
subdirs("darknet_ros/darknet_ros_msgs")
subdirs("darknet_ros/darknet_ros")
subdirs("rtabmap_ros/rtabmap_msgs")
subdirs("semantic_navigation")
subdirs("rtabmap_ros/rtabmap_conversions")
subdirs("rtabmap_ros/rtabmap_demos")
subdirs("rtabmap_ros/rtabmap_examples")
subdirs("rtabmap_ros/rtabmap_sync")
subdirs("rtabmap_ros/rtabmap_util")
subdirs("rtabmap_ros/rtabmap_legacy")
subdirs("rtabmap_ros/rtabmap_odom")
subdirs("rtabmap_ros/rtabmap_slam")
subdirs("rtabmap_ros/rtabmap_viz")
subdirs("tsdf_mapping")
subdirs("rtabmap_ros/rtabmap_rviz_plugins")
subdirs("semantic_perception")
subdirs("semantic_mapping")
subdirs("rtabmap_ros/rtabmap_costmap_plugins")
