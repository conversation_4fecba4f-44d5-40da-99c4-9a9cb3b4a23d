# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/autodl-tmp/rtab_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/autodl-tmp/rtab_ws/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# The main all target
all: cmake_check_build_system
	cd /root/autodl-tmp/rtab_ws/build && $(CMAKE_COMMAND) -E cmake_progress_start /root/autodl-tmp/rtab_ws/build/CMakeFiles /root/autodl-tmp/rtab_ws/build/rtabmap_ros/rtabmap_slam/CMakeFiles/progress.marks
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/autodl-tmp/rtab_ws/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /root/autodl-tmp/rtab_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_cpp.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_cpp.dir/rule
.PHONY : rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_cpp.dir/rule

# Convenience name for target.
apriltag_ros_generate_messages_cpp: rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_cpp.dir/rule

.PHONY : apriltag_ros_generate_messages_cpp

# fast build rule for target.
apriltag_ros_generate_messages_cpp/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_cpp.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_cpp.dir/build
.PHONY : apriltag_ros_generate_messages_cpp/fast

# Convenience name for target.
rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_eus.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_eus.dir/rule
.PHONY : rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_eus.dir/rule

# Convenience name for target.
apriltag_ros_generate_messages_eus: rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_eus.dir/rule

.PHONY : apriltag_ros_generate_messages_eus

# fast build rule for target.
apriltag_ros_generate_messages_eus/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_eus.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_eus.dir/build
.PHONY : apriltag_ros_generate_messages_eus/fast

# Convenience name for target.
rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_lisp.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_lisp.dir/rule
.PHONY : rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_lisp.dir/rule

# Convenience name for target.
apriltag_ros_generate_messages_lisp: rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_lisp.dir/rule

.PHONY : apriltag_ros_generate_messages_lisp

# fast build rule for target.
apriltag_ros_generate_messages_lisp/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_lisp.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_lisp.dir/build
.PHONY : apriltag_ros_generate_messages_lisp/fast

# Convenience name for target.
rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_nodejs.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_nodejs.dir/rule
.PHONY : rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_nodejs.dir/rule

# Convenience name for target.
apriltag_ros_generate_messages_nodejs: rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_nodejs.dir/rule

.PHONY : apriltag_ros_generate_messages_nodejs

# fast build rule for target.
apriltag_ros_generate_messages_nodejs/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_nodejs.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_nodejs.dir/build
.PHONY : apriltag_ros_generate_messages_nodejs/fast

# Convenience name for target.
rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_py.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_py.dir/rule
.PHONY : rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_py.dir/rule

# Convenience name for target.
apriltag_ros_generate_messages_py: rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_py.dir/rule

.PHONY : apriltag_ros_generate_messages_py

# fast build rule for target.
apriltag_ros_generate_messages_py/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_py.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/apriltag_ros_generate_messages_py.dir/build
.PHONY : apriltag_ros_generate_messages_py/fast

# Convenience name for target.
rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/rule
.PHONY : rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/rule

# Convenience name for target.
rtabmap_slam_plugins: rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/rule

.PHONY : rtabmap_slam_plugins

# fast build rule for target.
rtabmap_slam_plugins/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/build
.PHONY : rtabmap_slam_plugins/fast

# Convenience name for target.
rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/rule:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f CMakeFiles/Makefile2 rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/rule
.PHONY : rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/rule

# Convenience name for target.
rtabmap_node: rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/rule

.PHONY : rtabmap_node

# fast build rule for target.
rtabmap_node/fast:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/build
.PHONY : rtabmap_node/fast

src/CoreNode.o: src/CoreNode.cpp.o

.PHONY : src/CoreNode.o

# target to build an object file
src/CoreNode.cpp.o:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/src/CoreNode.cpp.o
.PHONY : src/CoreNode.cpp.o

src/CoreNode.i: src/CoreNode.cpp.i

.PHONY : src/CoreNode.i

# target to preprocess a source file
src/CoreNode.cpp.i:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/src/CoreNode.cpp.i
.PHONY : src/CoreNode.cpp.i

src/CoreNode.s: src/CoreNode.cpp.s

.PHONY : src/CoreNode.s

# target to generate assembly for a file
src/CoreNode.cpp.s:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_node.dir/src/CoreNode.cpp.s
.PHONY : src/CoreNode.cpp.s

src/CoreWrapper.o: src/CoreWrapper.cpp.o

.PHONY : src/CoreWrapper.o

# target to build an object file
src/CoreWrapper.cpp.o:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/src/CoreWrapper.cpp.o
.PHONY : src/CoreWrapper.cpp.o

src/CoreWrapper.i: src/CoreWrapper.cpp.i

.PHONY : src/CoreWrapper.i

# target to preprocess a source file
src/CoreWrapper.cpp.i:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/src/CoreWrapper.cpp.i
.PHONY : src/CoreWrapper.cpp.i

src/CoreWrapper.s: src/CoreWrapper.cpp.s

.PHONY : src/CoreWrapper.s

# target to generate assembly for a file
src/CoreWrapper.cpp.s:
	cd /root/autodl-tmp/rtab_ws/build && $(MAKE) -f rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/build.make rtabmap_ros/rtabmap_slam/CMakeFiles/rtabmap_slam_plugins.dir/src/CoreWrapper.cpp.s
.PHONY : src/CoreWrapper.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... apriltag_ros_generate_messages_cpp"
	@echo "... apriltag_ros_generate_messages_eus"
	@echo "... install/strip"
	@echo "... apriltag_ros_generate_messages_lisp"
	@echo "... apriltag_ros_generate_messages_nodejs"
	@echo "... test"
	@echo "... apriltag_ros_generate_messages_py"
	@echo "... rtabmap_slam_plugins"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... rtabmap_node"
	@echo "... list_install_components"
	@echo "... install"
	@echo "... install/local"
	@echo "... src/CoreNode.o"
	@echo "... src/CoreNode.i"
	@echo "... src/CoreNode.s"
	@echo "... src/CoreWrapper.o"
	@echo "... src/CoreWrapper.i"
	@echo "... src/CoreWrapper.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /root/autodl-tmp/rtab_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

