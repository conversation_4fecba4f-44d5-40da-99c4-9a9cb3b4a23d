# generated from genmsg/cmake/pkg-genmsg.cmake.em

message(STATUS "semantic_perception: 3 messages, 1 services")

set(MSG_I_FLAGS "-Isemantic_perception:/root/autodl-tmp/rtab_ws/src/semantic_perception/msg;-Istd_msgs:/opt/ros/noetic/share/std_msgs/cmake/../msg;-Isensor_msgs:/opt/ros/noetic/share/sensor_msgs/cmake/../msg;-Igeometry_msgs:/opt/ros/noetic/share/geometry_msgs/cmake/../msg;-Ivision_msgs:/opt/ros/noetic/share/vision_msgs/cmake/../msg")

# Find all generators
find_package(gencpp REQUIRED)
find_package(geneus REQUIRED)
find_package(genlisp REQUIRED)
find_package(gennodejs REQUIRED)
find_package(genpy REQUIRED)

add_custom_target(semantic_perception_generate_messages ALL)

# verify that message/service dependencies have not changed since configure



get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg" NAME_WE)
add_custom_target(_semantic_perception_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_perception" "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg" "geometry_msgs/Vector3:geometry_msgs/Point:sensor_msgs/Image:std_msgs/Header"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg" NAME_WE)
add_custom_target(_semantic_perception_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_perception" "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg" "sensor_msgs/Image:sensor_msgs/CameraInfo:std_msgs/Header:sensor_msgs/RegionOfInterest"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg" NAME_WE)
add_custom_target(_semantic_perception_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_perception" "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg" "sensor_msgs/Image:geometry_msgs/Point:geometry_msgs/Vector3:std_msgs/Header:geometry_msgs/Quaternion"
)

get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv" NAME_WE)
add_custom_target(_semantic_perception_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "semantic_perception" "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv" "sensor_msgs/Image:geometry_msgs/Point:geometry_msgs/Vector3:semantic_perception/SemanticObject:std_msgs/Header:geometry_msgs/Quaternion"
)

#
#  langs = gencpp;geneus;genlisp;gennodejs;genpy
#

### Section generating for lang: gencpp
### Generating Messages
_generate_msg_cpp(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_perception
)
_generate_msg_cpp(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_perception
)
_generate_msg_cpp(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_perception
)

### Generating Services
_generate_srv_cpp(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_perception
)

### Generating Module File
_generate_module_cpp(semantic_perception
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_perception
  "${ALL_GEN_OUTPUT_FILES_cpp}"
)

add_custom_target(semantic_perception_generate_messages_cpp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_cpp}
)
add_dependencies(semantic_perception_generate_messages semantic_perception_generate_messages_cpp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_cpp _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_cpp _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_cpp _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv" NAME_WE)
add_dependencies(semantic_perception_generate_messages_cpp _semantic_perception_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_perception_gencpp)
add_dependencies(semantic_perception_gencpp semantic_perception_generate_messages_cpp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_perception_generate_messages_cpp)

### Section generating for lang: geneus
### Generating Messages
_generate_msg_eus(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_perception
)
_generate_msg_eus(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_perception
)
_generate_msg_eus(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_perception
)

### Generating Services
_generate_srv_eus(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_perception
)

### Generating Module File
_generate_module_eus(semantic_perception
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_perception
  "${ALL_GEN_OUTPUT_FILES_eus}"
)

add_custom_target(semantic_perception_generate_messages_eus
  DEPENDS ${ALL_GEN_OUTPUT_FILES_eus}
)
add_dependencies(semantic_perception_generate_messages semantic_perception_generate_messages_eus)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_eus _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_eus _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_eus _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv" NAME_WE)
add_dependencies(semantic_perception_generate_messages_eus _semantic_perception_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_perception_geneus)
add_dependencies(semantic_perception_geneus semantic_perception_generate_messages_eus)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_perception_generate_messages_eus)

### Section generating for lang: genlisp
### Generating Messages
_generate_msg_lisp(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_perception
)
_generate_msg_lisp(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_perception
)
_generate_msg_lisp(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_perception
)

### Generating Services
_generate_srv_lisp(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_perception
)

### Generating Module File
_generate_module_lisp(semantic_perception
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_perception
  "${ALL_GEN_OUTPUT_FILES_lisp}"
)

add_custom_target(semantic_perception_generate_messages_lisp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_lisp}
)
add_dependencies(semantic_perception_generate_messages semantic_perception_generate_messages_lisp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_lisp _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_lisp _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_lisp _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv" NAME_WE)
add_dependencies(semantic_perception_generate_messages_lisp _semantic_perception_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_perception_genlisp)
add_dependencies(semantic_perception_genlisp semantic_perception_generate_messages_lisp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_perception_generate_messages_lisp)

### Section generating for lang: gennodejs
### Generating Messages
_generate_msg_nodejs(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_perception
)
_generate_msg_nodejs(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_perception
)
_generate_msg_nodejs(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_perception
)

### Generating Services
_generate_srv_nodejs(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_perception
)

### Generating Module File
_generate_module_nodejs(semantic_perception
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_perception
  "${ALL_GEN_OUTPUT_FILES_nodejs}"
)

add_custom_target(semantic_perception_generate_messages_nodejs
  DEPENDS ${ALL_GEN_OUTPUT_FILES_nodejs}
)
add_dependencies(semantic_perception_generate_messages semantic_perception_generate_messages_nodejs)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_nodejs _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_nodejs _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_nodejs _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv" NAME_WE)
add_dependencies(semantic_perception_generate_messages_nodejs _semantic_perception_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_perception_gennodejs)
add_dependencies(semantic_perception_gennodejs semantic_perception_generate_messages_nodejs)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_perception_generate_messages_nodejs)

### Section generating for lang: genpy
### Generating Messages
_generate_msg_py(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_perception
)
_generate_msg_py(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/CameraInfo.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/sensor_msgs/cmake/../msg/RegionOfInterest.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_perception
)
_generate_msg_py(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_perception
)

### Generating Services
_generate_srv_py(semantic_perception
  "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/sensor_msgs/cmake/../msg/Image.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg;/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Quaternion.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_perception
)

### Generating Module File
_generate_module_py(semantic_perception
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_perception
  "${ALL_GEN_OUTPUT_FILES_py}"
)

add_custom_target(semantic_perception_generate_messages_py
  DEPENDS ${ALL_GEN_OUTPUT_FILES_py}
)
add_dependencies(semantic_perception_generate_messages semantic_perception_generate_messages_py)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticDetection.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_py _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticSegmentation.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_py _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/msg/SemanticObject.msg" NAME_WE)
add_dependencies(semantic_perception_generate_messages_py _semantic_perception_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/root/autodl-tmp/rtab_ws/src/semantic_perception/srv/GetSemanticMap.srv" NAME_WE)
add_dependencies(semantic_perception_generate_messages_py _semantic_perception_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(semantic_perception_genpy)
add_dependencies(semantic_perception_genpy semantic_perception_generate_messages_py)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS semantic_perception_generate_messages_py)



if(gencpp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_perception)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/semantic_perception
    DESTINATION ${gencpp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_cpp)
  add_dependencies(semantic_perception_generate_messages_cpp std_msgs_generate_messages_cpp)
endif()
if(TARGET sensor_msgs_generate_messages_cpp)
  add_dependencies(semantic_perception_generate_messages_cpp sensor_msgs_generate_messages_cpp)
endif()
if(TARGET geometry_msgs_generate_messages_cpp)
  add_dependencies(semantic_perception_generate_messages_cpp geometry_msgs_generate_messages_cpp)
endif()
if(TARGET vision_msgs_generate_messages_cpp)
  add_dependencies(semantic_perception_generate_messages_cpp vision_msgs_generate_messages_cpp)
endif()

if(geneus_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_perception)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/semantic_perception
    DESTINATION ${geneus_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_eus)
  add_dependencies(semantic_perception_generate_messages_eus std_msgs_generate_messages_eus)
endif()
if(TARGET sensor_msgs_generate_messages_eus)
  add_dependencies(semantic_perception_generate_messages_eus sensor_msgs_generate_messages_eus)
endif()
if(TARGET geometry_msgs_generate_messages_eus)
  add_dependencies(semantic_perception_generate_messages_eus geometry_msgs_generate_messages_eus)
endif()
if(TARGET vision_msgs_generate_messages_eus)
  add_dependencies(semantic_perception_generate_messages_eus vision_msgs_generate_messages_eus)
endif()

if(genlisp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_perception)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/semantic_perception
    DESTINATION ${genlisp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_lisp)
  add_dependencies(semantic_perception_generate_messages_lisp std_msgs_generate_messages_lisp)
endif()
if(TARGET sensor_msgs_generate_messages_lisp)
  add_dependencies(semantic_perception_generate_messages_lisp sensor_msgs_generate_messages_lisp)
endif()
if(TARGET geometry_msgs_generate_messages_lisp)
  add_dependencies(semantic_perception_generate_messages_lisp geometry_msgs_generate_messages_lisp)
endif()
if(TARGET vision_msgs_generate_messages_lisp)
  add_dependencies(semantic_perception_generate_messages_lisp vision_msgs_generate_messages_lisp)
endif()

if(gennodejs_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_perception)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/semantic_perception
    DESTINATION ${gennodejs_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_nodejs)
  add_dependencies(semantic_perception_generate_messages_nodejs std_msgs_generate_messages_nodejs)
endif()
if(TARGET sensor_msgs_generate_messages_nodejs)
  add_dependencies(semantic_perception_generate_messages_nodejs sensor_msgs_generate_messages_nodejs)
endif()
if(TARGET geometry_msgs_generate_messages_nodejs)
  add_dependencies(semantic_perception_generate_messages_nodejs geometry_msgs_generate_messages_nodejs)
endif()
if(TARGET vision_msgs_generate_messages_nodejs)
  add_dependencies(semantic_perception_generate_messages_nodejs vision_msgs_generate_messages_nodejs)
endif()

if(genpy_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_perception)
  install(CODE "execute_process(COMMAND \"/root/miniconda3/bin/python3\" -m compileall \"${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_perception\")")
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/semantic_perception
    DESTINATION ${genpy_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_py)
  add_dependencies(semantic_perception_generate_messages_py std_msgs_generate_messages_py)
endif()
if(TARGET sensor_msgs_generate_messages_py)
  add_dependencies(semantic_perception_generate_messages_py sensor_msgs_generate_messages_py)
endif()
if(TARGET geometry_msgs_generate_messages_py)
  add_dependencies(semantic_perception_generate_messages_py geometry_msgs_generate_messages_py)
endif()
if(TARGET vision_msgs_generate_messages_py)
  add_dependencies(semantic_perception_generate_messages_py vision_msgs_generate_messages_py)
endif()
