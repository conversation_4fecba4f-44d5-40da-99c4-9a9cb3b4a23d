#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH='/root/autodl-tmp/rtab_ws/devel:/opt/ros/noetic'
export LD_LIBRARY_PATH='/opt/ros/noetic/share/euslisp/jskeus/eus//Linux64/lib:/root/autodl-tmp/rtab_ws/devel/lib:/opt/ros/noetic/lib:/opt/ros/noetic/lib/x86_64-linux-gnu:/opt/ros/noetic/share/euslisp/jskeus/eus//Linux64/lib:/opt/ros/noetic/share/euslisp/jskeus/eus//Linux64/lib'
export PATH='/opt/ros/noetic/share/euslisp/jskeus/eus//Linux64/bin:/opt/ros/noetic/bin:/opt/ros/noetic/share/euslisp/jskeus/eus//Linux64/bin:/root/miniconda3/bin:/usr/local/bin:/usr/local/nvidia/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'
export PKG_CONFIG_PATH="/root/autodl-tmp/rtab_ws/devel/lib/pkgconfig:$PKG_CONFIG_PATH"
export PWD='/root/autodl-tmp/rtab_ws/build'
export PYTHONPATH='/root/autodl-tmp/rtab_ws/devel/lib/python3/dist-packages:/opt/ros/noetic/lib/python3/dist-packages:/usr/lib/python3/dist-packages'
export ROS_PACKAGE_PATH="/root/autodl-tmp/rtab_ws/src:$ROS_PACKAGE_PATH"