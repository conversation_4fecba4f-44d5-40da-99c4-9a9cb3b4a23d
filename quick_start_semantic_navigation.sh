#!/bin/bash

# 语义导航系统启动脚本
# 功能：启动完整的语义导航避障系统
# 作者：语义SLAM增强项目 - 阶段2扩展
# 版本：2.1

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查ROS环境
check_ros_environment() {
    log_step "检查ROS环境..."
    
    if [ -z "$ROS_DISTRO" ]; then
        log_error "ROS环境未设置，请先source ROS setup文件"
        exit 1
    fi
    
    if [ ! -f "devel/setup.bash" ]; then
        log_error "工作空间未编译，请先运行 catkin_make"
        exit 1
    fi
    
    log_info "ROS环境检查通过 (ROS $ROS_DISTRO)"
}

# 检查依赖节点
check_dependencies() {
    log_step "检查依赖节点..."
    
    # 检查roscore
    if ! pgrep -f "roscore" > /dev/null; then
        log_error "roscore未运行，请先启动roscore"
        exit 1
    fi
    
    # 检查语义地图节点
    if ! rostopic list | grep -q "/semantic_mapping/enhanced_pointcloud"; then
        log_warn "语义地图节点未检测到，请确保语义建图系统正在运行"
    fi
    
    # 检查火焰检测节点
    if ! rostopic list | grep -q "/semantic_perception/fire_locations_3d"; then
        log_warn "火焰检测节点未检测到，请确保火焰检测系统正在运行"
    fi
    
    log_info "依赖检查完成"
}

# 设置环境
setup_environment() {
    log_step "设置环境..."
    
    # Source工作空间
    source devel/setup.bash
    
    # 设置ROS参数
    export ROS_MASTER_URI=http://localhost:11311
    export ROS_HOSTNAME=localhost
    
    # 创建日志目录
    mkdir -p logs/semantic_navigation
    
    log_info "环境设置完成"
}

# 启动语义代价地图转换器
start_costmap_converter() {
    log_step "启动语义代价地图转换器..."
    
    gnome-terminal --tab --title="语义代价地图转换器" -- bash -c "
        source devel/setup.bash
        echo -e '${CYAN}🗺️ 启动语义代价地图转换器${NC}'
        rosrun semantic_navigation semantic_costmap_converter.py \
            _resolution:=0.1 \
            _width:=1000 \
            _height:=1000 \
            _origin_x:=-50.0 \
            _origin_y:=-50.0 \
            _inflation_radius:=0.5 \
            _fire_safety_radius:=2.0 \
            2>&1 | tee logs/semantic_navigation/costmap_converter.log
        exec bash
    "
    
    sleep 3
    log_info "语义代价地图转换器启动完成"
}

# 启动语义路径规划器
start_path_planner() {
    log_step "启动语义路径规划器..."
    
    gnome-terminal --tab --title="语义路径规划器" -- bash -c "
        source devel/setup.bash
        echo -e '${PURPLE}🗺️ 启动语义路径规划器${NC}'
        rosrun semantic_navigation semantic_path_planner.py \
            _fire_safety_distance:=2.0 \
            _grid_resolution:=0.1 \
            _robot_radius:=0.3 \
            2>&1 | tee logs/semantic_navigation/path_planner.log
        exec bash
    "
    
    sleep 3
    log_info "语义路径规划器启动完成"
}

# 启动语义避障控制器
start_obstacle_avoidance() {
    log_step "启动语义避障控制器..."
    
    gnome-terminal --tab --title="语义避障控制器" -- bash -c "
        source devel/setup.bash
        echo -e '${YELLOW}🚗 启动语义避障控制器${NC}'
        rosrun semantic_navigation semantic_obstacle_avoidance.py \
            _max_speed:=1.0 \
            _max_angular_speed:=1.0 \
            _robot_radius:=0.3 \
            _fire_safety_distance:=2.0 \
            _min_obstacle_distance:=0.5 \
            _goal_tolerance:=0.5 \
            _path_lookahead_distance:=1.0 \
            2>&1 | tee logs/semantic_navigation/obstacle_avoidance.log
        exec bash
    "
    
    sleep 3
    log_info "语义避障控制器启动完成"
}

# 启动导航控制器
start_navigation_controller() {
    log_step "启动语义导航控制器..."
    
    gnome-terminal --tab --title="语义导航控制器" -- bash -c "
        source devel/setup.bash
        echo -e '${GREEN}🎮 启动语义导航控制器${NC}'
        rosrun semantic_navigation semantic_navigation_controller.py \
            _goal_tolerance:=0.5 \
            _planning_timeout:=10.0 \
            _max_navigation_time:=300.0 \
            _fire_safety_distance:=2.0 \
            _emergency_stop_distance:=1.0 \
            _replanning_frequency:=1.0 \
            _path_deviation_threshold:=1.0 \
            2>&1 | tee logs/semantic_navigation/navigation_controller.log
        exec bash
    "
    
    sleep 3
    log_info "语义导航控制器启动完成"
}

# 启动RViz可视化
start_rviz_visualization() {
    log_step "启动RViz可视化..."
    
    # 检查是否有RViz配置文件
    RVIZ_CONFIG="src/semantic_navigation/rviz/semantic_navigation.rviz"
    if [ ! -f "$RVIZ_CONFIG" ]; then
        log_warn "RViz配置文件不存在，使用默认配置"
        RVIZ_CONFIG=""
    fi
    
    gnome-terminal --tab --title="RViz可视化" -- bash -c "
        source devel/setup.bash
        echo -e '${CYAN}👁️ 启动RViz可视化${NC}'
        if [ -n '$RVIZ_CONFIG' ]; then
            rviz -d $RVIZ_CONFIG
        else
            rviz
        fi
        exec bash
    "
    
    sleep 2
    log_info "RViz可视化启动完成"
}

# 启动导航测试工具
start_navigation_test_tool() {
    log_step "启动导航测试工具..."
    
    gnome-terminal --tab --title="导航测试工具" -- bash -c "
        source devel/setup.bash
        echo -e '${BLUE}🧪 导航测试工具${NC}'
        echo '使用以下命令测试导航功能:'
        echo ''
        echo '1. 设置导航目标:'
        echo '   rosservice call /set_navigation_goal \"goal: {header: {frame_id: \\\"map\\\"}, pose: {position: {x: 5.0, y: 3.0, z: 0.0}, orientation: {w: 1.0}}}\"'
        echo ''
        echo '2. 应急停止:'
        echo '   rosservice call /emergency_stop_service \"stop: true, reason: \\\"手动停止\\\"\"'
        echo ''
        echo '3. 恢复导航:'
        echo '   rosservice call /emergency_stop_service \"stop: false, reason: \\\"手动恢复\\\"\"'
        echo ''
        echo '4. 查看导航状态:'
        echo '   rostopic echo /navigation_state'
        echo ''
        echo '5. 查看规划路径:'
        echo '   rostopic echo /planned_path'
        echo ''
        echo -e '${GREEN}系统已就绪，可以开始导航测试！${NC}'
        exec bash
    "
    
    log_info "导航测试工具启动完成"
}

# 显示系统状态
show_system_status() {
    log_step "显示系统状态..."
    
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}  语义导航系统状态${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
    
    # 检查节点状态
    echo -e "${YELLOW}活跃节点:${NC}"
    rosnode list | grep -E "(semantic_|navigation)" | while read node; do
        echo -e "  ✅ $node"
    done
    echo ""
    
    # 检查话题状态
    echo -e "${YELLOW}活跃话题:${NC}"
    rostopic list | grep -E "(semantic_|navigation|planned_path|cmd_vel)" | while read topic; do
        echo -e "  📡 $topic"
    done
    echo ""
    
    # 检查服务状态
    echo -e "${YELLOW}可用服务:${NC}"
    rosservice list | grep -E "(semantic_|navigation)" | while read service; do
        echo -e "  🔧 $service"
    done
    echo ""
    
    echo -e "${GREEN}系统启动完成！${NC}"
    echo -e "${BLUE}请在RViz中查看可视化效果${NC}"
    echo -e "${PURPLE}使用导航测试工具进行功能测试${NC}"
}

# 主函数
main() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}  语义导航避障系统启动器${NC}"
    echo -e "${CYAN}  版本: 2.1${NC}"
    echo -e "${CYAN}================================${NC}"
    echo ""
    
    # 执行启动步骤
    check_ros_environment
    check_dependencies
    setup_environment
    
    echo ""
    log_info "开始启动语义导航系统组件..."
    echo ""
    
    # 按顺序启动各个组件
    start_costmap_converter
    start_path_planner
    start_obstacle_avoidance
    start_navigation_controller
    
    # 启动可视化和测试工具
    start_rviz_visualization
    start_navigation_test_tool
    
    # 等待所有组件启动
    sleep 5
    
    # 显示系统状态
    show_system_status
    
    echo ""
    log_info "语义导航避障系统启动完成！"
    echo -e "${GREEN}🎉 所有组件已成功启动${NC}"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo -e "  1. 确保语义建图和火焰检测系统正在运行"
    echo -e "  2. 在RViz中添加相关显示项查看可视化"
    echo -e "  3. 使用测试工具验证导航功能"
    echo -e "  4. 遇到问题请查看各组件的日志文件"
    echo ""
}

# 信号处理
cleanup() {
    log_warn "接收到退出信号，正在清理..."
    # 这里可以添加清理代码
    exit 0
}

trap cleanup SIGINT SIGTERM

# 执行主函数
main "$@"
