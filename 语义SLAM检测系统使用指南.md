# 🎯 语义SLAM检测系统完整使用指南

## 📋 系统概述

这是一个基于RTAB-Map + TSDF + YOLO的真实语义检测系统，能够：
- 实时检测和标记室内物体
- 生成3D语义地图
- 在RViz中显示彩色语义标记
- 提供检测统计和分析

## 🚀 快速启动流程

### 1. 系统启动顺序（必须按顺序执行）

```bash
# 第一步：启动RTAB-Map + TSDF融合建图
cd /root/autodl-tmp/rtab_ws
./stage_2_rtab_tsdf.sh
# 等待系统完全启动（约30秒）

# 第二步：启动语义感知系统
./quick_start_enhanced_semantic.sh
# 选择模式3（高精度语义建图模式）

# 第三步：启动真实物体检测器
python3 real_object_detector.py

# 第四步：启动语义点云发布器
python3 real_semantic_pointcloud_publisher.py

# 第五步：修复YOLO图像输入（重要！）
python3 fix_yolo_image_remap.py
```

### 2. 验证系统状态

```bash
# 检查所有关键话题是否有数据
rostopic hz /darknet_ros/bounding_boxes              # YOLO检测
rostopic hz /real_object_detection/detected_objects  # 物体检测
rostopic hz /real_semantic_mapping/semantic_regions  # 语义区域
rostopic hz /tsdf_fusion_node/tsdf_pointcloud       # TSDF点云
```

## 🖥️ RViz配置步骤

### 1. 基础设置
- **Fixed Frame**: `map`
- **Target Frame**: `<Fixed Frame>`

### 2. 添加显示组件

#### A. 真实物体检测标记
```
Display Type: MarkerArray
Display Name: Real Object Detection
Topic: /real_object_detection/detected_objects
Queue Size: 100
```

#### B. 语义区域标记
```
Display Type: MarkerArray  
Display Name: Semantic Regions
Topic: /real_semantic_mapping/semantic_regions
Queue Size: 100
```

#### C. 检测统计信息
```
Display Type: MarkerArray
Display Name: Detection Statistics  
Topic: /real_object_detection/statistics
Queue Size: 100
```

#### D. TSDF点云（可选）
```
Display Type: PointCloud2
Display Name: TSDF Pointcloud
Topic: /tsdf_fusion_node/tsdf_pointcloud
Size (m): 0.01
Style: Points
Color Transformer: Intensity
```

#### E. 语义增强点云（可选）
```
Display Type: PointCloud2
Display Name: Semantic Enhanced Pointcloud
Topic: /real_semantic_mapping/enhanced_pointcloud
Size (m): 0.01
Style: Points
Color Transformer: RGB8
```

### 3. 视角调整
- 按 `R` 键重置视角
- 使用鼠标滚轮缩放到合适范围
- 拖拽调整视角到能看到检测标记的位置

## 🎨 预期显示效果

### 彩色语义标记
- 🔴 **红色球体**: 人 (person)
- 🟢 **绿色球体**: 椅子 (chair)
- 🔵 **蓝色球体**: 桌子 (dining table)
- 🟡 **黄色球体**: 沙发 (couch)
- 🟣 **紫色球体**: 电视 (tv)
- 🟦 **青色球体**: 笔记本电脑 (laptop)
- 🟠 **橙色球体**: 瓶子 (bottle)
- 其他颜色：杯子、书籍、手机等

### 附加信息
- **白色文本标签**: 显示物体中文名称和置信度
- **半透明球体**: 语义影响区域
- **统计信息**: 左上角显示实时检测统计

## 🔧 系统监控和调试

### 1. 实时监控命令

```bash
# 检查YOLO检测频率和内容
rostopic hz /darknet_ros/bounding_boxes
rostopic echo /darknet_ros/bounding_boxes --noarr -n 1

# 检查物体检测结果
rostopic echo /real_object_detection/detected_objects -n 1

# 查看检测统计
rostopic echo /real_object_detection/statistics --noarr -n 1

# 检查系统状态
rosnode list | grep -E "(darknet|real_object|real_semantic)"
```

### 2. 性能优化

```bash
# 检查CPU和GPU使用率
htop
nvidia-smi

# 检查话题发布频率
rostopic hz /camera/color/image_raw
rostopic hz /stereo_camera/left/image_rect_color
```

## ⚙️ 参数调整

### 1. YOLO检测参数
编辑YOLO配置文件调整：
- 置信度阈值
- NMS阈值  
- 检测类别

### 2. 物体检测参数
在 `real_object_detector.py` 中调整：
- 深度过滤范围
- 中心区域比例
- 坐标变换参数

### 3. 语义映射参数
在 `real_semantic_pointcloud_publisher.py` 中调整：
- 影响半径
- 颜色映射
- 发布频率

## 🚨 常见问题解决

### 问题1：看不到语义标记
**解决方案**：
1. 检查Fixed Frame是否设置为 `map`
2. 按 `R` 键重置RViz视角
3. 确认话题名称正确
4. 检查MarkerArray显示是否启用

### 问题2：YOLO检测无数据
**解决方案**：
1. 确保运行了 `fix_yolo_image_remap.py`
2. 检查图像话题是否有数据
3. 重启YOLO节点

### 问题3：检测精度低
**解决方案**：
1. 改善场景光照条件
2. 确保目标物体清晰可见
3. 调整YOLO置信度阈值
4. 检查相机标定参数

### 问题4：系统运行缓慢
**解决方案**：
1. 降低图像分辨率
2. 减少检测频率
3. 优化YOLO模型
4. 检查GPU使用情况

## 📊 系统状态检查清单

- [ ] RTAB-Map正常运行
- [ ] TSDF点云有数据
- [ ] YOLO检测有输出
- [ ] 图像重映射正常
- [ ] 物体检测器工作
- [ ] 语义点云发布
- [ ] RViz显示正常

## 🎯 使用技巧

1. **最佳检测效果**：确保场景光照充足，物体清晰可见
2. **性能优化**：根据需要调整检测频率和精度
3. **调试模式**：使用统计信息监控系统性能
4. **保存配置**：在RViz中保存显示配置以便重复使用

## 📞 技术支持

如遇到问题，请：
1. 检查终端错误输出
2. 验证话题数据流
3. 确认系统资源使用情况
4. 参考本指南的故障排除部分
