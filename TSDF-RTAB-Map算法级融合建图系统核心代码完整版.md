# TSDF-RTAB-Map算法级融合建图系统核心代码

## 1. 位姿订阅中心模块 (pose_subscription_center.cpp)

```cpp
#include <ros/ros.h>
#include <nav_msgs/Odometry.h>
#include <geometry_msgs/TransformStamped.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_ros/buffer.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <tf2/LinearMath/Transform.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <mutex>
#include <Eigen/Dense>
#include <Eigen/Geometry>

class PoseSubscriptionCenter
{
public:
    PoseSubscriptionCenter() : 
        tf_buffer_(ros::Duration(10.0)),
        tf_listener_(tf_buffer_),
        last_pose_time_(0.0),
        pose_quality_threshold_(0.1)
    {
        ros::NodeHandle nh;
        ros::NodeHandle pnh("~");
        
        pnh.param<std::string>("source_frame", source_frame_, "map");
        pnh.param<std::string>("target_frame", target_frame_, "base_link");
        pnh.param<double>("publish_rate", publish_rate_, 30.0);
        pnh.param<double>("pose_quality_threshold", pose_quality_threshold_, 0.1);
        pnh.param<bool>("enable_pose_filtering", enable_pose_filtering_, true);
        pnh.param<bool>("use_odom_backup", use_odom_backup_, true);
        pnh.param<bool>("enable_coordinate_correction", enable_coordinate_correction_, true);
        pnh.param<std::string>("camera_frame", camera_frame_, "zed_left_camera_optical_frame");
        pnh.param<bool>("apply_optical_to_mechanical_transform", apply_optical_to_mechanical_transform_, true);

        odom_pub_ = nh.advertise<nav_msgs::Odometry>("/pose_center/odom", 10);

        if (use_odom_backup_) {
            rtabmap_odom_sub_ = nh.subscribe("/rtabmap/odom", 10,
                                           &PoseSubscriptionCenter::rtabmapOdomCallback, this);
            ROS_INFO("✅ 启用odom话题备份订阅: /rtabmap/odom");
        }
        
        timer_ = nh.createTimer(ros::Duration(1.0 / publish_rate_), 
                               &PoseSubscriptionCenter::timerCallback, this);

        initializeCoordinateTransforms();
    }

    void timerCallback(const ros::TimerEvent& event) {
        nav_msgs::Odometry odom_msg;
        odom_msg.header.stamp = ros::Time::now();
        odom_msg.header.frame_id = source_frame_;
        odom_msg.child_frame_id = target_frame_;

        bool pose_obtained = false;

        if (use_odom_backup_) {
            std::lock_guard<std::mutex> lock(odom_mutex_);
            if (latest_rtabmap_odom_) {
                odom_msg.pose = latest_rtabmap_odom_->pose;
                odom_msg.twist = latest_rtabmap_odom_->twist;
                pose_obtained = true;
                pose_source_ = "rtabmap_odom";
            }
        }

        if (!pose_obtained) {
            if (getTFPose(odom_msg)) {
                pose_obtained = true;
                pose_source_ = "tf_transform";
            }
        }

        if (pose_obtained) {
            if (enable_coordinate_correction_) {
                applyCoordinateCorrection(odom_msg);
            }

            if (!enable_pose_filtering_ || isPoseValid(odom_msg)) {
                odom_pub_.publish(odom_msg);
                last_pose_time_ = odom_msg.header.stamp.toSec();
                pose_count_++;

                if (pose_count_ % 100 == 0) {
                    ROS_INFO("🎯 位姿订阅中心: 已发布%d个位姿，来源=%s", 
                             pose_count_, pose_source_.c_str());
                }
            }
        } else {
            ROS_WARN_THROTTLE(5.0, "⚠️ 位姿订阅中心: 无法获取有效位姿数据");
        }
    }

    void rtabmapOdomCallback(const nav_msgs::Odometry::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(odom_mutex_);
        latest_rtabmap_odom_ = msg;
        ROS_DEBUG("📡 接收到RTAB-Map位姿数据");
    }

    bool getTFPose(nav_msgs::Odometry& odom_msg) {
        try {
            geometry_msgs::TransformStamped transform = tf_buffer_.lookupTransform(
                source_frame_, target_frame_, ros::Time(0), ros::Duration(0.1));

            odom_msg.pose.pose.position.x = transform.transform.translation.x;
            odom_msg.pose.pose.position.y = transform.transform.translation.y;
            odom_msg.pose.pose.position.z = transform.transform.translation.z;
            odom_msg.pose.pose.orientation = transform.transform.rotation;

            return true;
        } catch (tf2::TransformException& ex) {
            ROS_DEBUG("TF查询失败: %s", ex.what());
            return false;
        }
    }

    void applyCoordinateCorrection(nav_msgs::Odometry& odom_msg) {
        if (!apply_optical_to_mechanical_transform_) return;

        Eigen::Quaterniond q(
            odom_msg.pose.pose.orientation.w,
            odom_msg.pose.pose.orientation.x,
            odom_msg.pose.pose.orientation.y,
            odom_msg.pose.pose.orientation.z
        );

        Eigen::Matrix3d optical_to_mechanical;
        optical_to_mechanical << 0, 0, 1,
                                -1, 0, 0,
                                 0,-1, 0;

        Eigen::Matrix3d corrected_rotation = q.toRotationMatrix() * optical_to_mechanical;
        Eigen::Quaterniond corrected_q(corrected_rotation);

        odom_msg.pose.pose.orientation.w = corrected_q.w();
        odom_msg.pose.pose.orientation.x = corrected_q.x();
        odom_msg.pose.pose.orientation.y = corrected_q.y();
        odom_msg.pose.pose.orientation.z = corrected_q.z();
    }

    bool isPoseValid(const nav_msgs::Odometry& odom_msg) {
        double current_time = odom_msg.header.stamp.toSec();
        
        if (last_pose_time_ > 0) {
            double time_diff = current_time - last_pose_time_;
            if (time_diff > 1.0) {
                ROS_WARN("位姿时间跳跃过大: %.3f秒", time_diff);
                return false;
            }
        }

        double position_magnitude = sqrt(
            pow(odom_msg.pose.pose.position.x, 2) +
            pow(odom_msg.pose.pose.position.y, 2) +
            pow(odom_msg.pose.pose.position.z, 2)
        );

        if (position_magnitude > 1000.0) {
            ROS_WARN("位姿位置异常: %.3f", position_magnitude);
            return false;
        }

        return true;
    }

    void initializeCoordinateTransforms() {
        ROS_INFO("🔧 初始化坐标系变换配置");
        ROS_INFO("   源坐标系: %s", source_frame_.c_str());
        ROS_INFO("   目标坐标系: %s", target_frame_.c_str());
        ROS_INFO("   相机坐标系: %s", camera_frame_.c_str());
        ROS_INFO("   光学到机械变换: %s", apply_optical_to_mechanical_transform_ ? "启用" : "禁用");
    }

private:
    ros::Publisher odom_pub_;
    ros::Subscriber rtabmap_odom_sub_;
    ros::Timer timer_;

    tf2_ros::Buffer tf_buffer_;
    tf2_ros::TransformListener tf_listener_;

    std::string source_frame_;
    std::string target_frame_;
    std::string camera_frame_;
    double publish_rate_;
    double pose_quality_threshold_;
    bool enable_pose_filtering_;
    bool use_odom_backup_;
    bool enable_coordinate_correction_;
    bool apply_optical_to_mechanical_transform_;

    nav_msgs::Odometry::ConstPtr latest_rtabmap_odom_;
    std::mutex odom_mutex_;
    std::string pose_source_;

    double last_pose_time_;
    int pose_count_ = 0;
};

int main(int argc, char** argv)
{
    ros::init(argc, argv, "pose_subscription_center");
    
    try {
        PoseSubscriptionCenter center;
        
        ROS_INFO("🚀 位姿订阅中心节点启动完成");
        ros::spin();
        
    } catch (const std::exception& e) {
        ROS_ERROR("位姿订阅中心启动失败: %s", e.what());
        return -1;
    }
    
    return 0;
}
```

## 2. TSDF融合算法模块 (tsdf_fusion.cpp)

```cpp
#include "tsdf_mapping/tsdf_fusion.h"
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include <sensor_msgs/PointCloud2.h>
#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>
#include <cv_bridge/cv_bridge.h>
#include <image_geometry/pinhole_camera_model.h>
#include <tf2_eigen/tf2_eigen.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>

namespace tsdf_mapping {

TSDFFusion::TSDFFusion(ros::NodeHandle& nh) : 
    nh_(nh),
    tf_buffer_(ros::Duration(10.0)),
    tf_listener_(tf_buffer_),
    voxel_size_(0.05),
    truncation_distance_(0.3),
    max_weight_(100.0),
    use_rtabmap_pose_(true),
    enable_rtabmap_collaboration_(true),
    enable_gpu_acceleration_(false),
    gpu_initialized_(false)
{
    ROS_INFO("🚀 TSDF融合算法模块初始化");
}

bool TSDFFusion::initialize() {
    ros::NodeHandle pnh("~");
    
    pnh.param<double>("voxel_size", voxel_size_, 0.05);
    pnh.param<double>("truncation_distance", truncation_distance_, 0.3);
    pnh.param<double>("max_weight", max_weight_, 100.0);
    pnh.param<bool>("use_rtabmap_pose", use_rtabmap_pose_, true);
    pnh.param<bool>("enable_rtabmap_collaboration", enable_rtabmap_collaboration_, true);
    pnh.param<bool>("enable_gpu_acceleration", enable_gpu_acceleration_, false);

    pointcloud_pub_ = nh_.advertise<sensor_msgs::PointCloud2>("/tsdf_mapping/pointcloud", 1);
    marker_pub_ = nh_.advertise<visualization_msgs::MarkerArray>("/tsdf_mapping/voxel_markers", 1);
    performance_pub_ = nh_.advertise<std_msgs::String>("/tsdf_mapping/performance_stats", 1);

    initializeDepthPointcloudTransform();
    initializeQualityControl();
    validateCoordinateConfiguration();
    initializeRTABMapCollaboration();

    if (enable_gpu_acceleration_) {
        if (initializeGPUAcceleration()) {
            ROS_INFO("🚀 GPU加速TSDF初始化成功，保持算法级融合架构");
        } else {
            ROS_WARN("⚠️ GPU加速初始化失败，回退到CPU模式");
            enable_gpu_acceleration_ = false;
        }
    }

    return true;
}

void TSDFFusion::processRGBD(const sensor_msgs::Image::ConstPtr& rgb_msg,
                             const sensor_msgs::Image::ConstPtr& depth_msg,
                             const sensor_msgs::CameraInfo::ConstPtr& camera_info_msg) {
    try {
        cv_bridge::CvImagePtr rgb_ptr = cv_bridge::toCvCopy(rgb_msg, sensor_msgs::image_encodings::BGR8);
        cv_bridge::CvImagePtr depth_ptr = cv_bridge::toCvCopy(depth_msg, sensor_msgs::image_encodings::TYPE_16UC1);

        cv::Mat rgb_image = rgb_ptr->image;
        cv::Mat depth_image = depth_ptr->image;

        Eigen::Matrix4f camera_pose = Eigen::Matrix4f::Identity();

        if (use_rtabmap_pose_ && enable_rtabmap_collaboration_) {
            if (!getRTABMapPose(camera_pose, rgb_msg->header.stamp)) {
                ROS_WARN_THROTTLE(1.0, "无法获取RTAB-Map位姿，跳过当前帧");
                return;
            }
        } else {
            if (!getCameraPoseFromTF(camera_pose, rgb_msg->header.stamp)) {
                ROS_WARN_THROTTLE(1.0, "无法获取相机位姿，跳过当前帧");
                return;
            }
        }

        if (enable_gpu_acceleration_ && gpu_initialized_) {
            processRGBDWithGPU(rgb_image, depth_image, camera_pose);
        } else {
            processRGBDWithCPU(rgb_image, depth_image, camera_pose, camera_info_msg);
        }

        publishVisualization();

    } catch (cv_bridge::Exception& e) {
        ROS_ERROR("CV Bridge异常: %s", e.what());
    } catch (std::exception& e) {
        ROS_ERROR("TSDF处理异常: %s", e.what());
    }
}

bool TSDFFusion::getRTABMapPose(Eigen::Matrix4f& pose, const ros::Time& timestamp) {
    std::lock_guard<std::mutex> lock(pose_mutex_);

    if (!latest_rtabmap_pose_) {
        return false;
    }

    double time_diff = fabs((timestamp - latest_rtabmap_pose_->header.stamp).toSec());
    if (time_diff > 0.1) {
        ROS_WARN_THROTTLE(1.0, "RTAB-Map位姿时间差过大: %.3f秒", time_diff);
        return false;
    }

    geometry_msgs::Pose pose_msg = latest_rtabmap_pose_->pose.pose;

    Eigen::Translation3f translation(
        pose_msg.position.x,
        pose_msg.position.y,
        pose_msg.position.z
    );

    Eigen::Quaternionf rotation(
        pose_msg.orientation.w,
        pose_msg.orientation.x,
        pose_msg.orientation.y,
        pose_msg.orientation.z
    );

    pose = (translation * rotation).matrix();
    return true;
}

void TSDFFusion::processRGBDWithCPU(const cv::Mat& rgb_image,
                                   const cv::Mat& depth_image,
                                   const Eigen::Matrix4f& camera_pose,
                                   const sensor_msgs::CameraInfo::ConstPtr& camera_info) {

    image_geometry::PinholeCameraModel camera_model;
    camera_model.fromCameraInfo(camera_info);

    for (int v = 0; v < depth_image.rows; v += 2) {
        for (int u = 0; u < depth_image.cols; u += 2) {
            uint16_t depth_raw = depth_image.at<uint16_t>(v, u);
            if (depth_raw == 0) continue;

            float depth = depth_raw * 0.001f;
            if (depth < 0.1f || depth > 10.0f) continue;

            cv::Point2d pixel_point(u, v);
            cv::Point3d ray = camera_model.projectPixelTo3dRay(pixel_point);

            Eigen::Vector3f point_camera(
                ray.x * depth,
                ray.y * depth,
                ray.z * depth
            );

            Eigen::Vector4f point_camera_h(point_camera.x(), point_camera.y(), point_camera.z(), 1.0f);
            Eigen::Vector4f point_world_h = camera_pose * point_camera_h;
            Eigen::Vector3f point_world = point_world_h.head<3>();

            cv::Vec3b color = rgb_image.at<cv::Vec3b>(v, u);
            updateVoxel(point_world, color, depth);
        }
    }
}

void TSDFFusion::updateVoxel(const Eigen::Vector3f& point, const cv::Vec3b& color, float depth) {
    VoxelIndex voxel_idx = worldToVoxel(point);

    float sdf_value = computeSDF(point, depth);
    if (fabs(sdf_value) > truncation_distance_) return;

    float weight = calculateAdaptiveWeight(sdf_value, depth);

    auto it = tsdf_volume_.find(voxel_idx);
    if (it == tsdf_volume_.end()) {
        TSDFVoxel voxel;
        voxel.tsdf_value = sdf_value;
        voxel.weight = weight;
        voxel.r = color[2];
        voxel.g = color[1];
        voxel.b = color[0];
        tsdf_volume_[voxel_idx] = voxel;
    } else {
        TSDFVoxel& voxel = it->second;
        float old_tsdf = voxel.tsdf_value;

        float weight_decay = 0.95f;
        voxel.weight *= weight_decay;

        float total_weight = voxel.weight + weight;
        if (total_weight > max_weight_) {
            weight = max_weight_ - voxel.weight;
            total_weight = max_weight_;
        }

        voxel.tsdf_value = (voxel.tsdf_value * voxel.weight + sdf_value * weight) / total_weight;
        voxel.weight = total_weight;

        float color_weight = weight / total_weight;
        voxel.r = static_cast<uint8_t>(voxel.r * (1.0f - color_weight) + color[2] * color_weight);
        voxel.g = static_cast<uint8_t>(voxel.g * (1.0f - color_weight) + color[1] * color_weight);
        voxel.b = static_cast<uint8_t>(voxel.b * (1.0f - color_weight) + color[0] * color_weight);
    }
}

VoxelIndex TSDFFusion::worldToVoxel(const Eigen::Vector3f& point) {
    return VoxelIndex(
        static_cast<int>(std::floor(point.x() / voxel_size_)),
        static_cast<int>(std::floor(point.y() / voxel_size_)),
        static_cast<int>(std::floor(point.z() / voxel_size_))
    );
}

float TSDFFusion::computeSDF(const Eigen::Vector3f& point, float depth) {
    float distance_to_surface = depth - point.norm();
    return std::max(-truncation_distance_, std::min(truncation_distance_, distance_to_surface));
}

float TSDFFusion::calculateAdaptiveWeight(float sdf_value, float depth) const {
    float base_weight = 1.0f;

    float depth_factor = std::exp(-depth * 0.1f);
    float sdf_factor = 1.0f - std::abs(sdf_value) / truncation_distance_;

    return base_weight * depth_factor * sdf_factor;
}

void TSDFFusion::initializeRTABMapCollaboration() {
    if (!use_rtabmap_pose_ || !enable_rtabmap_collaboration_) {
        ROS_INFO("RTAB-Map协作已禁用");
        return;
    }

    std::string selected_topic = "/pose_center/odom";
    rtabmap_odom_sub_ = nh_.subscribe(selected_topic, 10,
                                     &TSDFFusion::rtabmapOdomCallback, this);
    ROS_INFO("✅ 已启用位姿订阅中心融合，订阅话题: %s", selected_topic.c_str());
    ROS_INFO("  use_rtabmap_pose: %s", use_rtabmap_pose_ ? "是" : "否");
    ROS_INFO("  enable_collaboration: %s", enable_rtabmap_collaboration_ ? "是" : "否");
    ROS_INFO("  位姿数据源: RTAB-Map优化位姿（算法级融合）");
}

void TSDFFusion::rtabmapOdomCallback(const nav_msgs::Odometry::ConstPtr& msg) {
    std::lock_guard<std::mutex> lock(pose_mutex_);
    latest_rtabmap_pose_ = msg;
}

void TSDFFusion::publishVisualization() {
    publishPointCloud();
    publishVoxelMarkers();
    publishPerformanceStats();
}

void TSDFFusion::publishPointCloud() {
    pcl::PointCloud<pcl::PointXYZRGB>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGB>);

    for (const auto& pair : tsdf_volume_) {
        const VoxelIndex& idx = pair.first;
        const TSDFVoxel& voxel = pair.second;

        if (std::abs(voxel.tsdf_value) < 0.1f && voxel.weight > 5.0f) {
            pcl::PointXYZRGB point;
            point.x = (idx.x + 0.5f) * voxel_size_;
            point.y = (idx.y + 0.5f) * voxel_size_;
            point.z = (idx.z + 0.5f) * voxel_size_;
            point.r = voxel.r;
            point.g = voxel.g;
            point.b = voxel.b;
            cloud->points.push_back(point);
        }
    }

    sensor_msgs::PointCloud2 cloud_msg;
    pcl::toROSMsg(*cloud, cloud_msg);
    cloud_msg.header.frame_id = "map";
    cloud_msg.header.stamp = ros::Time::now();
    pointcloud_pub_.publish(cloud_msg);
}

} // namespace tsdf_mapping

## 3. GPU加速TSDF模块 (tsdf_cuda.cu)

```cuda
#include "tsdf_mapping/tsdf_cuda.h"
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <iostream>
#include <chrono>
#include <thread>

namespace tsdf_mapping {

TSDFCuda::TSDFCuda() :
    d_voxel_volume_(nullptr),
    d_depth_image_(nullptr),
    d_rgb_image_(nullptr),
    d_camera_pose_(nullptr),
    compute_stream_(0),
    transfer_stream_(0),
    voxel_volume_size_(0),
    initialized_(false),
    last_migration_origin_x_(0.0f),
    last_migration_origin_y_(0.0f),
    last_migration_origin_z_(0.0f),
    migration_distance_threshold_(100.0f),
    volume_origin_initialized_(false) {

    perf_stats_ = {};

    gpu_health_.gpu_healthy_ = true;
    gpu_health_.last_check_time_ = 0.0;
    gpu_health_.consecutive_failures_ = 0;
    gpu_health_.recovery_attempted_ = false;

    kernel_config_.block_size_ = dim3(16, 16);
    kernel_config_.max_retries_ = 3;
    kernel_config_.timeout_ms_ = 500.0;
    kernel_config_.use_async_execution_ = true;

    perf_monitor_.successful_frames_ = 0;
    perf_monitor_.failed_frames_ = 0;
    perf_monitor_.avg_processing_time_ms_ = 0.0;
    perf_monitor_.last_success_time_ = 0.0;

    cudaEventCreate(&start_event_);
    cudaEventCreate(&stop_event_);
    cudaEventCreate(&perf_monitor_.start_event_);
    cudaEventCreate(&perf_monitor_.completion_event_);

    std::cout << "🚀 GPU加速TSDF：构造函数完成，保持算法级融合架构，增强错误处理" << std::endl;
}

TSDFCuda::~TSDFCuda() {
    cleanupMigrationBuffers();
    deallocateGPUMemory();
    cleanupCudaStreams();

    cudaEventDestroy(start_event_);
    cudaEventDestroy(stop_event_);

    std::cout << "🚀 GPU加速TSDF：析构函数完成" << std::endl;
}

bool TSDFCuda::initialize(const CudaTSDFParams& params, const CudaCameraParams& camera_params) {
    std::cout << "🚀 GPU加速TSDF：开始初始化..." << std::endl;

    tsdf_params_ = params;
    camera_params_ = camera_params;

    voxel_volume_size_ = params.volume_size_x * params.volume_size_y * params.volume_size_z;

    std::cout << "🚀 TSDF参数：" << std::endl;
    std::cout << "   体素大小: " << params.voxel_size << "m" << std::endl;
    std::cout << "   截断距离: " << params.truncation_distance << "m" << std::endl;
    std::cout << "   体积大小: " << params.volume_size_x << "x" << params.volume_size_y << "x" << params.volume_size_z << std::endl;
    std::cout << "   总体素数: " << voxel_volume_size_ << std::endl;

    if (!allocateGPUMemory()) {
        std::cerr << "❌ GPU内存分配失败" << std::endl;
        return false;
    }

    if (!setupCudaStreams()) {
        std::cerr << "❌ CUDA流设置失败" << std::endl;
        return false;
    }

    if (!initializeMigrationBuffers()) {
        std::cerr << "❌ 迁移缓冲区初始化失败" << std::endl;
        return false;
    }

    initialized_ = true;
    std::cout << "✅ GPU加速TSDF初始化成功，保持算法级融合架构" << std::endl;

    return true;
}

bool TSDFCuda::allocateGPUMemory() {
    std::cout << "🚀 分配GPU内存..." << std::endl;

    size_t free_mem, total_mem;
    cudaMemGetInfo(&free_mem, &total_mem);
    std::cout << "   GPU内存: " << free_mem / (1024*1024) << "MB 可用 / " << total_mem / (1024*1024) << "MB 总计" << std::endl;

    size_t voxel_memory_size = voxel_volume_size_ * sizeof(CudaTSDFVoxel);
    cudaError_t err = cudaMalloc(&d_voxel_volume_, voxel_memory_size);
    if (err != cudaSuccess) {
        std::cerr << "❌ 体素体积内存分配失败: " << cudaGetErrorString(err) << std::endl;
        return false;
    }

    size_t depth_memory_size = camera_params_.width * camera_params_.height * sizeof(float);
    err = cudaMalloc(&d_depth_image_, depth_memory_size);
    if (err != cudaSuccess) {
        std::cerr << "❌ 深度图像内存分配失败: " << cudaGetErrorString(err) << std::endl;
        return false;
    }

    size_t rgb_memory_size = camera_params_.width * camera_params_.height * 3 * sizeof(uint8_t);
    err = cudaMalloc(&d_rgb_image_, rgb_memory_size);
    if (err != cudaSuccess) {
        std::cerr << "❌ RGB图像内存分配失败: " << cudaGetErrorString(err) << std::endl;
        return false;
    }

    std::cout << "   体素体积: " << voxel_memory_size / (1024*1024) << "MB" << std::endl;
    std::cout << "   深度图像: " << depth_memory_size / (1024*1024) << "MB" << std::endl;
    std::cout << "   RGB图像: " << rgb_memory_size / (1024*1024) << "MB" << std::endl;

    err = cudaMalloc(&d_camera_pose_, 16 * sizeof(float));
    if (err != cudaSuccess) {
        std::cerr << "❌ 相机位姿内存分配失败: " << cudaGetErrorString(err) << std::endl;
        return false;
    }

    err = cudaMemset(d_voxel_volume_, 0, voxel_memory_size);
    if (err != cudaSuccess) {
        std::cerr << "❌ 体素体积初始化失败: " << cudaGetErrorString(err) << std::endl;
        return false;
    }

    std::cout << "✅ GPU内存分配成功" << std::endl;
    return true;
}

bool TSDFCuda::setupCudaStreams() {
    std::cout << "🚀 设置CUDA流..." << std::endl;

    cudaError_t err = cudaStreamCreate(&compute_stream_);
    if (err != cudaSuccess) {
        std::cerr << "❌ 计算流创建失败: " << cudaGetErrorString(err) << std::endl;
        return false;
    }

    err = cudaStreamCreate(&transfer_stream_);
    if (err != cudaSuccess) {
        std::cerr << "❌ 传输流创建失败: " << cudaGetErrorString(err) << std::endl;
        return false;
    }

    std::cout << "✅ CUDA流设置成功" << std::endl;
    return true;
}

bool TSDFCuda::processRGBD(const cv::Mat& rgb_image,
                          const cv::Mat& depth_image,
                          const Eigen::Matrix4f& camera_pose) {
    if (!initialized_) {
        std::cerr << "❌ GPU TSDF未初始化" << std::endl;
        return false;
    }

    if (!checkGPUHealth()) {
        std::cerr << "❌ GPU健康检查失败，尝试恢复" << std::endl;
        if (!attemptGPURecovery()) {
            std::cerr << "❌ GPU恢复失败，建议切换到CPU模式" << std::endl;
            return false;
        }
    }

    cudaEventRecord(start_event_, compute_stream_);

    std::cout << "🎯 GPU加速处理：使用位姿订阅中心的变换矩阵，保持算法级融合" << std::endl;

    float pose_data[16];
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 4; j++) {
            pose_data[i * 4 + j] = camera_pose(i, j);
        }
    }

    cudaError_t err = cudaMemcpyAsync(d_camera_pose_, pose_data, 16 * sizeof(float),
                                     cudaMemcpyHostToDevice, transfer_stream_);
    if (err != cudaSuccess) {
        std::cerr << "❌ 相机位姿传输失败: " << cudaGetErrorString(err) << std::endl;
        return false;
    }

    if (!launchDepthPreprocessing(depth_image)) {
        std::cerr << "❌ 深度预处理失败" << std::endl;
        return false;
    }

    std::cout << "🔧 [DEBUG] 开始启动TSDF内核..." << std::endl;
    cudaError_t kernel_result = launchTSDFKernelSafe();
    std::cout << "🔧 [DEBUG] TSDF内核启动返回: " << cudaGetErrorString(kernel_result) << std::endl;
    if (kernel_result != cudaSuccess) {
        handleKernelLaunchError(kernel_result);

        const int max_retries = kernel_config_.max_retries_;
        for (int retry = 0; retry < max_retries; retry++) {
            std::cout << "⚠️ 重试GPU内核启动 (尝试 " << (retry+1) << "/" << max_retries << ")" << std::endl;
            kernel_result = launchTSDFKernelSafe();
            if (kernel_result == cudaSuccess) break;

            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }

        if (kernel_result != cudaSuccess) {
            std::cerr << "❌ GPU内核启动最终失败" << std::endl;
            return false;
        }
    }

    cudaEventRecord(stop_event_, compute_stream_);
    cudaEventSynchronize(stop_event_);

    float processing_time;
    cudaEventElapsedTime(&processing_time, start_event_, stop_event_);

    updatePerformanceStats(true, processing_time);

    std::cout << "✅ GPU处理完成，耗时: " << processing_time << "ms" << std::endl;
    return true;
}

__global__ void updateTSDFVoxelsKernel(
    float* depth_data,
    uint8_t* rgb_data,
    float* camera_pose,
    CudaTSDFVoxel* voxel_volume,
    CudaTSDFParams tsdf_params,
    CudaCameraParams camera_params) {

    int u = blockIdx.x * blockDim.x + threadIdx.x;
    int v = blockIdx.y * blockDim.y + threadIdx.y;

    if (u >= camera_params.width || v >= camera_params.height) return;

    int pixel_idx = v * camera_params.width + u;
    float depth = depth_data[pixel_idx];

    if (depth <= 0.1f || depth > 10.0f || !isfinite(depth)) return;

    float x_cam = (u - camera_params.cx) * depth / camera_params.fx;
    float y_cam = (v - camera_params.cy) * depth / camera_params.fy;
    float z_cam = depth;

    float x_world = camera_pose[0] * x_cam + camera_pose[1] * y_cam + camera_pose[2] * z_cam + camera_pose[3];
    float y_world = camera_pose[4] * x_cam + camera_pose[5] * y_cam + camera_pose[6] * z_cam + camera_pose[7];
    float z_world = camera_pose[8] * x_cam + camera_pose[9] * y_cam + camera_pose[10] * z_cam + camera_pose[11];

    int vx = __float2int_rd((x_world - tsdf_params.volume_origin_x) / tsdf_params.voxel_size);
    int vy = __float2int_rd((y_world - tsdf_params.volume_origin_y) / tsdf_params.voxel_size);
    int vz = __float2int_rd((z_world - tsdf_params.volume_origin_z) / tsdf_params.voxel_size);

    if (vx >= 0 && vx < tsdf_params.volume_size_x &&
        vy >= 0 && vy < tsdf_params.volume_size_y &&
        vz >= 0 && vz < tsdf_params.volume_size_z) {

        int volume_idx = vz * tsdf_params.volume_size_x * tsdf_params.volume_size_y +
                        vy * tsdf_params.volume_size_x + vx;

        float voxel_x = tsdf_params.volume_origin_x + (vx + 0.5f) * tsdf_params.voxel_size;
        float voxel_y = tsdf_params.volume_origin_y + (vy + 0.5f) * tsdf_params.voxel_size;
        float voxel_z = tsdf_params.volume_origin_z + (vz + 0.5f) * tsdf_params.voxel_size;

        float distance_to_surface = depth - sqrtf(voxel_x*voxel_x + voxel_y*voxel_y + voxel_z*voxel_z);
        float sdf = fmaxf(-1.0f, fminf(1.0f, distance_to_surface / tsdf_params.truncation_distance));

        if (fabsf(sdf) <= 1.0f) {
            float weight = 1.0f - fabsf(sdf);
            weight = fmaxf(0.1f, weight);

            CudaTSDFVoxel* voxel = &voxel_volume[volume_idx];

            float old_weight = atomicAdd(&voxel->weight, weight);
            float total_weight = old_weight + weight;

            if (threadIdx.x == 0 && threadIdx.y == 0 && blockIdx.x == 0 && blockIdx.y == 0) {
                printf("GPU内核：更新体素(%d,%d,%d), SDF=%.3f, 权重=%.3f, 旧权重=%.3f\n",
                       vx, vy, vz, sdf, weight, old_weight);
            }

            if (total_weight > 0.0f) {
                float new_tsdf = (old_weight * voxel->tsdf_value + weight * sdf) / total_weight;
                voxel->tsdf_value = new_tsdf;

                int rgb_idx = pixel_idx * 3;
                uint8_t r = rgb_data[rgb_idx + 2];
                uint8_t g = rgb_data[rgb_idx + 1];
                uint8_t b = rgb_data[rgb_idx + 0];

                float color_weight = weight / total_weight;
                voxel->r = (uint8_t)(voxel->r * (1.0f - color_weight) + r * color_weight);
                voxel->g = (uint8_t)(voxel->g * (1.0f - color_weight) + g * color_weight);
                voxel->b = (uint8_t)(voxel->b * (1.0f - color_weight) + b * color_weight);

                voxel->world_x = voxel_x;
                voxel->world_y = voxel_y;
                voxel->world_z = voxel_z;
            }
        }
    }
}

} // namespace tsdf_mapping
```

## 4. TSDF融合节点启动器 (tsdf_fusion_node.cpp)

```cpp
#include "tsdf_mapping/tsdf_fusion.h"
#include <message_filters/subscriber.h>
#include <message_filters/time_synchronizer.h>
#include <message_filters/sync_policies/approximate_time.h>
#include <message_filters/sync_policies/exact_time.h>
#include <boost/thread/mutex.hpp>
#include <rosgraph_msgs/Clock.h>
#include <ros/topic.h>

class TSDFFusionNode {
public:
    TSDFFusionNode() : nh_("~"), use_manual_sync_(false), sync_timeout_(0.1) {
        tsdf_fusion_ = std::make_unique<tsdf_mapping::TSDFFusion>(nh_);

        if (!tsdf_fusion_->initialize()) {
            ROS_ERROR("TSDF融合器初始化失败");
            return;
        }

        checkSimTimeStatus();
        setupSubscribers();

        ROS_INFO("🔧 TSDF Fusion Node started - 消息同步修复版");
    }

private:
    ros::NodeHandle nh_;
    std::unique_ptr<tsdf_mapping::TSDFFusion> tsdf_fusion_;

    std::unique_ptr<message_filters::Subscriber<sensor_msgs::Image>> depth_sub_;
    std::unique_ptr<message_filters::Subscriber<sensor_msgs::Image>> rgb_sub_;
    std::unique_ptr<message_filters::Subscriber<sensor_msgs::CameraInfo>> camera_info_sub_filter_;

    typedef message_filters::sync_policies::ApproximateTime<sensor_msgs::Image, sensor_msgs::Image, sensor_msgs::CameraInfo> ApproxSyncPolicy;
    std::unique_ptr<message_filters::Synchronizer<ApproxSyncPolicy>> approx_sync_;

    bool use_manual_sync_;
    double sync_timeout_;
    int callback_count_ = 0;
    double total_process_time_ = 0.0;
    int process_count_ = 0;
    double current_publish_rate_ = 30.0;

    void checkSimTimeStatus() {
        bool use_sim_time = false;
        nh_.param("/use_sim_time", use_sim_time, false);

        if (use_sim_time) {
            ROS_INFO("🔧 检测到仿真时间模式，调整同步策略");
            sync_timeout_ = 1.0;
        } else {
            ROS_INFO("🔧 使用系统时间模式");
            sync_timeout_ = 0.1;
        }
    }

    void setupSubscribers() {
        std::string depth_topic = "/camera/depth/image_raw";
        std::string rgb_topic = "/camera/color/image_raw";
        std::string camera_info_topic = "/camera/color/camera_info";

        nh_.param<std::string>("depth_topic", depth_topic, depth_topic);
        nh_.param<std::string>("rgb_topic", rgb_topic, rgb_topic);
        nh_.param<std::string>("camera_info_topic", camera_info_topic, camera_info_topic);

        ROS_INFO("🔧 订阅话题配置:");
        ROS_INFO("   深度图像: %s", depth_topic.c_str());
        ROS_INFO("   RGB图像: %s", rgb_topic.c_str());
        ROS_INFO("   相机信息: %s", camera_info_topic.c_str());

        depth_sub_ = std::make_unique<message_filters::Subscriber<sensor_msgs::Image>>(nh_, depth_topic, 10);
        rgb_sub_ = std::make_unique<message_filters::Subscriber<sensor_msgs::Image>>(nh_, rgb_topic, 10);
        camera_info_sub_filter_ = std::make_unique<message_filters::Subscriber<sensor_msgs::CameraInfo>>(nh_, camera_info_topic, 10);

        approx_sync_ = std::make_unique<message_filters::Synchronizer<ApproxSyncPolicy>>(
            ApproxSyncPolicy(10), *rgb_sub_, *depth_sub_, *camera_info_sub_filter_);

        approx_sync_->setAgePenalty(100.0);
        approx_sync_->registerCallback(boost::bind(&TSDFFusionNode::syncCallback, this, _1, _2, _3));

        ROS_INFO("✅ 消息同步器设置完成，同步超时: %.1fs", sync_timeout_);
    }

    void syncCallback(const sensor_msgs::Image::ConstPtr& rgb_msg,
                     const sensor_msgs::Image::ConstPtr& depth_msg,
                     const sensor_msgs::CameraInfo::ConstPtr& camera_info_msg) {

        callback_count_++;
        ros::Time process_start = ros::Time::now();

        ROS_INFO_THROTTLE(2.0, "🔧 同步回调 #%d: RGB时间=%.3f, 深度时间=%.3f, 相机信息时间=%.3f",
                         callback_count_,
                         rgb_msg->header.stamp.toSec(),
                         depth_msg->header.stamp.toSec(),
                         camera_info_msg->header.stamp.toSec());

        double rgb_depth_diff = fabs((rgb_msg->header.stamp - depth_msg->header.stamp).toSec());
        if (rgb_depth_diff > sync_timeout_) {
            ROS_WARN("RGB-深度时间差过大: %.3fs", rgb_depth_diff);
            return;
        }

        ROS_INFO_THROTTLE(3.0, "🎯 准备调用processRGBD，保持算法级融合架构");

        tsdf_fusion_->processRGBD(rgb_msg, depth_msg, camera_info_msg);

        ROS_INFO_THROTTLE(5.0, "🔧 立即发布可视化数据...");
        tsdf_fusion_->publishVisualization();

        double process_time = (ros::Time::now() - process_start).toSec();
        updatePerformanceMetrics(process_time);

        if (callback_count_ % 10 == 0) {
            double avg_process_time = total_process_time_ / process_count_;
            ROS_INFO("TSDF性能统计: 回调#%d, 平均处理时间=%.3fs, 当前发布频率=%.1fHz",
                     callback_count_, avg_process_time, current_publish_rate_);
        }
    }

    void updatePerformanceMetrics(double process_time) {
        total_process_time_ += process_time;
        process_count_++;

        if (process_count_ > 100) {
            total_process_time_ *= 0.9;
            process_count_ = static_cast<int>(process_count_ * 0.9);
        }

        double avg_time = total_process_time_ / process_count_;
        current_publish_rate_ = std::min(30.0, 1.0 / avg_time);
    }
};

int main(int argc, char** argv) {
    ros::init(argc, argv, "tsdf_fusion_node");

    try {
        TSDFFusionNode node;

        ROS_INFO("🚀 TSDF融合节点启动完成");
        ros::spin();

    } catch (const std::exception& e) {
        ROS_ERROR("TSDF融合节点启动失败: %s", e.what());
        return -1;
    }

    return 0;
}
```

## 5. TSDF CUDA头文件 (tsdf_cuda.h)

```cpp
#ifndef TSDF_CUDA_H
#define TSDF_CUDA_H

#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <thrust/device_vector.h>
#include <thrust/host_vector.h>

#include <opencv2/opencv.hpp>
#include <Eigen/Dense>
#include <memory>
#include <vector>

namespace tsdf_mapping {

struct __align__(32) CudaTSDFVoxel {
    float tsdf_value;
    float weight;
    uint8_t r, g, b;
    uint8_t padding;

    float world_x, world_y, world_z;
    float padding2;

    __device__ __host__ CudaTSDFVoxel() :
        tsdf_value(0.0f), weight(0.0f), r(0), g(0), b(0), padding(0),
        world_x(0.0f), world_y(0.0f), world_z(0.0f), padding2(0.0f) {}
};

struct CudaVoxelIndex {
    int x, y, z;

    __device__ __host__ CudaVoxelIndex(int x_ = 0, int y_ = 0, int z_ = 0) :
        x(x_), y(y_), z(z_) {}

    __device__ __host__ bool operator==(const CudaVoxelIndex& other) const {
        return x == other.x && y == other.y && z == other.z;
    }
};

struct CudaCameraParams {
    float fx, fy, cx, cy;
    float depth_min, depth_max;
    int width, height;
};

struct CudaTSDFParams {
    float voxel_size;
    float truncation_distance;
    float max_weight;
    int volume_size_x, volume_size_y, volume_size_z;
    float volume_origin_x, volume_origin_y, volume_origin_z;
};

struct PerformanceStats {
    int processed_frames;
    double total_processing_time_ms;
    double average_fps;
    double gpu_process_time_ms;
    double memory_usage_mb;
    int successful_kernels;
    int failed_kernels;
};

struct GPUHealthMonitor {
    bool gpu_healthy_;
    double last_check_time_;
    int consecutive_failures_;
    bool recovery_attempted_;
};

struct KernelConfiguration {
    dim3 block_size_;
    int max_retries_;
    double timeout_ms_;
    bool use_async_execution_;
};

struct PerformanceMonitor {
    int successful_frames_;
    int failed_frames_;
    double avg_processing_time_ms_;
    double last_success_time_;
    cudaEvent_t start_event_;
    cudaEvent_t completion_event_;
};

struct VoxelMigrationManager {
    CudaTSDFVoxel* d_migration_buffer_;
    bool* d_migration_mask_;
    int* d_migration_mapping_;
    size_t migration_buffer_size_;
    bool migration_in_progress_;
    double last_migration_time_;
    int migration_count_;
};

class TSDFCuda {
public:
    TSDFCuda();
    ~TSDFCuda();

    bool initialize(const CudaTSDFParams& params, const CudaCameraParams& camera_params);

    bool processRGBD(const cv::Mat& rgb_image,
                     const cv::Mat& depth_image,
                     const Eigen::Matrix4f& camera_pose);

    std::vector<CudaTSDFVoxel> getVoxelData() const;
    PerformanceStats getPerformanceStats() const;

    bool isInitialized() const { return initialized_; }

    void setVolumeOrigin(float x, float y, float z);
    bool migrateVolume(float new_origin_x, float new_origin_y, float new_origin_z);

    void enableDynamicVolumeManagement(bool enable);
    void setMigrationThreshold(float threshold);

private:
    CudaTSDFVoxel* d_voxel_volume_;
    float* d_depth_image_;
    uint8_t* d_rgb_image_;
    float* d_camera_pose_;

    CudaTSDFParams tsdf_params_;
    CudaCameraParams camera_params_;

    cudaStream_t compute_stream_;
    cudaStream_t transfer_stream_;

    PerformanceStats perf_stats_;
    cudaEvent_t start_event_, stop_event_;

    size_t voxel_volume_size_;
    bool initialized_;

    VoxelMigrationManager migration_manager_;
    float last_migration_origin_x_, last_migration_origin_y_, last_migration_origin_z_;
    float migration_distance_threshold_;
    bool volume_origin_initialized_;

    GPUHealthMonitor gpu_health_;
    KernelConfiguration kernel_config_;
    PerformanceMonitor perf_monitor_;

    bool allocateGPUMemory();
    void deallocateGPUMemory();
    bool setupCudaStreams();
    void cleanupCudaStreams();

    bool checkGPUHealth();
    bool attemptGPURecovery();
    void handleKernelLaunchError(cudaError_t error);
    void updatePerformanceStats(bool success, double processing_time_ms);

    cudaError_t launchTSDFKernelSafe();

    bool initializeMigrationBuffers();
    void cleanupMigrationBuffers();
    bool allocateMigrationMemory();
    void deallocateMigrationMemory();
    void updateMigrationStats(bool success, double migration_time_ms);
    void logMigrationStats();

    bool launchDepthPreprocessing(const cv::Mat& depth_image);
    bool launchVoxelUpdate(const cv::Mat& rgb_image,
                          const Eigen::Matrix4f& camera_pose);
    bool synchronizeGPU();
};

__global__ void preprocessDepthKernel(
    float* input_depth,
    float* output_depth,
    int width, int height,
    float depth_min, float depth_max
);

__global__ void updateTSDFVoxelsKernel(
    float* depth_data,
    uint8_t* rgb_data,
    float* camera_pose,
    CudaTSDFVoxel* voxel_volume,
    CudaTSDFParams tsdf_params,
    CudaCameraParams camera_params
);

__global__ void generatePointCloudKernel(
    CudaTSDFVoxel* voxel_volume,
    float* output_points,
    uint8_t* output_colors,
    int* output_count,
    CudaTSDFParams tsdf_params,
    float surface_threshold
);

__device__ CudaVoxelIndex worldToVoxel(
    float x, float y, float z,
    const CudaTSDFParams& params
);

__device__ void voxelToWorld(
    const CudaVoxelIndex& voxel_idx,
    const CudaTSDFParams& params,
    float& x, float& y, float& z
);

__device__ float computeSDF(
    float voxel_x, float voxel_y, float voxel_z,
    float surface_x, float surface_y, float surface_z,
    float camera_x, float camera_y, float camera_z,
    float truncation_distance
);

__global__ void detectMigrationVoxelsKernel(
    CudaTSDFVoxel* voxel_volume,
    bool* migration_mask,
    CudaTSDFParams old_params,
    CudaTSDFParams new_params,
    int total_voxels
);

__global__ void transformVoxelCoordinatesKernel(
    CudaTSDFVoxel* source_volume,
    CudaTSDFVoxel* target_volume,
    int* migration_mapping,
    bool* migration_mask,
    CudaTSDFParams old_params,
    CudaTSDFParams new_params,
    int total_voxels
);

} // namespace tsdf_mapping

#endif // TSDF_CUDA_H
```
