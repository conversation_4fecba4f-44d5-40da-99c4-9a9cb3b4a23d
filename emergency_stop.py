#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
应急停止控制工具
功能：快速停止或恢复语义导航系统
作者：语义SLAM增强项目 - 阶段2扩展
版本：2.1
"""

import rospy
import sys
import argparse
from semantic_navigation.srv import EmergencyStop, EmergencyStopRequest

def emergency_stop_control(stop=True, reason="用户手动操作"):
    """应急停止控制"""
    rospy.init_node('emergency_stop_tool', anonymous=True)
    
    # 等待服务
    action = "停止" if stop else "恢复"
    print(f"⏳ 等待应急{action}服务...")
    
    try:
        rospy.wait_for_service('emergency_stop_service', timeout=10)
        print(f"✅ 应急{action}服务已就绪")
    except rospy.ROSException:
        print(f"❌ 应急{action}服务不可用，请确保语义导航系统正在运行")
        return False
    
    # 创建服务客户端
    emergency_client = rospy.ServiceProxy('emergency_stop_service', EmergencyStop)
    
    try:
        # 创建请求
        request = EmergencyStopRequest()
        request.stop = stop
        request.reason = reason
        
        if stop:
            print(f"🚨 执行应急停止: {reason}")
        else:
            print(f"✅ 恢复导航: {reason}")
        
        # 调用服务
        response = emergency_client(request)
        
        if response.success:
            print(f"✅ 操作成功: {response.message}")
            
            if stop:
                print("🛑 机器人已停止，所有运动被禁用")
                print("💡 使用恢复命令重新启动导航")
            else:
                print("🚀 导航系统已恢复，机器人可以继续移动")
                print("💡 系统将重新规划路径到之前的目标")
            
            return True
        else:
            print(f"❌ 操作失败: {response.message}")
            return False
            
    except rospy.ServiceException as e:
        print(f"❌ 服务调用失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='语义导航应急停止控制')
    parser.add_argument('action', choices=['stop', 'resume'], help='操作类型: stop(停止) 或 resume(恢复)')
    parser.add_argument('-r', '--reason', type=str, default='用户手动操作', help='操作原因')
    
    # 如果没有参数，显示帮助和快速操作
    if len(sys.argv) == 1:
        print("🚨 语义导航应急停止控制工具")
        print("版本: 2.1")
        print("")
        print("快速操作:")
        print("  应急停止: python3 emergency_stop.py stop")
        print("  恢复导航: python3 emergency_stop.py resume")
        print("")
        print("带原因说明:")
        print("  python3 emergency_stop.py stop -r '检测到火焰'")
        print("  python3 emergency_stop.py resume -r '威胁已解除'")
        print("")
        parser.print_help()
        
        # 提供交互式选择
        print("\n" + "="*40)
        print("🎮 交互式模式")
        print("="*40)
        
        while True:
            try:
                choice = input("\n请选择操作 (1=停止, 2=恢复, q=退出): ").strip().lower()
                
                if choice == 'q' or choice == 'quit':
                    print("👋 退出")
                    break
                elif choice == '1' or choice == 'stop':
                    reason = input("请输入停止原因 (可选): ").strip()
                    if not reason:
                        reason = "用户手动停止"
                    
                    success = emergency_stop_control(True, reason)
                    if success:
                        print("🎉 应急停止执行成功！")
                    
                elif choice == '2' or choice == 'resume':
                    reason = input("请输入恢复原因 (可选): ").strip()
                    if not reason:
                        reason = "用户手动恢复"
                    
                    success = emergency_stop_control(False, reason)
                    if success:
                        print("🎉 导航恢复成功！")
                        
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出")
                break
            except EOFError:
                print("\n👋 退出")
                break
        
        return
    
    args = parser.parse_args()
    
    print("🚨 语义导航应急停止控制工具")
    print("="*40)
    
    stop_action = (args.action == 'stop')
    success = emergency_stop_control(stop_action, args.reason)
    
    if success:
        if stop_action:
            print("\n🛑 应急停止完成！")
            print("💡 系统状态:")
            print("  - 机器人运动已停止")
            print("  - 所有导航命令被忽略")
            print("  - 安全系统激活")
            print("\n🔧 后续操作:")
            print("  - 检查并排除威胁")
            print("  - 使用恢复命令重启导航")
        else:
            print("\n🚀 导航恢复完成！")
            print("💡 系统状态:")
            print("  - 机器人可以正常移动")
            print("  - 导航功能已恢复")
            print("  - 路径重新规划中")
            print("\n🎯 后续操作:")
            print("  - 系统将自动恢复到之前的目标")
            print("  - 可以设置新的导航目标")
    else:
        print(f"\n❌ 操作失败")
        print("🔧 故障排除:")
        print("  1. 检查语义导航系统是否正在运行")
        print("  2. 确认应急停止服务可用")
        print("  3. 检查ROS网络连接")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
