#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速设置导航目标工具
功能：通过命令行快速设置语义导航目标
作者：语义SLAM增强项目 - 阶段2扩展
版本：2.1
"""

import rospy
import sys
import argparse
from geometry_msgs.msg import PoseStamped
from semantic_navigation.srv import SetNavigationGoal, SetNavigationGoalRequest

def create_goal_pose(x, y, z=0.0, yaw=0.0):
    """创建目标位姿"""
    goal = PoseStamped()
    goal.header.frame_id = "map"
    goal.header.stamp = rospy.Time.now()
    
    goal.pose.position.x = x
    goal.pose.position.y = y
    goal.pose.position.z = z
    
    # 简单的yaw角度转四元数
    goal.pose.orientation.z = yaw
    goal.pose.orientation.w = 1.0
    
    return goal

def set_navigation_goal(x, y, z=0.0, yaw=0.0, emergency_mode=False, timeout=60.0):
    """设置导航目标"""
    rospy.init_node('set_navigation_goal_tool', anonymous=True)
    
    # 等待服务
    print("⏳ 等待导航服务...")
    try:
        rospy.wait_for_service('set_navigation_goal', timeout=10)
        print("✅ 导航服务已就绪")
    except rospy.ROSException:
        print("❌ 导航服务不可用，请确保语义导航系统正在运行")
        return False
    
    # 创建服务客户端
    set_goal_client = rospy.ServiceProxy('set_navigation_goal', SetNavigationGoal)
    
    try:
        # 创建请求
        request = SetNavigationGoalRequest()
        request.goal = create_goal_pose(x, y, z, yaw)
        request.emergency_mode = emergency_mode
        request.timeout = timeout
        
        print(f"🎯 设置导航目标: ({x}, {y}, {z})")
        if emergency_mode:
            print("🚨 应急模式已启用")
        
        # 调用服务
        response = set_goal_client(request)
        
        if response.success:
            print(f"✅ 导航目标设置成功: {response.message}")
            print("📍 机器人将开始导航到目标位置")
            return True
        else:
            print(f"❌ 导航目标设置失败: {response.message}")
            return False
            
    except rospy.ServiceException as e:
        print(f"❌ 服务调用失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='设置语义导航目标')
    parser.add_argument('x', type=float, help='目标X坐标 (米)')
    parser.add_argument('y', type=float, help='目标Y坐标 (米)')
    parser.add_argument('-z', '--z', type=float, default=0.0, help='目标Z坐标 (米，默认0.0)')
    parser.add_argument('--yaw', type=float, default=0.0, help='目标朝向角度 (弧度，默认0.0)')
    parser.add_argument('--emergency', action='store_true', help='启用应急模式')
    parser.add_argument('--timeout', type=float, default=60.0, help='导航超时时间 (秒，默认60)')
    
    # 如果没有参数，显示帮助
    if len(sys.argv) == 1:
        print("🎯 语义导航目标设置工具")
        print("版本: 2.1")
        print("")
        print("使用示例:")
        print("  python3 set_navigation_goal.py 5.0 3.0")
        print("  python3 set_navigation_goal.py 2.0 1.0 --emergency")
        print("  python3 set_navigation_goal.py -1.0 -2.0 --yaw 1.57")
        print("")
        print("预设目标:")
        print("  办公室入口: python3 set_navigation_goal.py 0.0 0.0")
        print("  会议室:     python3 set_navigation_goal.py 5.0 3.0")
        print("  安全出口:   python3 set_navigation_goal.py -2.0 -1.0 --emergency")
        print("")
        parser.print_help()
        return
    
    args = parser.parse_args()
    
    print("🚀 语义导航目标设置工具")
    print("="*40)
    
    success = set_navigation_goal(
        args.x, args.y, args.z, args.yaw, 
        args.emergency, args.timeout
    )
    
    if success:
        print("\n🎉 目标设置完成！")
        print("💡 提示:")
        print("  - 在RViz中查看规划路径")
        print("  - 使用 'rostopic echo /navigation_state' 查看导航状态")
        print("  - 如需停止导航，运行应急停止命令")
    else:
        print("\n❌ 目标设置失败")
        print("🔧 故障排除:")
        print("  1. 检查语义导航系统是否正在运行")
        print("  2. 确认目标位置在地图范围内")
        print("  3. 检查是否有火焰或障碍物阻挡")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
