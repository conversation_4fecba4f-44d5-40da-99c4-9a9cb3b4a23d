#!/bin/bash

# 阶段2语义导航系统快速启动脚本
# 基于现有TSDF+语义检测系统的导航增强
# 作者：语义SLAM增强项目 - 阶段2
# 版本：2.0

echo "🚀 启动阶段2语义导航系统"
echo "基于TSDF+语义检测的智能导航"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查ROS环境
if [ -z "$ROS_MASTER_URI" ]; then
    echo -e "${RED}❌ ROS环境未设置${NC}"
    exit 1
fi

# 设置环境
source devel/setup.bash

echo -e "${BLUE}🔍 检查前置系统状态...${NC}"

# 检查TSDF建图系统
if ! rostopic list 2>/dev/null | grep -q "/tsdf_fusion_node/tsdf_pointcloud"; then
    echo -e "${RED}❌ TSDF建图系统未运行${NC}"
    echo -e "${YELLOW}请先启动: ./stage_2_rtab_tsdf.sh${NC}"
    exit 1
fi
echo -e "${GREEN}✅ TSDF建图系统运行正常${NC}"

# 检查语义检测系统
if ! rostopic list 2>/dev/null | grep -q "/semantic_perception"; then
    echo -e "${RED}❌ 语义检测系统未运行${NC}"
    echo -e "${YELLOW}请先启动: ./quick_start_enhanced_semantic.sh${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 语义检测系统运行正常${NC}"

# 检查实时检测进程
echo -e "${BLUE}🔍 检查实时检测进程...${NC}"
if ! pgrep -f "real_object_detector.py" > /dev/null; then
    echo -e "${YELLOW}⚠️ 实时物体检测器未运行，建议启动${NC}"
fi

if ! pgrep -f "real_semantic_pointcloud_publisher.py" > /dev/null; then
    echo -e "${YELLOW}⚠️ 语义点云发布器未运行，建议启动${NC}"
fi

if ! pgrep -f "fix_yolo_image_remap.py" > /dev/null; then
    echo -e "${YELLOW}⚠️ YOLO图像重映射未运行，建议启动${NC}"
fi

echo -e "${BLUE}🚀 启动增强火焰检测节点...${NC}"
# 在后台启动增强火焰检测
gnome-terminal --tab --title="Enhanced Fire Detection" -- bash -c "
    source devel/setup.bash
    echo '🔥 启动增强火焰检测节点...'
    rosrun semantic_perception enhanced_fire_detection_node.py \
        _processing_rate:=8.0 \
        _confidence_threshold:=0.65 \
        _enable_visualization:=true \
        _enable_emergency_mode:=true \
        _enable_3d_tracking:=true \
        image_raw:=/stereo_camera/left/image_rect_color \
        depth_image:=/stereo_camera/depth/depth_registered \
        camera_info:=/stereo_camera/left/camera_info
    exec bash
" &

sleep 3

echo -e "${BLUE}🎯 启动语义导航管理器...${NC}"
# 在后台启动语义导航管理器
gnome-terminal --tab --title="Semantic Navigation Manager" -- bash -c "
    source devel/setup.bash
    echo '🎯 启动语义导航管理器...'
    rosrun semantic_navigation semantic_navigation_manager.py \
        _fire_safety_distance:=2.0 \
        _emergency_stop_distance:=1.0 \
        _max_navigation_time:=300.0 \
        _status_publish_rate:=2.0 \
        _monitoring_rate:=10.0 \
        robot_pose:=/pose_center/odom
    exec bash
" &

sleep 5

echo -e "${GREEN}🎉 阶段2语义导航系统启动完成！${NC}"
echo "========================================"
echo -e "${BLUE}📋 系统组件状态:${NC}"
echo "🔥 增强火焰检测: 运行中"
echo "🎯 语义导航管理器: 运行中"
echo "🗺️ TSDF建图: 运行中"
echo "🔍 语义检测: 运行中"
echo ""
echo -e "${BLUE}📋 可用功能:${NC}"
echo "🎯 设置导航目标:"
echo "   rosservice call /semantic_navigation/set_navigation_goal ..."
echo ""
echo "🛑 应急停止:"
echo "   rosservice call /semantic_navigation/emergency_stop ..."
echo ""
echo "📊 查看导航状态:"
echo "   rostopic echo /semantic_navigation/status"
echo ""
echo "🔥 查看增强火焰检测:"
echo "   rostopic echo /semantic_perception/enhanced_fire_detections"
echo ""
echo "🌐 查看3D火焰位置:"
echo "   rostopic echo /semantic_perception/fire_locations_3d"
echo ""
echo -e "${BLUE}🧪 运行系统测试:${NC}"
echo "   python3 src/semantic_navigation/scripts/test_semantic_navigation.py"
echo ""
echo -e "${YELLOW}⚠️ 注意事项:${NC}"
echo "• 系统已集成火焰检测和应急响应"
echo "• 检测到火焰时会自动触发应急模式"
echo "• 应急情况下请立即调用emergency_stop服务"
echo "• 建议在安全环境下进行测试"
echo ""
echo -e "${GREEN}✅ 阶段2开发完成 - 火焰检测增强和语义导航框架已就绪${NC}"
echo "========================================"

# 保持脚本运行，显示实时状态
echo -e "${BLUE}📊 实时系统监控 (Ctrl+C 退出):${NC}"
while true; do
    sleep 5
    echo -n "$(date '+%H:%M:%S') - "
    
    # 检查节点状态
    if rostopic list 2>/dev/null | grep -q "/semantic_navigation/status"; then
        echo -n "导航✅ "
    else
        echo -n "导航❌ "
    fi
    
    if rostopic list 2>/dev/null | grep -q "/semantic_perception/enhanced_fire_detections"; then
        echo -n "火焰检测✅ "
    else
        echo -n "火焰检测❌ "
    fi
    
    if rostopic list 2>/dev/null | grep -q "/tsdf_fusion_node/tsdf_pointcloud"; then
        echo -n "TSDF✅ "
    else
        echo -n "TSDF❌ "
    fi
    
    echo ""
done
