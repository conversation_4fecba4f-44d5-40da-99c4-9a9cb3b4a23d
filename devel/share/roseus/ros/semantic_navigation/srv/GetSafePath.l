;; Auto-generated. Do not edit!


(when (boundp 'semantic_navigation::GetSafePath)
  (if (not (find-package "SEMANTIC_NAVIGATION"))
    (make-package "SEMANTIC_NAVIGATION"))
  (shadow 'GetSafePath (find-package "SEMANTIC_NAVIGATION")))
(unless (find-package "SEMANTIC_NAVIGATION::GETSAFEPATH")
  (make-package "SEMANTIC_NAVIGATION::GETSAFEPATH"))
(unless (find-package "SEMANTIC_NAVIGATION::GETSAFEPATHREQUEST")
  (make-package "SEMANTIC_NAVIGATION::GETSAFEPATHREQUEST"))
(unless (find-package "SEMANTIC_NAVIGATION::GETSAFEPATHRESPONSE")
  (make-package "SEMANTIC_NAVIGATION::GETSAFEPATHRESPONSE"))

(in-package "ROS")

(if (not (find-package "GEOMETRY_MSGS"))
  (ros::roseus-add-msgs "geometry_msgs"))




(defclass semantic_navigation::GetSafePathRequest
  :super ros::object
  :slots (_start_pose _goal_pose _hazard_types _safety_margin _emergency_mode ))

(defmethod semantic_navigation::GetSafePathRequest
  (:init
   (&key
    ((:start_pose __start_pose) (instance geometry_msgs::PoseStamped :init))
    ((:goal_pose __goal_pose) (instance geometry_msgs::PoseStamped :init))
    ((:hazard_types __hazard_types) (let (r) (dotimes (i 0) (push "" r)) r))
    ((:safety_margin __safety_margin) 0.0)
    ((:emergency_mode __emergency_mode) nil)
    )
   (send-super :init)
   (setq _start_pose __start_pose)
   (setq _goal_pose __goal_pose)
   (setq _hazard_types __hazard_types)
   (setq _safety_margin (float __safety_margin))
   (setq _emergency_mode __emergency_mode)
   self)
  (:start_pose
   (&rest __start_pose)
   (if (keywordp (car __start_pose))
       (send* _start_pose __start_pose)
     (progn
       (if __start_pose (setq _start_pose (car __start_pose)))
       _start_pose)))
  (:goal_pose
   (&rest __goal_pose)
   (if (keywordp (car __goal_pose))
       (send* _goal_pose __goal_pose)
     (progn
       (if __goal_pose (setq _goal_pose (car __goal_pose)))
       _goal_pose)))
  (:hazard_types
   (&optional __hazard_types)
   (if __hazard_types (setq _hazard_types __hazard_types)) _hazard_types)
  (:safety_margin
   (&optional __safety_margin)
   (if __safety_margin (setq _safety_margin __safety_margin)) _safety_margin)
  (:emergency_mode
   (&optional (__emergency_mode :null))
   (if (not (eq __emergency_mode :null)) (setq _emergency_mode __emergency_mode)) _emergency_mode)
  (:serialization-length
   ()
   (+
    ;; geometry_msgs/PoseStamped _start_pose
    (send _start_pose :serialization-length)
    ;; geometry_msgs/PoseStamped _goal_pose
    (send _goal_pose :serialization-length)
    ;; string[] _hazard_types
    (apply #'+ (mapcar #'(lambda (x) (+ 4 (length x))) _hazard_types)) 4
    ;; float32 _safety_margin
    4
    ;; bool _emergency_mode
    1
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; geometry_msgs/PoseStamped _start_pose
       (send _start_pose :serialize s)
     ;; geometry_msgs/PoseStamped _goal_pose
       (send _goal_pose :serialize s)
     ;; string[] _hazard_types
     (write-long (length _hazard_types) s)
     (dolist (elem _hazard_types)
       (write-long (length elem) s) (princ elem s)
       )
     ;; float32 _safety_margin
       (sys::poke _safety_margin (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; bool _emergency_mode
       (if _emergency_mode (write-byte -1 s) (write-byte 0 s))
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; geometry_msgs/PoseStamped _start_pose
     (send _start_pose :deserialize buf ptr-) (incf ptr- (send _start_pose :serialization-length))
   ;; geometry_msgs/PoseStamped _goal_pose
     (send _goal_pose :deserialize buf ptr-) (incf ptr- (send _goal_pose :serialization-length))
   ;; string[] _hazard_types
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _hazard_types (make-list n))
     (dotimes (i n)
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setf (elt _hazard_types i) (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
     ))
   ;; float32 _safety_margin
     (setq _safety_margin (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; bool _emergency_mode
     (setq _emergency_mode (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;;
   self)
  )

(defclass semantic_navigation::GetSafePathResponse
  :super ros::object
  :slots (_success _message _safe_path _alternative_paths _safety_score _warnings ))

(defmethod semantic_navigation::GetSafePathResponse
  (:init
   (&key
    ((:success __success) nil)
    ((:message __message) "")
    ((:safe_path __safe_path) (instance semantic_navigation::SemanticPath :init))
    ((:alternative_paths __alternative_paths) ())
    ((:safety_score __safety_score) 0.0)
    ((:warnings __warnings) (let (r) (dotimes (i 0) (push "" r)) r))
    )
   (send-super :init)
   (setq _success __success)
   (setq _message (string __message))
   (setq _safe_path __safe_path)
   (setq _alternative_paths __alternative_paths)
   (setq _safety_score (float __safety_score))
   (setq _warnings __warnings)
   self)
  (:success
   (&optional (__success :null))
   (if (not (eq __success :null)) (setq _success __success)) _success)
  (:message
   (&optional __message)
   (if __message (setq _message __message)) _message)
  (:safe_path
   (&rest __safe_path)
   (if (keywordp (car __safe_path))
       (send* _safe_path __safe_path)
     (progn
       (if __safe_path (setq _safe_path (car __safe_path)))
       _safe_path)))
  (:alternative_paths
   (&rest __alternative_paths)
   (if (keywordp (car __alternative_paths))
       (send* _alternative_paths __alternative_paths)
     (progn
       (if __alternative_paths (setq _alternative_paths (car __alternative_paths)))
       _alternative_paths)))
  (:safety_score
   (&optional __safety_score)
   (if __safety_score (setq _safety_score __safety_score)) _safety_score)
  (:warnings
   (&optional __warnings)
   (if __warnings (setq _warnings __warnings)) _warnings)
  (:serialization-length
   ()
   (+
    ;; bool _success
    1
    ;; string _message
    4 (length _message)
    ;; semantic_navigation/SemanticPath _safe_path
    (send _safe_path :serialization-length)
    ;; semantic_navigation/SemanticPath[] _alternative_paths
    (apply #'+ (send-all _alternative_paths :serialization-length)) 4
    ;; float32 _safety_score
    4
    ;; string[] _warnings
    (apply #'+ (mapcar #'(lambda (x) (+ 4 (length x))) _warnings)) 4
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; bool _success
       (if _success (write-byte -1 s) (write-byte 0 s))
     ;; string _message
       (write-long (length _message) s) (princ _message s)
     ;; semantic_navigation/SemanticPath _safe_path
       (send _safe_path :serialize s)
     ;; semantic_navigation/SemanticPath[] _alternative_paths
     (write-long (length _alternative_paths) s)
     (dolist (elem _alternative_paths)
       (send elem :serialize s)
       )
     ;; float32 _safety_score
       (sys::poke _safety_score (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; string[] _warnings
     (write-long (length _warnings) s)
     (dolist (elem _warnings)
       (write-long (length elem) s) (princ elem s)
       )
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; bool _success
     (setq _success (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; string _message
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _message (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; semantic_navigation/SemanticPath _safe_path
     (send _safe_path :deserialize buf ptr-) (incf ptr- (send _safe_path :serialization-length))
   ;; semantic_navigation/SemanticPath[] _alternative_paths
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _alternative_paths (let (r) (dotimes (i n) (push (instance semantic_navigation::SemanticPath :init) r)) r))
     (dolist (elem- _alternative_paths)
     (send elem- :deserialize buf ptr-) (incf ptr- (send elem- :serialization-length))
     ))
   ;; float32 _safety_score
     (setq _safety_score (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; string[] _warnings
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _warnings (make-list n))
     (dotimes (i n)
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setf (elt _warnings i) (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
     ))
   ;;
   self)
  )

(defclass semantic_navigation::GetSafePath
  :super ros::object
  :slots ())

(setf (get semantic_navigation::GetSafePath :md5sum-) "677123b52cef0c55e41c66d038e4b3a2")
(setf (get semantic_navigation::GetSafePath :datatype-) "semantic_navigation/GetSafePath")
(setf (get semantic_navigation::GetSafePath :request) semantic_navigation::GetSafePathRequest)
(setf (get semantic_navigation::GetSafePath :response) semantic_navigation::GetSafePathResponse)

(defmethod semantic_navigation::GetSafePathRequest
  (:response () (instance semantic_navigation::GetSafePathResponse :init)))

(setf (get semantic_navigation::GetSafePathRequest :md5sum-) "677123b52cef0c55e41c66d038e4b3a2")
(setf (get semantic_navigation::GetSafePathRequest :datatype-) "semantic_navigation/GetSafePathRequest")
(setf (get semantic_navigation::GetSafePathRequest :definition-)
      "# 获取安全路径服务
# 请求
geometry_msgs/PoseStamped start_pose
geometry_msgs/PoseStamped goal_pose
string[] hazard_types       # 需要避开的危险类型
float32 safety_margin       # 安全边距
bool emergency_mode         # 是否为应急模式


================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
---

# 响应
bool success
string message
SemanticPath safe_path      # 安全路径
SemanticPath[] alternative_paths  # 备选路径
float32 safety_score        # 安全评分
string[] warnings           # 警告信息


================================================================================
MSG: semantic_navigation/SemanticPath
# 语义路径消息
Header header

# 路径信息
nav_msgs/Path path

# 语义信息
string[] semantic_labels     # 路径上的语义标签
float32[] semantic_costs     # 对应的语义代价
float32[] safety_scores      # 安全性评分

# 路径属性
float32 total_distance       # 总距离
float32 total_cost          # 总代价
float32 safety_rating       # 安全评级 (0-1)
bool contains_fire_risk     # 是否包含火灾风险

# 应急信息
geometry_msgs/Point[] emergency_exits  # 应急出口位置
string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: nav_msgs/Path
#An array of poses that represents a Path for a robot to follow
Header header
geometry_msgs/PoseStamped[] poses

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
")

(setf (get semantic_navigation::GetSafePathResponse :md5sum-) "677123b52cef0c55e41c66d038e4b3a2")
(setf (get semantic_navigation::GetSafePathResponse :datatype-) "semantic_navigation/GetSafePathResponse")
(setf (get semantic_navigation::GetSafePathResponse :definition-)
      "# 获取安全路径服务
# 请求
geometry_msgs/PoseStamped start_pose
geometry_msgs/PoseStamped goal_pose
string[] hazard_types       # 需要避开的危险类型
float32 safety_margin       # 安全边距
bool emergency_mode         # 是否为应急模式


================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
---

# 响应
bool success
string message
SemanticPath safe_path      # 安全路径
SemanticPath[] alternative_paths  # 备选路径
float32 safety_score        # 安全评分
string[] warnings           # 警告信息


================================================================================
MSG: semantic_navigation/SemanticPath
# 语义路径消息
Header header

# 路径信息
nav_msgs/Path path

# 语义信息
string[] semantic_labels     # 路径上的语义标签
float32[] semantic_costs     # 对应的语义代价
float32[] safety_scores      # 安全性评分

# 路径属性
float32 total_distance       # 总距离
float32 total_cost          # 总代价
float32 safety_rating       # 安全评级 (0-1)
bool contains_fire_risk     # 是否包含火灾风险

# 应急信息
geometry_msgs/Point[] emergency_exits  # 应急出口位置
string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: nav_msgs/Path
#An array of poses that represents a Path for a robot to follow
Header header
geometry_msgs/PoseStamped[] poses

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
")



(provide :semantic_navigation/GetSafePath "677123b52cef0c55e41c66d038e4b3a2")


