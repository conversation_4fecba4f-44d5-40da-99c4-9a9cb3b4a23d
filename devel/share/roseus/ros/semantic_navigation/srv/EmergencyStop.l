;; Auto-generated. Do not edit!


(when (boundp 'semantic_navigation::EmergencyStop)
  (if (not (find-package "SEMANTIC_NAVIGATION"))
    (make-package "SEMANTIC_NAVIGATION"))
  (shadow 'EmergencyStop (find-package "SEMANTIC_NAVIGATION")))
(unless (find-package "SEMANTIC_NAVIGATION::EMER<PERSON><PERSON>Y<PERSON><PERSON>")
  (make-package "SEMANTIC_NAVIGATION::EMERGENCYSTOP"))
(unless (find-package "SEMANTIC_NAVIGATION::EMERGENCYSTOPREQUEST")
  (make-package "SEMANTIC_NAVIGATION::EMERGENCYSTOPREQUEST"))
(unless (find-package "SEMANTIC_NAVIGATION::EMER<PERSON><PERSON>Y<PERSON>OPRESPONSE")
  (make-package "SEMANTIC_NAVIGATION::EMERGENCYSTOPRESPONSE"))

(in-package "ROS")

(if (not (find-package "GEOMETRY_MSGS"))
  (ros::roseus-add-msgs "geometry_msgs"))


(if (not (find-package "GEOMETRY_MSGS"))
  (ros::roseus-add-msgs "geometry_msgs"))


(defclass semantic_navigation::EmergencyStopRequest
  :super ros::object
  :slots (_reason _immediate _hazard_location ))

(defmethod semantic_navigation::EmergencyStopRequest
  (:init
   (&key
    ((:reason __reason) "")
    ((:immediate __immediate) nil)
    ((:hazard_location __hazard_location) (instance geometry_msgs::Point :init))
    )
   (send-super :init)
   (setq _reason (string __reason))
   (setq _immediate __immediate)
   (setq _hazard_location __hazard_location)
   self)
  (:reason
   (&optional __reason)
   (if __reason (setq _reason __reason)) _reason)
  (:immediate
   (&optional (__immediate :null))
   (if (not (eq __immediate :null)) (setq _immediate __immediate)) _immediate)
  (:hazard_location
   (&rest __hazard_location)
   (if (keywordp (car __hazard_location))
       (send* _hazard_location __hazard_location)
     (progn
       (if __hazard_location (setq _hazard_location (car __hazard_location)))
       _hazard_location)))
  (:serialization-length
   ()
   (+
    ;; string _reason
    4 (length _reason)
    ;; bool _immediate
    1
    ;; geometry_msgs/Point _hazard_location
    (send _hazard_location :serialization-length)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; string _reason
       (write-long (length _reason) s) (princ _reason s)
     ;; bool _immediate
       (if _immediate (write-byte -1 s) (write-byte 0 s))
     ;; geometry_msgs/Point _hazard_location
       (send _hazard_location :serialize s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; string _reason
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _reason (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; bool _immediate
     (setq _immediate (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; geometry_msgs/Point _hazard_location
     (send _hazard_location :deserialize buf ptr-) (incf ptr- (send _hazard_location :serialization-length))
   ;;
   self)
  )

(defclass semantic_navigation::EmergencyStopResponse
  :super ros::object
  :slots (_success _message _stop_time _final_pose ))

(defmethod semantic_navigation::EmergencyStopResponse
  (:init
   (&key
    ((:success __success) nil)
    ((:message __message) "")
    ((:stop_time __stop_time) (instance ros::time :init))
    ((:final_pose __final_pose) (instance geometry_msgs::PoseStamped :init))
    )
   (send-super :init)
   (setq _success __success)
   (setq _message (string __message))
   (setq _stop_time __stop_time)
   (setq _final_pose __final_pose)
   self)
  (:success
   (&optional (__success :null))
   (if (not (eq __success :null)) (setq _success __success)) _success)
  (:message
   (&optional __message)
   (if __message (setq _message __message)) _message)
  (:stop_time
   (&optional __stop_time)
   (if __stop_time (setq _stop_time __stop_time)) _stop_time)
  (:final_pose
   (&rest __final_pose)
   (if (keywordp (car __final_pose))
       (send* _final_pose __final_pose)
     (progn
       (if __final_pose (setq _final_pose (car __final_pose)))
       _final_pose)))
  (:serialization-length
   ()
   (+
    ;; bool _success
    1
    ;; string _message
    4 (length _message)
    ;; time _stop_time
    8
    ;; geometry_msgs/PoseStamped _final_pose
    (send _final_pose :serialization-length)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; bool _success
       (if _success (write-byte -1 s) (write-byte 0 s))
     ;; string _message
       (write-long (length _message) s) (princ _message s)
     ;; time _stop_time
       (write-long (send _stop_time :sec) s) (write-long (send _stop_time :nsec) s)
     ;; geometry_msgs/PoseStamped _final_pose
       (send _final_pose :serialize s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; bool _success
     (setq _success (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; string _message
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _message (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; time _stop_time
     (send _stop_time :sec (sys::peek buf ptr- :integer)) (incf ptr- 4)  (send _stop_time :nsec (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;; geometry_msgs/PoseStamped _final_pose
     (send _final_pose :deserialize buf ptr-) (incf ptr- (send _final_pose :serialization-length))
   ;;
   self)
  )

(defclass semantic_navigation::EmergencyStop
  :super ros::object
  :slots ())

(setf (get semantic_navigation::EmergencyStop :md5sum-) "13ca1b92fd866cf0141e505652497177")
(setf (get semantic_navigation::EmergencyStop :datatype-) "semantic_navigation/EmergencyStop")
(setf (get semantic_navigation::EmergencyStop :request) semantic_navigation::EmergencyStopRequest)
(setf (get semantic_navigation::EmergencyStop :response) semantic_navigation::EmergencyStopResponse)

(defmethod semantic_navigation::EmergencyStopRequest
  (:response () (instance semantic_navigation::EmergencyStopResponse :init)))

(setf (get semantic_navigation::EmergencyStopRequest :md5sum-) "13ca1b92fd866cf0141e505652497177")
(setf (get semantic_navigation::EmergencyStopRequest :datatype-) "semantic_navigation/EmergencyStopRequest")
(setf (get semantic_navigation::EmergencyStopRequest :definition-)
      "# 应急停止服务
# 请求
string reason               # 停止原因
bool immediate              # 是否立即停止
geometry_msgs/Point hazard_location  # 危险位置（可选）


================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z
---

# 响应
bool success
string message
time stop_time              # 停止时间
geometry_msgs/PoseStamped final_pose  # 最终位置


================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
")

(setf (get semantic_navigation::EmergencyStopResponse :md5sum-) "13ca1b92fd866cf0141e505652497177")
(setf (get semantic_navigation::EmergencyStopResponse :datatype-) "semantic_navigation/EmergencyStopResponse")
(setf (get semantic_navigation::EmergencyStopResponse :definition-)
      "# 应急停止服务
# 请求
string reason               # 停止原因
bool immediate              # 是否立即停止
geometry_msgs/Point hazard_location  # 危险位置（可选）


================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z
---

# 响应
bool success
string message
time stop_time              # 停止时间
geometry_msgs/PoseStamped final_pose  # 最终位置


================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
")



(provide :semantic_navigation/EmergencyStop "13ca1b92fd866cf0141e505652497177")


