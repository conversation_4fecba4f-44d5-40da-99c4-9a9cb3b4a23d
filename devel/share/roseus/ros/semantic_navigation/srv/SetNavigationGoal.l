;; Auto-generated. Do not edit!


(when (boundp 'semantic_navigation::SetNavigationGoal)
  (if (not (find-package "SEMANTIC_NAVIGATION"))
    (make-package "SEMANTIC_NAVIGATION"))
  (shadow 'SetNavigationGoal (find-package "SEMANTIC_NAVIGATION")))
(unless (find-package "SEMANTIC_NAVIGATION::SETNAVIGATIONGOAL")
  (make-package "SEMANTIC_NAVIGATION::SETNAVIGATIONGOAL"))
(unless (find-package "SEMANTIC_NAVIGATION::SETNAVIGATIONGOALREQUEST")
  (make-package "SEMANTIC_NAVIGATION::SETNAVIGATIONGOALREQUEST"))
(unless (find-package "SEMANTIC_NAVIGATION::SETNAVIGATIONGOALRESPONSE")
  (make-package "SEMANTIC_NAVIGATION::SETNAVIGATIONGOALRESPONSE"))

(in-package "ROS")





(defclass semantic_navigation::SetNavigationGoalRequest
  :super ros::object
  :slots (_goal ))

(defmethod semantic_navigation::SetNavigationGoalRequest
  (:init
   (&key
    ((:goal __goal) (instance semantic_navigation::SemanticNavigationGoal :init))
    )
   (send-super :init)
   (setq _goal __goal)
   self)
  (:goal
   (&rest __goal)
   (if (keywordp (car __goal))
       (send* _goal __goal)
     (progn
       (if __goal (setq _goal (car __goal)))
       _goal)))
  (:serialization-length
   ()
   (+
    ;; semantic_navigation/SemanticNavigationGoal _goal
    (send _goal :serialization-length)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; semantic_navigation/SemanticNavigationGoal _goal
       (send _goal :serialize s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; semantic_navigation/SemanticNavigationGoal _goal
     (send _goal :deserialize buf ptr-) (incf ptr- (send _goal :serialization-length))
   ;;
   self)
  )

(defclass semantic_navigation::SetNavigationGoalResponse
  :super ros::object
  :slots (_success _message _goal_id _estimated_time _planned_path ))

(defmethod semantic_navigation::SetNavigationGoalResponse
  (:init
   (&key
    ((:success __success) nil)
    ((:message __message) "")
    ((:goal_id __goal_id) "")
    ((:estimated_time __estimated_time) 0.0)
    ((:planned_path __planned_path) (instance semantic_navigation::SemanticPath :init))
    )
   (send-super :init)
   (setq _success __success)
   (setq _message (string __message))
   (setq _goal_id (string __goal_id))
   (setq _estimated_time (float __estimated_time))
   (setq _planned_path __planned_path)
   self)
  (:success
   (&optional (__success :null))
   (if (not (eq __success :null)) (setq _success __success)) _success)
  (:message
   (&optional __message)
   (if __message (setq _message __message)) _message)
  (:goal_id
   (&optional __goal_id)
   (if __goal_id (setq _goal_id __goal_id)) _goal_id)
  (:estimated_time
   (&optional __estimated_time)
   (if __estimated_time (setq _estimated_time __estimated_time)) _estimated_time)
  (:planned_path
   (&rest __planned_path)
   (if (keywordp (car __planned_path))
       (send* _planned_path __planned_path)
     (progn
       (if __planned_path (setq _planned_path (car __planned_path)))
       _planned_path)))
  (:serialization-length
   ()
   (+
    ;; bool _success
    1
    ;; string _message
    4 (length _message)
    ;; string _goal_id
    4 (length _goal_id)
    ;; float32 _estimated_time
    4
    ;; semantic_navigation/SemanticPath _planned_path
    (send _planned_path :serialization-length)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; bool _success
       (if _success (write-byte -1 s) (write-byte 0 s))
     ;; string _message
       (write-long (length _message) s) (princ _message s)
     ;; string _goal_id
       (write-long (length _goal_id) s) (princ _goal_id s)
     ;; float32 _estimated_time
       (sys::poke _estimated_time (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; semantic_navigation/SemanticPath _planned_path
       (send _planned_path :serialize s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; bool _success
     (setq _success (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; string _message
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _message (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; string _goal_id
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _goal_id (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; float32 _estimated_time
     (setq _estimated_time (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; semantic_navigation/SemanticPath _planned_path
     (send _planned_path :deserialize buf ptr-) (incf ptr- (send _planned_path :serialization-length))
   ;;
   self)
  )

(defclass semantic_navigation::SetNavigationGoal
  :super ros::object
  :slots ())

(setf (get semantic_navigation::SetNavigationGoal :md5sum-) "9b33341aa6d4bf3615200fa760500108")
(setf (get semantic_navigation::SetNavigationGoal :datatype-) "semantic_navigation/SetNavigationGoal")
(setf (get semantic_navigation::SetNavigationGoal :request) semantic_navigation::SetNavigationGoalRequest)
(setf (get semantic_navigation::SetNavigationGoal :response) semantic_navigation::SetNavigationGoalResponse)

(defmethod semantic_navigation::SetNavigationGoalRequest
  (:response () (instance semantic_navigation::SetNavigationGoalResponse :init)))

(setf (get semantic_navigation::SetNavigationGoalRequest :md5sum-) "9b33341aa6d4bf3615200fa760500108")
(setf (get semantic_navigation::SetNavigationGoalRequest :datatype-) "semantic_navigation/SetNavigationGoalRequest")
(setf (get semantic_navigation::SetNavigationGoalRequest :definition-)
      "# 设置导航目标服务
# 请求
SemanticNavigationGoal goal


================================================================================
MSG: semantic_navigation/SemanticNavigationGoal
# 语义导航目标消息
Header header

# 目标位置
geometry_msgs/PoseStamped target_pose

# 导航模式
string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE

# 语义约束
string[] avoid_classes    # 需要避开的语义类别
string[] prefer_classes   # 优先通过的语义类别

# 安全参数
float32 safety_distance   # 安全距离 (米)
float32 fire_avoidance_distance  # 火焰避让距离 (米)

# 优先级设置
uint8 priority           # 导航优先级 (0-10, 10最高)
bool emergency_mode      # 是否为应急模式

# 超时设置
duration timeout         # 导航超时时间

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
---

# 响应
bool success
string message
string goal_id              # 目标ID，用于跟踪
float32 estimated_time      # 预计完成时间
SemanticPath planned_path   # 规划的路径


================================================================================
MSG: semantic_navigation/SemanticPath
# 语义路径消息
Header header

# 路径信息
nav_msgs/Path path

# 语义信息
string[] semantic_labels     # 路径上的语义标签
float32[] semantic_costs     # 对应的语义代价
float32[] safety_scores      # 安全性评分

# 路径属性
float32 total_distance       # 总距离
float32 total_cost          # 总代价
float32 safety_rating       # 安全评级 (0-1)
bool contains_fire_risk     # 是否包含火灾风险

# 应急信息
geometry_msgs/Point[] emergency_exits  # 应急出口位置
string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: nav_msgs/Path
#An array of poses that represents a Path for a robot to follow
Header header
geometry_msgs/PoseStamped[] poses

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
")

(setf (get semantic_navigation::SetNavigationGoalResponse :md5sum-) "9b33341aa6d4bf3615200fa760500108")
(setf (get semantic_navigation::SetNavigationGoalResponse :datatype-) "semantic_navigation/SetNavigationGoalResponse")
(setf (get semantic_navigation::SetNavigationGoalResponse :definition-)
      "# 设置导航目标服务
# 请求
SemanticNavigationGoal goal


================================================================================
MSG: semantic_navigation/SemanticNavigationGoal
# 语义导航目标消息
Header header

# 目标位置
geometry_msgs/PoseStamped target_pose

# 导航模式
string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE

# 语义约束
string[] avoid_classes    # 需要避开的语义类别
string[] prefer_classes   # 优先通过的语义类别

# 安全参数
float32 safety_distance   # 安全距离 (米)
float32 fire_avoidance_distance  # 火焰避让距离 (米)

# 优先级设置
uint8 priority           # 导航优先级 (0-10, 10最高)
bool emergency_mode      # 是否为应急模式

# 超时设置
duration timeout         # 导航超时时间

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
---

# 响应
bool success
string message
string goal_id              # 目标ID，用于跟踪
float32 estimated_time      # 预计完成时间
SemanticPath planned_path   # 规划的路径


================================================================================
MSG: semantic_navigation/SemanticPath
# 语义路径消息
Header header

# 路径信息
nav_msgs/Path path

# 语义信息
string[] semantic_labels     # 路径上的语义标签
float32[] semantic_costs     # 对应的语义代价
float32[] safety_scores      # 安全性评分

# 路径属性
float32 total_distance       # 总距离
float32 total_cost          # 总代价
float32 safety_rating       # 安全评级 (0-1)
bool contains_fire_risk     # 是否包含火灾风险

# 应急信息
geometry_msgs/Point[] emergency_exits  # 应急出口位置
string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: nav_msgs/Path
#An array of poses that represents a Path for a robot to follow
Header header
geometry_msgs/PoseStamped[] poses

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
")



(provide :semantic_navigation/SetNavigationGoal "9b33341aa6d4bf3615200fa760500108")


