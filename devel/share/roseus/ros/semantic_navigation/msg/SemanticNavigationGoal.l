;; Auto-generated. Do not edit!


(when (boundp 'semantic_navigation::SemanticNavigationGoal)
  (if (not (find-package "SEMANTIC_NAVIGATION"))
    (make-package "SEMANTIC_NAVIGATION"))
  (shadow 'SemanticNavigationGoal (find-package "SEMANTIC_NAVIGATION")))
(unless (find-package "SEMANTIC_NAVIGATION::SEMANTICNAVIGATIONGOAL")
  (make-package "SEMANTIC_NAVIGATION::SEMANTICNAVIGATIONGOAL"))

(in-package "ROS")
;;//! \htmlinclude SemanticNavigationGoal.msg.html
(if (not (find-package "GEOMETRY_MSGS"))
  (ros::roseus-add-msgs "geometry_msgs"))
(if (not (find-package "STD_MSGS"))
  (ros::roseus-add-msgs "std_msgs"))


(defclass semantic_navigation::SemanticNavigationGoal
  :super ros::object
  :slots (_header _target_pose _navigation_mode _avoid_classes _prefer_classes _safety_distance _fire_avoidance_distance _priority _emergency_mode _timeout ))

(defmethod semantic_navigation::SemanticNavigationGoal
  (:init
   (&key
    ((:header __header) (instance std_msgs::Header :init))
    ((:target_pose __target_pose) (instance geometry_msgs::PoseStamped :init))
    ((:navigation_mode __navigation_mode) "")
    ((:avoid_classes __avoid_classes) (let (r) (dotimes (i 0) (push "" r)) r))
    ((:prefer_classes __prefer_classes) (let (r) (dotimes (i 0) (push "" r)) r))
    ((:safety_distance __safety_distance) 0.0)
    ((:fire_avoidance_distance __fire_avoidance_distance) 0.0)
    ((:priority __priority) 0)
    ((:emergency_mode __emergency_mode) nil)
    ((:timeout __timeout) (instance ros::time :init))
    )
   (send-super :init)
   (setq _header __header)
   (setq _target_pose __target_pose)
   (setq _navigation_mode (string __navigation_mode))
   (setq _avoid_classes __avoid_classes)
   (setq _prefer_classes __prefer_classes)
   (setq _safety_distance (float __safety_distance))
   (setq _fire_avoidance_distance (float __fire_avoidance_distance))
   (setq _priority (round __priority))
   (setq _emergency_mode __emergency_mode)
   (setq _timeout __timeout)
   self)
  (:header
   (&rest __header)
   (if (keywordp (car __header))
       (send* _header __header)
     (progn
       (if __header (setq _header (car __header)))
       _header)))
  (:target_pose
   (&rest __target_pose)
   (if (keywordp (car __target_pose))
       (send* _target_pose __target_pose)
     (progn
       (if __target_pose (setq _target_pose (car __target_pose)))
       _target_pose)))
  (:navigation_mode
   (&optional __navigation_mode)
   (if __navigation_mode (setq _navigation_mode __navigation_mode)) _navigation_mode)
  (:avoid_classes
   (&optional __avoid_classes)
   (if __avoid_classes (setq _avoid_classes __avoid_classes)) _avoid_classes)
  (:prefer_classes
   (&optional __prefer_classes)
   (if __prefer_classes (setq _prefer_classes __prefer_classes)) _prefer_classes)
  (:safety_distance
   (&optional __safety_distance)
   (if __safety_distance (setq _safety_distance __safety_distance)) _safety_distance)
  (:fire_avoidance_distance
   (&optional __fire_avoidance_distance)
   (if __fire_avoidance_distance (setq _fire_avoidance_distance __fire_avoidance_distance)) _fire_avoidance_distance)
  (:priority
   (&optional __priority)
   (if __priority (setq _priority __priority)) _priority)
  (:emergency_mode
   (&optional (__emergency_mode :null))
   (if (not (eq __emergency_mode :null)) (setq _emergency_mode __emergency_mode)) _emergency_mode)
  (:timeout
   (&optional __timeout)
   (if __timeout (setq _timeout __timeout)) _timeout)
  (:serialization-length
   ()
   (+
    ;; std_msgs/Header _header
    (send _header :serialization-length)
    ;; geometry_msgs/PoseStamped _target_pose
    (send _target_pose :serialization-length)
    ;; string _navigation_mode
    4 (length _navigation_mode)
    ;; string[] _avoid_classes
    (apply #'+ (mapcar #'(lambda (x) (+ 4 (length x))) _avoid_classes)) 4
    ;; string[] _prefer_classes
    (apply #'+ (mapcar #'(lambda (x) (+ 4 (length x))) _prefer_classes)) 4
    ;; float32 _safety_distance
    4
    ;; float32 _fire_avoidance_distance
    4
    ;; uint8 _priority
    1
    ;; bool _emergency_mode
    1
    ;; duration _timeout
    8
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; std_msgs/Header _header
       (send _header :serialize s)
     ;; geometry_msgs/PoseStamped _target_pose
       (send _target_pose :serialize s)
     ;; string _navigation_mode
       (write-long (length _navigation_mode) s) (princ _navigation_mode s)
     ;; string[] _avoid_classes
     (write-long (length _avoid_classes) s)
     (dolist (elem _avoid_classes)
       (write-long (length elem) s) (princ elem s)
       )
     ;; string[] _prefer_classes
     (write-long (length _prefer_classes) s)
     (dolist (elem _prefer_classes)
       (write-long (length elem) s) (princ elem s)
       )
     ;; float32 _safety_distance
       (sys::poke _safety_distance (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; float32 _fire_avoidance_distance
       (sys::poke _fire_avoidance_distance (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; uint8 _priority
       (write-byte _priority s)
     ;; bool _emergency_mode
       (if _emergency_mode (write-byte -1 s) (write-byte 0 s))
     ;; duration _timeout
       (write-long (send _timeout :sec) s) (write-long (send _timeout :nsec) s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; std_msgs/Header _header
     (send _header :deserialize buf ptr-) (incf ptr- (send _header :serialization-length))
   ;; geometry_msgs/PoseStamped _target_pose
     (send _target_pose :deserialize buf ptr-) (incf ptr- (send _target_pose :serialization-length))
   ;; string _navigation_mode
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _navigation_mode (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; string[] _avoid_classes
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _avoid_classes (make-list n))
     (dotimes (i n)
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setf (elt _avoid_classes i) (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
     ))
   ;; string[] _prefer_classes
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _prefer_classes (make-list n))
     (dotimes (i n)
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setf (elt _prefer_classes i) (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
     ))
   ;; float32 _safety_distance
     (setq _safety_distance (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; float32 _fire_avoidance_distance
     (setq _fire_avoidance_distance (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; uint8 _priority
     (setq _priority (sys::peek buf ptr- :char)) (incf ptr- 1)
   ;; bool _emergency_mode
     (setq _emergency_mode (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; duration _timeout
     (send _timeout :sec (sys::peek buf ptr- :integer)) (incf ptr- 4)  (send _timeout :nsec (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;;
   self)
  )

(setf (get semantic_navigation::SemanticNavigationGoal :md5sum-) "f3344e67e6ced968d5c3cc9238c45392")
(setf (get semantic_navigation::SemanticNavigationGoal :datatype-) "semantic_navigation/SemanticNavigationGoal")
(setf (get semantic_navigation::SemanticNavigationGoal :definition-)
      "# 语义导航目标消息
Header header

# 目标位置
geometry_msgs/PoseStamped target_pose

# 导航模式
string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE

# 语义约束
string[] avoid_classes    # 需要避开的语义类别
string[] prefer_classes   # 优先通过的语义类别

# 安全参数
float32 safety_distance   # 安全距离 (米)
float32 fire_avoidance_distance  # 火焰避让距离 (米)

# 优先级设置
uint8 priority           # 导航优先级 (0-10, 10最高)
bool emergency_mode      # 是否为应急模式

# 超时设置
duration timeout         # 导航超时时间

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w

")



(provide :semantic_navigation/SemanticNavigationGoal "f3344e67e6ced968d5c3cc9238c45392")


