;; Auto-generated. Do not edit!


(when (boundp 'semantic_navigation::EmergencyAlert)
  (if (not (find-package "SEMANTIC_NAVIGATION"))
    (make-package "SEMANTIC_NAVIGATION"))
  (shadow 'EmergencyAlert (find-package "SEMANTIC_NAVIGATION")))
(unless (find-package "SEMANTIC_NAVIGATION::EME<PERSON><PERSON>NCYALERT")
  (make-package "SEMANTIC_NAVIGATION::EMERGENCYALERT"))

(in-package "ROS")
;;//! \htmlinclude EmergencyAlert.msg.html
(if (not (find-package "GEOMETRY_MSGS"))
  (ros::roseus-add-msgs "geometry_msgs"))
(if (not (find-package "STD_MSGS"))
  (ros::roseus-add-msgs "std_msgs"))


(defclass semantic_navigation::EmergencyAlert
  :super ros::object
  :slots (_header _alert_type _severity _location _affected_radius _description _recommended_action _detection_time _estimated_duration _confidence _related_objects ))

(defmethod semantic_navigation::EmergencyAlert
  (:init
   (&key
    ((:header __header) (instance std_msgs::Header :init))
    ((:alert_type __alert_type) "")
    ((:severity __severity) "")
    ((:location __location) (instance geometry_msgs::Point :init))
    ((:affected_radius __affected_radius) 0.0)
    ((:description __description) "")
    ((:recommended_action __recommended_action) "")
    ((:detection_time __detection_time) (instance ros::time :init))
    ((:estimated_duration __estimated_duration) (instance ros::time :init))
    ((:confidence __confidence) 0.0)
    ((:related_objects __related_objects) (let (r) (dotimes (i 0) (push "" r)) r))
    )
   (send-super :init)
   (setq _header __header)
   (setq _alert_type (string __alert_type))
   (setq _severity (string __severity))
   (setq _location __location)
   (setq _affected_radius (float __affected_radius))
   (setq _description (string __description))
   (setq _recommended_action (string __recommended_action))
   (setq _detection_time __detection_time)
   (setq _estimated_duration __estimated_duration)
   (setq _confidence (float __confidence))
   (setq _related_objects __related_objects)
   self)
  (:header
   (&rest __header)
   (if (keywordp (car __header))
       (send* _header __header)
     (progn
       (if __header (setq _header (car __header)))
       _header)))
  (:alert_type
   (&optional __alert_type)
   (if __alert_type (setq _alert_type __alert_type)) _alert_type)
  (:severity
   (&optional __severity)
   (if __severity (setq _severity __severity)) _severity)
  (:location
   (&rest __location)
   (if (keywordp (car __location))
       (send* _location __location)
     (progn
       (if __location (setq _location (car __location)))
       _location)))
  (:affected_radius
   (&optional __affected_radius)
   (if __affected_radius (setq _affected_radius __affected_radius)) _affected_radius)
  (:description
   (&optional __description)
   (if __description (setq _description __description)) _description)
  (:recommended_action
   (&optional __recommended_action)
   (if __recommended_action (setq _recommended_action __recommended_action)) _recommended_action)
  (:detection_time
   (&optional __detection_time)
   (if __detection_time (setq _detection_time __detection_time)) _detection_time)
  (:estimated_duration
   (&optional __estimated_duration)
   (if __estimated_duration (setq _estimated_duration __estimated_duration)) _estimated_duration)
  (:confidence
   (&optional __confidence)
   (if __confidence (setq _confidence __confidence)) _confidence)
  (:related_objects
   (&optional __related_objects)
   (if __related_objects (setq _related_objects __related_objects)) _related_objects)
  (:serialization-length
   ()
   (+
    ;; std_msgs/Header _header
    (send _header :serialization-length)
    ;; string _alert_type
    4 (length _alert_type)
    ;; string _severity
    4 (length _severity)
    ;; geometry_msgs/Point _location
    (send _location :serialization-length)
    ;; float32 _affected_radius
    4
    ;; string _description
    4 (length _description)
    ;; string _recommended_action
    4 (length _recommended_action)
    ;; time _detection_time
    8
    ;; duration _estimated_duration
    8
    ;; float32 _confidence
    4
    ;; string[] _related_objects
    (apply #'+ (mapcar #'(lambda (x) (+ 4 (length x))) _related_objects)) 4
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; std_msgs/Header _header
       (send _header :serialize s)
     ;; string _alert_type
       (write-long (length _alert_type) s) (princ _alert_type s)
     ;; string _severity
       (write-long (length _severity) s) (princ _severity s)
     ;; geometry_msgs/Point _location
       (send _location :serialize s)
     ;; float32 _affected_radius
       (sys::poke _affected_radius (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; string _description
       (write-long (length _description) s) (princ _description s)
     ;; string _recommended_action
       (write-long (length _recommended_action) s) (princ _recommended_action s)
     ;; time _detection_time
       (write-long (send _detection_time :sec) s) (write-long (send _detection_time :nsec) s)
     ;; duration _estimated_duration
       (write-long (send _estimated_duration :sec) s) (write-long (send _estimated_duration :nsec) s)
     ;; float32 _confidence
       (sys::poke _confidence (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; string[] _related_objects
     (write-long (length _related_objects) s)
     (dolist (elem _related_objects)
       (write-long (length elem) s) (princ elem s)
       )
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; std_msgs/Header _header
     (send _header :deserialize buf ptr-) (incf ptr- (send _header :serialization-length))
   ;; string _alert_type
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _alert_type (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; string _severity
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _severity (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; geometry_msgs/Point _location
     (send _location :deserialize buf ptr-) (incf ptr- (send _location :serialization-length))
   ;; float32 _affected_radius
     (setq _affected_radius (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; string _description
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _description (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; string _recommended_action
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _recommended_action (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; time _detection_time
     (send _detection_time :sec (sys::peek buf ptr- :integer)) (incf ptr- 4)  (send _detection_time :nsec (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;; duration _estimated_duration
     (send _estimated_duration :sec (sys::peek buf ptr- :integer)) (incf ptr- 4)  (send _estimated_duration :nsec (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;; float32 _confidence
     (setq _confidence (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; string[] _related_objects
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _related_objects (make-list n))
     (dotimes (i n)
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setf (elt _related_objects i) (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
     ))
   ;;
   self)
  )

(setf (get semantic_navigation::EmergencyAlert :md5sum-) "a8796edaa18ec76393edd2510a2b4bdd")
(setf (get semantic_navigation::EmergencyAlert :datatype-) "semantic_navigation/EmergencyAlert")
(setf (get semantic_navigation::EmergencyAlert :definition-)
      "# 应急警报消息
Header header

# 警报类型
string alert_type           # FIRE, SMOKE, OBSTACLE, SYSTEM_FAILURE

# 警报等级
string severity             # LOW, MEDIUM, HIGH, CRITICAL

# 位置信息
geometry_msgs/Point location
float32 affected_radius     # 影响半径 (米)

# 描述信息
string description          # 警报描述
string recommended_action   # 建议行动

# 时间信息
time detection_time         # 检测时间
duration estimated_duration # 预计持续时间

# 相关数据
float32 confidence          # 置信度
string[] related_objects    # 相关物体

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

")



(provide :semantic_navigation/EmergencyAlert "a8796edaa18ec76393edd2510a2b4bdd")


