;; Auto-generated. Do not edit!


(when (boundp 'semantic_navigation::SemanticPath)
  (if (not (find-package "SEMANTIC_NAVIGATION"))
    (make-package "SEMANTIC_NAVIGATION"))
  (shadow 'SemanticPath (find-package "SEMANTIC_NAVIGATION")))
(unless (find-package "SEMANTIC_NAVIGATION::SEMANTICPATH")
  (make-package "SEMANTIC_NAVIGATION::SEMANTICPATH"))

(in-package "ROS")
;;//! \htmlinclude SemanticPath.msg.html
(if (not (find-package "GEOMETRY_MSGS"))
  (ros::roseus-add-msgs "geometry_msgs"))
(if (not (find-package "NAV_MSGS"))
  (ros::roseus-add-msgs "nav_msgs"))
(if (not (find-package "STD_MSGS"))
  (ros::roseus-add-msgs "std_msgs"))


(defclass semantic_navigation::SemanticPath
  :super ros::object
  :slots (_header _path _semantic_labels _semantic_costs _safety_scores _total_distance _total_cost _safety_rating _contains_fire_risk _emergency_exits _risk_level ))

(defmethod semantic_navigation::SemanticPath
  (:init
   (&key
    ((:header __header) (instance std_msgs::Header :init))
    ((:path __path) (instance nav_msgs::Path :init))
    ((:semantic_labels __semantic_labels) (let (r) (dotimes (i 0) (push "" r)) r))
    ((:semantic_costs __semantic_costs) (make-array 0 :initial-element 0.0 :element-type :float))
    ((:safety_scores __safety_scores) (make-array 0 :initial-element 0.0 :element-type :float))
    ((:total_distance __total_distance) 0.0)
    ((:total_cost __total_cost) 0.0)
    ((:safety_rating __safety_rating) 0.0)
    ((:contains_fire_risk __contains_fire_risk) nil)
    ((:emergency_exits __emergency_exits) ())
    ((:risk_level __risk_level) "")
    )
   (send-super :init)
   (setq _header __header)
   (setq _path __path)
   (setq _semantic_labels __semantic_labels)
   (setq _semantic_costs __semantic_costs)
   (setq _safety_scores __safety_scores)
   (setq _total_distance (float __total_distance))
   (setq _total_cost (float __total_cost))
   (setq _safety_rating (float __safety_rating))
   (setq _contains_fire_risk __contains_fire_risk)
   (setq _emergency_exits __emergency_exits)
   (setq _risk_level (string __risk_level))
   self)
  (:header
   (&rest __header)
   (if (keywordp (car __header))
       (send* _header __header)
     (progn
       (if __header (setq _header (car __header)))
       _header)))
  (:path
   (&rest __path)
   (if (keywordp (car __path))
       (send* _path __path)
     (progn
       (if __path (setq _path (car __path)))
       _path)))
  (:semantic_labels
   (&optional __semantic_labels)
   (if __semantic_labels (setq _semantic_labels __semantic_labels)) _semantic_labels)
  (:semantic_costs
   (&optional __semantic_costs)
   (if __semantic_costs (setq _semantic_costs __semantic_costs)) _semantic_costs)
  (:safety_scores
   (&optional __safety_scores)
   (if __safety_scores (setq _safety_scores __safety_scores)) _safety_scores)
  (:total_distance
   (&optional __total_distance)
   (if __total_distance (setq _total_distance __total_distance)) _total_distance)
  (:total_cost
   (&optional __total_cost)
   (if __total_cost (setq _total_cost __total_cost)) _total_cost)
  (:safety_rating
   (&optional __safety_rating)
   (if __safety_rating (setq _safety_rating __safety_rating)) _safety_rating)
  (:contains_fire_risk
   (&optional (__contains_fire_risk :null))
   (if (not (eq __contains_fire_risk :null)) (setq _contains_fire_risk __contains_fire_risk)) _contains_fire_risk)
  (:emergency_exits
   (&rest __emergency_exits)
   (if (keywordp (car __emergency_exits))
       (send* _emergency_exits __emergency_exits)
     (progn
       (if __emergency_exits (setq _emergency_exits (car __emergency_exits)))
       _emergency_exits)))
  (:risk_level
   (&optional __risk_level)
   (if __risk_level (setq _risk_level __risk_level)) _risk_level)
  (:serialization-length
   ()
   (+
    ;; std_msgs/Header _header
    (send _header :serialization-length)
    ;; nav_msgs/Path _path
    (send _path :serialization-length)
    ;; string[] _semantic_labels
    (apply #'+ (mapcar #'(lambda (x) (+ 4 (length x))) _semantic_labels)) 4
    ;; float32[] _semantic_costs
    (* 4    (length _semantic_costs)) 4
    ;; float32[] _safety_scores
    (* 4    (length _safety_scores)) 4
    ;; float32 _total_distance
    4
    ;; float32 _total_cost
    4
    ;; float32 _safety_rating
    4
    ;; bool _contains_fire_risk
    1
    ;; geometry_msgs/Point[] _emergency_exits
    (apply #'+ (send-all _emergency_exits :serialization-length)) 4
    ;; string _risk_level
    4 (length _risk_level)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; std_msgs/Header _header
       (send _header :serialize s)
     ;; nav_msgs/Path _path
       (send _path :serialize s)
     ;; string[] _semantic_labels
     (write-long (length _semantic_labels) s)
     (dolist (elem _semantic_labels)
       (write-long (length elem) s) (princ elem s)
       )
     ;; float32[] _semantic_costs
     (write-long (length _semantic_costs) s)
     (dotimes (i (length _semantic_costs))
       (sys::poke (elt _semantic_costs i) (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
       )
     ;; float32[] _safety_scores
     (write-long (length _safety_scores) s)
     (dotimes (i (length _safety_scores))
       (sys::poke (elt _safety_scores i) (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
       )
     ;; float32 _total_distance
       (sys::poke _total_distance (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; float32 _total_cost
       (sys::poke _total_cost (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; float32 _safety_rating
       (sys::poke _safety_rating (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; bool _contains_fire_risk
       (if _contains_fire_risk (write-byte -1 s) (write-byte 0 s))
     ;; geometry_msgs/Point[] _emergency_exits
     (write-long (length _emergency_exits) s)
     (dolist (elem _emergency_exits)
       (send elem :serialize s)
       )
     ;; string _risk_level
       (write-long (length _risk_level) s) (princ _risk_level s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; std_msgs/Header _header
     (send _header :deserialize buf ptr-) (incf ptr- (send _header :serialization-length))
   ;; nav_msgs/Path _path
     (send _path :deserialize buf ptr-) (incf ptr- (send _path :serialization-length))
   ;; string[] _semantic_labels
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _semantic_labels (make-list n))
     (dotimes (i n)
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setf (elt _semantic_labels i) (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
     ))
   ;; float32[] _semantic_costs
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _semantic_costs (instantiate float-vector n))
     (dotimes (i n)
     (setf (elt _semantic_costs i) (sys::peek buf ptr- :float)) (incf ptr- 4)
     ))
   ;; float32[] _safety_scores
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _safety_scores (instantiate float-vector n))
     (dotimes (i n)
     (setf (elt _safety_scores i) (sys::peek buf ptr- :float)) (incf ptr- 4)
     ))
   ;; float32 _total_distance
     (setq _total_distance (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; float32 _total_cost
     (setq _total_cost (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; float32 _safety_rating
     (setq _safety_rating (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; bool _contains_fire_risk
     (setq _contains_fire_risk (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; geometry_msgs/Point[] _emergency_exits
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _emergency_exits (let (r) (dotimes (i n) (push (instance geometry_msgs::Point :init) r)) r))
     (dolist (elem- _emergency_exits)
     (send elem- :deserialize buf ptr-) (incf ptr- (send elem- :serialization-length))
     ))
   ;; string _risk_level
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _risk_level (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;;
   self)
  )

(setf (get semantic_navigation::SemanticPath :md5sum-) "0320dcd1e20996da4804a029f13c56f2")
(setf (get semantic_navigation::SemanticPath :datatype-) "semantic_navigation/SemanticPath")
(setf (get semantic_navigation::SemanticPath :definition-)
      "# 语义路径消息
Header header

# 路径信息
nav_msgs/Path path

# 语义信息
string[] semantic_labels     # 路径上的语义标签
float32[] semantic_costs     # 对应的语义代价
float32[] safety_scores      # 安全性评分

# 路径属性
float32 total_distance       # 总距离
float32 total_cost          # 总代价
float32 safety_rating       # 安全评级 (0-1)
bool contains_fire_risk     # 是否包含火灾风险

# 应急信息
geometry_msgs/Point[] emergency_exits  # 应急出口位置
string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: nav_msgs/Path
#An array of poses that represents a Path for a robot to follow
Header header
geometry_msgs/PoseStamped[] poses

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w

")



(provide :semantic_navigation/SemanticPath "0320dcd1e20996da4804a029f13c56f2")


