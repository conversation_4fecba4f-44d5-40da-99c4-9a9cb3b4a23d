;; Auto-generated. Do not edit!


(when (boundp 'semantic_navigation::NavigationStatus)
  (if (not (find-package "SEMANTIC_NAVIGATION"))
    (make-package "SEMANTIC_NAVIGATION"))
  (shadow 'NavigationStatus (find-package "SEMANTIC_NAVIGATION")))
(unless (find-package "SEMANTIC_NAVIGATION::NAVIGATIONSTATUS")
  (make-package "SEMANTIC_NAVIGATION::NAVIGATIONSTATUS"))

(in-package "ROS")
;;//! \htmlinclude NavigationStatus.msg.html
(if (not (find-package "GEOMETRY_MSGS"))
  (ros::roseus-add-msgs "geometry_msgs"))
(if (not (find-package "STD_MSGS"))
  (ros::roseus-add-msgs "std_msgs"))


(defclass semantic_navigation::NavigationStatus
  :super ros::object
  :slots (_header _state _mode _current_pose _target_pose _progress _distance_remaining _time_remaining _fire_detected _emergency_active _detected_hazards _current_speed _path_deviation ))

(defmethod semantic_navigation::NavigationStatus
  (:init
   (&key
    ((:header __header) (instance std_msgs::Header :init))
    ((:state __state) "")
    ((:mode __mode) "")
    ((:current_pose __current_pose) (instance geometry_msgs::PoseStamped :init))
    ((:target_pose __target_pose) (instance geometry_msgs::PoseStamped :init))
    ((:progress __progress) 0.0)
    ((:distance_remaining __distance_remaining) 0.0)
    ((:time_remaining __time_remaining) (instance ros::time :init))
    ((:fire_detected __fire_detected) nil)
    ((:emergency_active __emergency_active) nil)
    ((:detected_hazards __detected_hazards) (let (r) (dotimes (i 0) (push "" r)) r))
    ((:current_speed __current_speed) 0.0)
    ((:path_deviation __path_deviation) 0.0)
    )
   (send-super :init)
   (setq _header __header)
   (setq _state (string __state))
   (setq _mode (string __mode))
   (setq _current_pose __current_pose)
   (setq _target_pose __target_pose)
   (setq _progress (float __progress))
   (setq _distance_remaining (float __distance_remaining))
   (setq _time_remaining __time_remaining)
   (setq _fire_detected __fire_detected)
   (setq _emergency_active __emergency_active)
   (setq _detected_hazards __detected_hazards)
   (setq _current_speed (float __current_speed))
   (setq _path_deviation (float __path_deviation))
   self)
  (:header
   (&rest __header)
   (if (keywordp (car __header))
       (send* _header __header)
     (progn
       (if __header (setq _header (car __header)))
       _header)))
  (:state
   (&optional __state)
   (if __state (setq _state __state)) _state)
  (:mode
   (&optional __mode)
   (if __mode (setq _mode __mode)) _mode)
  (:current_pose
   (&rest __current_pose)
   (if (keywordp (car __current_pose))
       (send* _current_pose __current_pose)
     (progn
       (if __current_pose (setq _current_pose (car __current_pose)))
       _current_pose)))
  (:target_pose
   (&rest __target_pose)
   (if (keywordp (car __target_pose))
       (send* _target_pose __target_pose)
     (progn
       (if __target_pose (setq _target_pose (car __target_pose)))
       _target_pose)))
  (:progress
   (&optional __progress)
   (if __progress (setq _progress __progress)) _progress)
  (:distance_remaining
   (&optional __distance_remaining)
   (if __distance_remaining (setq _distance_remaining __distance_remaining)) _distance_remaining)
  (:time_remaining
   (&optional __time_remaining)
   (if __time_remaining (setq _time_remaining __time_remaining)) _time_remaining)
  (:fire_detected
   (&optional (__fire_detected :null))
   (if (not (eq __fire_detected :null)) (setq _fire_detected __fire_detected)) _fire_detected)
  (:emergency_active
   (&optional (__emergency_active :null))
   (if (not (eq __emergency_active :null)) (setq _emergency_active __emergency_active)) _emergency_active)
  (:detected_hazards
   (&optional __detected_hazards)
   (if __detected_hazards (setq _detected_hazards __detected_hazards)) _detected_hazards)
  (:current_speed
   (&optional __current_speed)
   (if __current_speed (setq _current_speed __current_speed)) _current_speed)
  (:path_deviation
   (&optional __path_deviation)
   (if __path_deviation (setq _path_deviation __path_deviation)) _path_deviation)
  (:serialization-length
   ()
   (+
    ;; std_msgs/Header _header
    (send _header :serialization-length)
    ;; string _state
    4 (length _state)
    ;; string _mode
    4 (length _mode)
    ;; geometry_msgs/PoseStamped _current_pose
    (send _current_pose :serialization-length)
    ;; geometry_msgs/PoseStamped _target_pose
    (send _target_pose :serialization-length)
    ;; float32 _progress
    4
    ;; float32 _distance_remaining
    4
    ;; duration _time_remaining
    8
    ;; bool _fire_detected
    1
    ;; bool _emergency_active
    1
    ;; string[] _detected_hazards
    (apply #'+ (mapcar #'(lambda (x) (+ 4 (length x))) _detected_hazards)) 4
    ;; float32 _current_speed
    4
    ;; float32 _path_deviation
    4
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; std_msgs/Header _header
       (send _header :serialize s)
     ;; string _state
       (write-long (length _state) s) (princ _state s)
     ;; string _mode
       (write-long (length _mode) s) (princ _mode s)
     ;; geometry_msgs/PoseStamped _current_pose
       (send _current_pose :serialize s)
     ;; geometry_msgs/PoseStamped _target_pose
       (send _target_pose :serialize s)
     ;; float32 _progress
       (sys::poke _progress (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; float32 _distance_remaining
       (sys::poke _distance_remaining (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; duration _time_remaining
       (write-long (send _time_remaining :sec) s) (write-long (send _time_remaining :nsec) s)
     ;; bool _fire_detected
       (if _fire_detected (write-byte -1 s) (write-byte 0 s))
     ;; bool _emergency_active
       (if _emergency_active (write-byte -1 s) (write-byte 0 s))
     ;; string[] _detected_hazards
     (write-long (length _detected_hazards) s)
     (dolist (elem _detected_hazards)
       (write-long (length elem) s) (princ elem s)
       )
     ;; float32 _current_speed
       (sys::poke _current_speed (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;; float32 _path_deviation
       (sys::poke _path_deviation (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; std_msgs/Header _header
     (send _header :deserialize buf ptr-) (incf ptr- (send _header :serialization-length))
   ;; string _state
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _state (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; string _mode
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _mode (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; geometry_msgs/PoseStamped _current_pose
     (send _current_pose :deserialize buf ptr-) (incf ptr- (send _current_pose :serialization-length))
   ;; geometry_msgs/PoseStamped _target_pose
     (send _target_pose :deserialize buf ptr-) (incf ptr- (send _target_pose :serialization-length))
   ;; float32 _progress
     (setq _progress (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; float32 _distance_remaining
     (setq _distance_remaining (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; duration _time_remaining
     (send _time_remaining :sec (sys::peek buf ptr- :integer)) (incf ptr- 4)  (send _time_remaining :nsec (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;; bool _fire_detected
     (setq _fire_detected (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; bool _emergency_active
     (setq _emergency_active (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; string[] _detected_hazards
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _detected_hazards (make-list n))
     (dotimes (i n)
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setf (elt _detected_hazards i) (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
     ))
   ;; float32 _current_speed
     (setq _current_speed (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;; float32 _path_deviation
     (setq _path_deviation (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;;
   self)
  )

(setf (get semantic_navigation::NavigationStatus :md5sum-) "cdbdd9a0f2482e0f81f898630e3a3988")
(setf (get semantic_navigation::NavigationStatus :datatype-) "semantic_navigation/NavigationStatus")
(setf (get semantic_navigation::NavigationStatus :definition-)
      "# 导航状态消息
Header header

# 导航状态
string state                # IDLE, PLANNING, NAVIGATING, EMERGENCY, STOPPED, FAILED
string mode                 # NORMAL, EMERGENCY, FIRE_ESCAPE

# 当前位置和目标
geometry_msgs/PoseStamped current_pose
geometry_msgs/PoseStamped target_pose

# 进度信息
float32 progress            # 完成进度 (0-1)
float32 distance_remaining  # 剩余距离
duration time_remaining     # 预计剩余时间

# 安全状态
bool fire_detected          # 是否检测到火焰
bool emergency_active       # 是否处于应急状态
string[] detected_hazards   # 检测到的危险

# 性能指标
float32 current_speed       # 当前速度
float32 path_deviation      # 路径偏差

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w

")



(provide :semantic_navigation/NavigationStatus "cdbdd9a0f2482e0f81f898630e3a3988")


