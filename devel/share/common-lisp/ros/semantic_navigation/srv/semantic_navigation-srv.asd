
(cl:in-package :asdf)

(defsystem "semantic_navigation-srv"
  :depends-on (:roslisp-msg-protocol :roslisp-utils :geometry_msgs-msg
               :semantic_navigation-msg
)
  :components ((:file "_package")
    (:file "EmergencyStop" :depends-on ("_package_EmergencyStop"))
    (:file "_package_EmergencyStop" :depends-on ("_package"))
    (:file "GetSafePath" :depends-on ("_package_GetSafePath"))
    (:file "_package_GetSafePath" :depends-on ("_package"))
    (:file "SetNavigationGoal" :depends-on ("_package_SetNavigationGoal"))
    (:file "_package_SetNavigationGoal" :depends-on ("_package"))
  ))