(cl:defpackage semantic_navigation-srv
  (:use )
  (:export
   "EMERGENCYSTOP"
   "<EMERGENCYSTOP-REQUEST>"
   "EMERGENCYSTOP-REQUEST"
   "<EMERGENCYSTOP-RESPONSE>"
   "EMERGENCYSTOP-RESPONSE"
   "GETSAFEPATH"
   "<GETSAFEPATH-REQUEST>"
   "GETSAFEPATH-REQUEST"
   "<GETSAFEPATH-RESPONSE>"
   "GETSAFEPATH-RESPONSE"
   "SETNAVIGATIONGOAL"
   "<SETNAVIGATIONGOAL-REQUEST>"
   "SETNAVIGATIONGOAL-REQUEST"
   "<SETNAVIGATIONGOAL-RESPONSE>"
   "SETNAVIGATIONGOAL-RESPONSE"
  ))

