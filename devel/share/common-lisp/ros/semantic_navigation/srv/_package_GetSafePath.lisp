(cl:in-package semantic_navigation-srv)
(cl:export '(START_POSE-VAL
          START_POSE
          GOAL_POSE-VAL
          GOAL_POSE
          HAZARD_TYPES-VAL
          HAZARD_TYPES
          SAFETY_MARGIN-<PERSON><PERSON>
          SAFETY_MARGIN
          EMERGENCY_MODE-VA<PERSON>
          EMERGENCY_MODE
          SUCCESS-VAL
          SUCCESS
          MESSAGE-VAL
          MESSAGE
          SAFE_PATH-<PERSON><PERSON>
          SAFE_PATH
          ALTERNATIVE_PATHS-VAL
          ALTERNATIVE_PATHS
          SAFETY_SCORE-VAL
          SAFETY_SCORE
          WARNINGS-<PERSON><PERSON>
          WARNINGS
))