; Auto-generated. Do not edit!


(cl:in-package semantic_navigation-srv)


;//! \htmlinclude SetNavigationGoal-request.msg.html

(cl:defclass <SetNavigationGoal-request> (roslisp-msg-protocol:ros-message)
  ((goal
    :reader goal
    :initarg :goal
    :type semantic_navigation-msg:SemanticNavigationGoal
    :initform (cl:make-instance 'semantic_navigation-msg:SemanticNavigationGoal)))
)

(cl:defclass SetNavigationGoal-request (<SetNavigationGoal-request>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <SetNavigationGoal-request>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'SetNavigationGoal-request)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name semantic_navigation-srv:<SetNavigationGoal-request> is deprecated: use semantic_navigation-srv:SetNavigationGoal-request instead.")))

(cl:ensure-generic-function 'goal-val :lambda-list '(m))
(cl:defmethod goal-val ((m <SetNavigationGoal-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:goal-val is deprecated.  Use semantic_navigation-srv:goal instead.")
  (goal m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <SetNavigationGoal-request>) ostream)
  "Serializes a message object of type '<SetNavigationGoal-request>"
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'goal) ostream)
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <SetNavigationGoal-request>) istream)
  "Deserializes a message object of type '<SetNavigationGoal-request>"
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'goal) istream)
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<SetNavigationGoal-request>)))
  "Returns string type for a service object of type '<SetNavigationGoal-request>"
  "semantic_navigation/SetNavigationGoalRequest")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'SetNavigationGoal-request)))
  "Returns string type for a service object of type 'SetNavigationGoal-request"
  "semantic_navigation/SetNavigationGoalRequest")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<SetNavigationGoal-request>)))
  "Returns md5sum for a message object of type '<SetNavigationGoal-request>"
  "9b33341aa6d4bf3615200fa760500108")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'SetNavigationGoal-request)))
  "Returns md5sum for a message object of type 'SetNavigationGoal-request"
  "9b33341aa6d4bf3615200fa760500108")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<SetNavigationGoal-request>)))
  "Returns full string definition for message of type '<SetNavigationGoal-request>"
  (cl:format cl:nil "# 设置导航目标服务~%# 请求~%SemanticNavigationGoal goal~%~%~%================================================================================~%MSG: semantic_navigation/SemanticNavigationGoal~%# 语义导航目标消息~%Header header~%~%# 目标位置~%geometry_msgs/PoseStamped target_pose~%~%# 导航模式~%string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE~%~%# 语义约束~%string[] avoid_classes    # 需要避开的语义类别~%string[] prefer_classes   # 优先通过的语义类别~%~%# 安全参数~%float32 safety_distance   # 安全距离 (米)~%float32 fire_avoidance_distance  # 火焰避让距离 (米)~%~%# 优先级设置~%uint8 priority           # 导航优先级 (0-10, 10最高)~%bool emergency_mode      # 是否为应急模式~%~%# 超时设置~%duration timeout         # 导航超时时间~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'SetNavigationGoal-request)))
  "Returns full string definition for message of type 'SetNavigationGoal-request"
  (cl:format cl:nil "# 设置导航目标服务~%# 请求~%SemanticNavigationGoal goal~%~%~%================================================================================~%MSG: semantic_navigation/SemanticNavigationGoal~%# 语义导航目标消息~%Header header~%~%# 目标位置~%geometry_msgs/PoseStamped target_pose~%~%# 导航模式~%string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE~%~%# 语义约束~%string[] avoid_classes    # 需要避开的语义类别~%string[] prefer_classes   # 优先通过的语义类别~%~%# 安全参数~%float32 safety_distance   # 安全距离 (米)~%float32 fire_avoidance_distance  # 火焰避让距离 (米)~%~%# 优先级设置~%uint8 priority           # 导航优先级 (0-10, 10最高)~%bool emergency_mode      # 是否为应急模式~%~%# 超时设置~%duration timeout         # 导航超时时间~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <SetNavigationGoal-request>))
  (cl:+ 0
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'goal))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <SetNavigationGoal-request>))
  "Converts a ROS message object to a list"
  (cl:list 'SetNavigationGoal-request
    (cl:cons ':goal (goal msg))
))
;//! \htmlinclude SetNavigationGoal-response.msg.html

(cl:defclass <SetNavigationGoal-response> (roslisp-msg-protocol:ros-message)
  ((success
    :reader success
    :initarg :success
    :type cl:boolean
    :initform cl:nil)
   (message
    :reader message
    :initarg :message
    :type cl:string
    :initform "")
   (goal_id
    :reader goal_id
    :initarg :goal_id
    :type cl:string
    :initform "")
   (estimated_time
    :reader estimated_time
    :initarg :estimated_time
    :type cl:float
    :initform 0.0)
   (planned_path
    :reader planned_path
    :initarg :planned_path
    :type semantic_navigation-msg:SemanticPath
    :initform (cl:make-instance 'semantic_navigation-msg:SemanticPath)))
)

(cl:defclass SetNavigationGoal-response (<SetNavigationGoal-response>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <SetNavigationGoal-response>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'SetNavigationGoal-response)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name semantic_navigation-srv:<SetNavigationGoal-response> is deprecated: use semantic_navigation-srv:SetNavigationGoal-response instead.")))

(cl:ensure-generic-function 'success-val :lambda-list '(m))
(cl:defmethod success-val ((m <SetNavigationGoal-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:success-val is deprecated.  Use semantic_navigation-srv:success instead.")
  (success m))

(cl:ensure-generic-function 'message-val :lambda-list '(m))
(cl:defmethod message-val ((m <SetNavigationGoal-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:message-val is deprecated.  Use semantic_navigation-srv:message instead.")
  (message m))

(cl:ensure-generic-function 'goal_id-val :lambda-list '(m))
(cl:defmethod goal_id-val ((m <SetNavigationGoal-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:goal_id-val is deprecated.  Use semantic_navigation-srv:goal_id instead.")
  (goal_id m))

(cl:ensure-generic-function 'estimated_time-val :lambda-list '(m))
(cl:defmethod estimated_time-val ((m <SetNavigationGoal-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:estimated_time-val is deprecated.  Use semantic_navigation-srv:estimated_time instead.")
  (estimated_time m))

(cl:ensure-generic-function 'planned_path-val :lambda-list '(m))
(cl:defmethod planned_path-val ((m <SetNavigationGoal-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:planned_path-val is deprecated.  Use semantic_navigation-srv:planned_path instead.")
  (planned_path m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <SetNavigationGoal-response>) ostream)
  "Serializes a message object of type '<SetNavigationGoal-response>"
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'success) 1 0)) ostream)
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'message))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'message))
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'goal_id))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'goal_id))
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'estimated_time))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'planned_path) ostream)
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <SetNavigationGoal-response>) istream)
  "Deserializes a message object of type '<SetNavigationGoal-response>"
    (cl:setf (cl:slot-value msg 'success) (cl:not (cl:zerop (cl:read-byte istream))))
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'message) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'message) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'goal_id) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'goal_id) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'estimated_time) (roslisp-utils:decode-single-float-bits bits)))
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'planned_path) istream)
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<SetNavigationGoal-response>)))
  "Returns string type for a service object of type '<SetNavigationGoal-response>"
  "semantic_navigation/SetNavigationGoalResponse")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'SetNavigationGoal-response)))
  "Returns string type for a service object of type 'SetNavigationGoal-response"
  "semantic_navigation/SetNavigationGoalResponse")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<SetNavigationGoal-response>)))
  "Returns md5sum for a message object of type '<SetNavigationGoal-response>"
  "9b33341aa6d4bf3615200fa760500108")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'SetNavigationGoal-response)))
  "Returns md5sum for a message object of type 'SetNavigationGoal-response"
  "9b33341aa6d4bf3615200fa760500108")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<SetNavigationGoal-response>)))
  "Returns full string definition for message of type '<SetNavigationGoal-response>"
  (cl:format cl:nil "~%# 响应~%bool success~%string message~%string goal_id              # 目标ID，用于跟踪~%float32 estimated_time      # 预计完成时间~%SemanticPath planned_path   # 规划的路径~%~%~%================================================================================~%MSG: semantic_navigation/SemanticPath~%# 语义路径消息~%Header header~%~%# 路径信息~%nav_msgs/Path path~%~%# 语义信息~%string[] semantic_labels     # 路径上的语义标签~%float32[] semantic_costs     # 对应的语义代价~%float32[] safety_scores      # 安全性评分~%~%# 路径属性~%float32 total_distance       # 总距离~%float32 total_cost          # 总代价~%float32 safety_rating       # 安全评级 (0-1)~%bool contains_fire_risk     # 是否包含火灾风险~%~%# 应急信息~%geometry_msgs/Point[] emergency_exits  # 应急出口位置~%string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: nav_msgs/Path~%#An array of poses that represents a Path for a robot to follow~%Header header~%geometry_msgs/PoseStamped[] poses~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'SetNavigationGoal-response)))
  "Returns full string definition for message of type 'SetNavigationGoal-response"
  (cl:format cl:nil "~%# 响应~%bool success~%string message~%string goal_id              # 目标ID，用于跟踪~%float32 estimated_time      # 预计完成时间~%SemanticPath planned_path   # 规划的路径~%~%~%================================================================================~%MSG: semantic_navigation/SemanticPath~%# 语义路径消息~%Header header~%~%# 路径信息~%nav_msgs/Path path~%~%# 语义信息~%string[] semantic_labels     # 路径上的语义标签~%float32[] semantic_costs     # 对应的语义代价~%float32[] safety_scores      # 安全性评分~%~%# 路径属性~%float32 total_distance       # 总距离~%float32 total_cost          # 总代价~%float32 safety_rating       # 安全评级 (0-1)~%bool contains_fire_risk     # 是否包含火灾风险~%~%# 应急信息~%geometry_msgs/Point[] emergency_exits  # 应急出口位置~%string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: nav_msgs/Path~%#An array of poses that represents a Path for a robot to follow~%Header header~%geometry_msgs/PoseStamped[] poses~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <SetNavigationGoal-response>))
  (cl:+ 0
     1
     4 (cl:length (cl:slot-value msg 'message))
     4 (cl:length (cl:slot-value msg 'goal_id))
     4
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'planned_path))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <SetNavigationGoal-response>))
  "Converts a ROS message object to a list"
  (cl:list 'SetNavigationGoal-response
    (cl:cons ':success (success msg))
    (cl:cons ':message (message msg))
    (cl:cons ':goal_id (goal_id msg))
    (cl:cons ':estimated_time (estimated_time msg))
    (cl:cons ':planned_path (planned_path msg))
))
(cl:defmethod roslisp-msg-protocol:service-request-type ((msg (cl:eql 'SetNavigationGoal)))
  'SetNavigationGoal-request)
(cl:defmethod roslisp-msg-protocol:service-response-type ((msg (cl:eql 'SetNavigationGoal)))
  'SetNavigationGoal-response)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'SetNavigationGoal)))
  "Returns string type for a service object of type '<SetNavigationGoal>"
  "semantic_navigation/SetNavigationGoal")