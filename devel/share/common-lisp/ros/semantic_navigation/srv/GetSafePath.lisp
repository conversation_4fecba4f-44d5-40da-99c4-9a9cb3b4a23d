; Auto-generated. Do not edit!


(cl:in-package semantic_navigation-srv)


;//! \htmlinclude GetSafePath-request.msg.html

(cl:defclass <GetSafePath-request> (roslisp-msg-protocol:ros-message)
  ((start_pose
    :reader start_pose
    :initarg :start_pose
    :type geometry_msgs-msg:PoseStamped
    :initform (cl:make-instance 'geometry_msgs-msg:PoseStamped))
   (goal_pose
    :reader goal_pose
    :initarg :goal_pose
    :type geometry_msgs-msg:PoseStamped
    :initform (cl:make-instance 'geometry_msgs-msg:PoseStamped))
   (hazard_types
    :reader hazard_types
    :initarg :hazard_types
    :type (cl:vector cl:string)
   :initform (cl:make-array 0 :element-type 'cl:string :initial-element ""))
   (safety_margin
    :reader safety_margin
    :initarg :safety_margin
    :type cl:float
    :initform 0.0)
   (emergency_mode
    :reader emergency_mode
    :initarg :emergency_mode
    :type cl:boolean
    :initform cl:nil))
)

(cl:defclass GetSafePath-request (<GetSafePath-request>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <GetSafePath-request>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'GetSafePath-request)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name semantic_navigation-srv:<GetSafePath-request> is deprecated: use semantic_navigation-srv:GetSafePath-request instead.")))

(cl:ensure-generic-function 'start_pose-val :lambda-list '(m))
(cl:defmethod start_pose-val ((m <GetSafePath-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:start_pose-val is deprecated.  Use semantic_navigation-srv:start_pose instead.")
  (start_pose m))

(cl:ensure-generic-function 'goal_pose-val :lambda-list '(m))
(cl:defmethod goal_pose-val ((m <GetSafePath-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:goal_pose-val is deprecated.  Use semantic_navigation-srv:goal_pose instead.")
  (goal_pose m))

(cl:ensure-generic-function 'hazard_types-val :lambda-list '(m))
(cl:defmethod hazard_types-val ((m <GetSafePath-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:hazard_types-val is deprecated.  Use semantic_navigation-srv:hazard_types instead.")
  (hazard_types m))

(cl:ensure-generic-function 'safety_margin-val :lambda-list '(m))
(cl:defmethod safety_margin-val ((m <GetSafePath-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:safety_margin-val is deprecated.  Use semantic_navigation-srv:safety_margin instead.")
  (safety_margin m))

(cl:ensure-generic-function 'emergency_mode-val :lambda-list '(m))
(cl:defmethod emergency_mode-val ((m <GetSafePath-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:emergency_mode-val is deprecated.  Use semantic_navigation-srv:emergency_mode instead.")
  (emergency_mode m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <GetSafePath-request>) ostream)
  "Serializes a message object of type '<GetSafePath-request>"
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'start_pose) ostream)
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'goal_pose) ostream)
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'hazard_types))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (cl:let ((__ros_str_len (cl:length ele)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) ele))
   (cl:slot-value msg 'hazard_types))
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'safety_margin))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'emergency_mode) 1 0)) ostream)
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <GetSafePath-request>) istream)
  "Deserializes a message object of type '<GetSafePath-request>"
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'start_pose) istream)
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'goal_pose) istream)
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'hazard_types) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'hazard_types)))
    (cl:dotimes (i __ros_arr_len)
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:aref vals i) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:aref vals i) __ros_str_idx) (cl:code-char (cl:read-byte istream))))))))
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'safety_margin) (roslisp-utils:decode-single-float-bits bits)))
    (cl:setf (cl:slot-value msg 'emergency_mode) (cl:not (cl:zerop (cl:read-byte istream))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<GetSafePath-request>)))
  "Returns string type for a service object of type '<GetSafePath-request>"
  "semantic_navigation/GetSafePathRequest")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'GetSafePath-request)))
  "Returns string type for a service object of type 'GetSafePath-request"
  "semantic_navigation/GetSafePathRequest")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<GetSafePath-request>)))
  "Returns md5sum for a message object of type '<GetSafePath-request>"
  "677123b52cef0c55e41c66d038e4b3a2")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'GetSafePath-request)))
  "Returns md5sum for a message object of type 'GetSafePath-request"
  "677123b52cef0c55e41c66d038e4b3a2")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<GetSafePath-request>)))
  "Returns full string definition for message of type '<GetSafePath-request>"
  (cl:format cl:nil "# 获取安全路径服务~%# 请求~%geometry_msgs/PoseStamped start_pose~%geometry_msgs/PoseStamped goal_pose~%string[] hazard_types       # 需要避开的危险类型~%float32 safety_margin       # 安全边距~%bool emergency_mode         # 是否为应急模式~%~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'GetSafePath-request)))
  "Returns full string definition for message of type 'GetSafePath-request"
  (cl:format cl:nil "# 获取安全路径服务~%# 请求~%geometry_msgs/PoseStamped start_pose~%geometry_msgs/PoseStamped goal_pose~%string[] hazard_types       # 需要避开的危险类型~%float32 safety_margin       # 安全边距~%bool emergency_mode         # 是否为应急模式~%~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <GetSafePath-request>))
  (cl:+ 0
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'start_pose))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'goal_pose))
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'hazard_types) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ 4 (cl:length ele))))
     4
     1
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <GetSafePath-request>))
  "Converts a ROS message object to a list"
  (cl:list 'GetSafePath-request
    (cl:cons ':start_pose (start_pose msg))
    (cl:cons ':goal_pose (goal_pose msg))
    (cl:cons ':hazard_types (hazard_types msg))
    (cl:cons ':safety_margin (safety_margin msg))
    (cl:cons ':emergency_mode (emergency_mode msg))
))
;//! \htmlinclude GetSafePath-response.msg.html

(cl:defclass <GetSafePath-response> (roslisp-msg-protocol:ros-message)
  ((success
    :reader success
    :initarg :success
    :type cl:boolean
    :initform cl:nil)
   (message
    :reader message
    :initarg :message
    :type cl:string
    :initform "")
   (safe_path
    :reader safe_path
    :initarg :safe_path
    :type semantic_navigation-msg:SemanticPath
    :initform (cl:make-instance 'semantic_navigation-msg:SemanticPath))
   (alternative_paths
    :reader alternative_paths
    :initarg :alternative_paths
    :type (cl:vector semantic_navigation-msg:SemanticPath)
   :initform (cl:make-array 0 :element-type 'semantic_navigation-msg:SemanticPath :initial-element (cl:make-instance 'semantic_navigation-msg:SemanticPath)))
   (safety_score
    :reader safety_score
    :initarg :safety_score
    :type cl:float
    :initform 0.0)
   (warnings
    :reader warnings
    :initarg :warnings
    :type (cl:vector cl:string)
   :initform (cl:make-array 0 :element-type 'cl:string :initial-element "")))
)

(cl:defclass GetSafePath-response (<GetSafePath-response>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <GetSafePath-response>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'GetSafePath-response)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name semantic_navigation-srv:<GetSafePath-response> is deprecated: use semantic_navigation-srv:GetSafePath-response instead.")))

(cl:ensure-generic-function 'success-val :lambda-list '(m))
(cl:defmethod success-val ((m <GetSafePath-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:success-val is deprecated.  Use semantic_navigation-srv:success instead.")
  (success m))

(cl:ensure-generic-function 'message-val :lambda-list '(m))
(cl:defmethod message-val ((m <GetSafePath-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:message-val is deprecated.  Use semantic_navigation-srv:message instead.")
  (message m))

(cl:ensure-generic-function 'safe_path-val :lambda-list '(m))
(cl:defmethod safe_path-val ((m <GetSafePath-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:safe_path-val is deprecated.  Use semantic_navigation-srv:safe_path instead.")
  (safe_path m))

(cl:ensure-generic-function 'alternative_paths-val :lambda-list '(m))
(cl:defmethod alternative_paths-val ((m <GetSafePath-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:alternative_paths-val is deprecated.  Use semantic_navigation-srv:alternative_paths instead.")
  (alternative_paths m))

(cl:ensure-generic-function 'safety_score-val :lambda-list '(m))
(cl:defmethod safety_score-val ((m <GetSafePath-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:safety_score-val is deprecated.  Use semantic_navigation-srv:safety_score instead.")
  (safety_score m))

(cl:ensure-generic-function 'warnings-val :lambda-list '(m))
(cl:defmethod warnings-val ((m <GetSafePath-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-srv:warnings-val is deprecated.  Use semantic_navigation-srv:warnings instead.")
  (warnings m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <GetSafePath-response>) ostream)
  "Serializes a message object of type '<GetSafePath-response>"
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'success) 1 0)) ostream)
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'message))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'message))
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'safe_path) ostream)
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'alternative_paths))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (roslisp-msg-protocol:serialize ele ostream))
   (cl:slot-value msg 'alternative_paths))
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'safety_score))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'warnings))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (cl:let ((__ros_str_len (cl:length ele)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) ele))
   (cl:slot-value msg 'warnings))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <GetSafePath-response>) istream)
  "Deserializes a message object of type '<GetSafePath-response>"
    (cl:setf (cl:slot-value msg 'success) (cl:not (cl:zerop (cl:read-byte istream))))
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'message) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'message) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'safe_path) istream)
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'alternative_paths) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'alternative_paths)))
    (cl:dotimes (i __ros_arr_len)
    (cl:setf (cl:aref vals i) (cl:make-instance 'semantic_navigation-msg:SemanticPath))
  (roslisp-msg-protocol:deserialize (cl:aref vals i) istream))))
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'safety_score) (roslisp-utils:decode-single-float-bits bits)))
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'warnings) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'warnings)))
    (cl:dotimes (i __ros_arr_len)
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:aref vals i) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:aref vals i) __ros_str_idx) (cl:code-char (cl:read-byte istream))))))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<GetSafePath-response>)))
  "Returns string type for a service object of type '<GetSafePath-response>"
  "semantic_navigation/GetSafePathResponse")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'GetSafePath-response)))
  "Returns string type for a service object of type 'GetSafePath-response"
  "semantic_navigation/GetSafePathResponse")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<GetSafePath-response>)))
  "Returns md5sum for a message object of type '<GetSafePath-response>"
  "677123b52cef0c55e41c66d038e4b3a2")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'GetSafePath-response)))
  "Returns md5sum for a message object of type 'GetSafePath-response"
  "677123b52cef0c55e41c66d038e4b3a2")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<GetSafePath-response>)))
  "Returns full string definition for message of type '<GetSafePath-response>"
  (cl:format cl:nil "~%# 响应~%bool success~%string message~%SemanticPath safe_path      # 安全路径~%SemanticPath[] alternative_paths  # 备选路径~%float32 safety_score        # 安全评分~%string[] warnings           # 警告信息~%~%~%================================================================================~%MSG: semantic_navigation/SemanticPath~%# 语义路径消息~%Header header~%~%# 路径信息~%nav_msgs/Path path~%~%# 语义信息~%string[] semantic_labels     # 路径上的语义标签~%float32[] semantic_costs     # 对应的语义代价~%float32[] safety_scores      # 安全性评分~%~%# 路径属性~%float32 total_distance       # 总距离~%float32 total_cost          # 总代价~%float32 safety_rating       # 安全评级 (0-1)~%bool contains_fire_risk     # 是否包含火灾风险~%~%# 应急信息~%geometry_msgs/Point[] emergency_exits  # 应急出口位置~%string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: nav_msgs/Path~%#An array of poses that represents a Path for a robot to follow~%Header header~%geometry_msgs/PoseStamped[] poses~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'GetSafePath-response)))
  "Returns full string definition for message of type 'GetSafePath-response"
  (cl:format cl:nil "~%# 响应~%bool success~%string message~%SemanticPath safe_path      # 安全路径~%SemanticPath[] alternative_paths  # 备选路径~%float32 safety_score        # 安全评分~%string[] warnings           # 警告信息~%~%~%================================================================================~%MSG: semantic_navigation/SemanticPath~%# 语义路径消息~%Header header~%~%# 路径信息~%nav_msgs/Path path~%~%# 语义信息~%string[] semantic_labels     # 路径上的语义标签~%float32[] semantic_costs     # 对应的语义代价~%float32[] safety_scores      # 安全性评分~%~%# 路径属性~%float32 total_distance       # 总距离~%float32 total_cost          # 总代价~%float32 safety_rating       # 安全评级 (0-1)~%bool contains_fire_risk     # 是否包含火灾风险~%~%# 应急信息~%geometry_msgs/Point[] emergency_exits  # 应急出口位置~%string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: nav_msgs/Path~%#An array of poses that represents a Path for a robot to follow~%Header header~%geometry_msgs/PoseStamped[] poses~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <GetSafePath-response>))
  (cl:+ 0
     1
     4 (cl:length (cl:slot-value msg 'message))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'safe_path))
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'alternative_paths) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ (roslisp-msg-protocol:serialization-length ele))))
     4
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'warnings) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ 4 (cl:length ele))))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <GetSafePath-response>))
  "Converts a ROS message object to a list"
  (cl:list 'GetSafePath-response
    (cl:cons ':success (success msg))
    (cl:cons ':message (message msg))
    (cl:cons ':safe_path (safe_path msg))
    (cl:cons ':alternative_paths (alternative_paths msg))
    (cl:cons ':safety_score (safety_score msg))
    (cl:cons ':warnings (warnings msg))
))
(cl:defmethod roslisp-msg-protocol:service-request-type ((msg (cl:eql 'GetSafePath)))
  'GetSafePath-request)
(cl:defmethod roslisp-msg-protocol:service-response-type ((msg (cl:eql 'GetSafePath)))
  'GetSafePath-response)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'GetSafePath)))
  "Returns string type for a service object of type '<GetSafePath>"
  "semantic_navigation/GetSafePath")