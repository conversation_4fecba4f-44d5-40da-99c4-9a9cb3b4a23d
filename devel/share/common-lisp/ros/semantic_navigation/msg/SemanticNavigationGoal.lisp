; Auto-generated. Do not edit!


(cl:in-package semantic_navigation-msg)


;//! \htmlinclude SemanticNavigationGoal.msg.html

(cl:defclass <SemanticNavigationGoal> (roslisp-msg-protocol:ros-message)
  ((header
    :reader header
    :initarg :header
    :type std_msgs-msg:Header
    :initform (cl:make-instance 'std_msgs-msg:Header))
   (target_pose
    :reader target_pose
    :initarg :target_pose
    :type geometry_msgs-msg:PoseStamped
    :initform (cl:make-instance 'geometry_msgs-msg:PoseStamped))
   (navigation_mode
    :reader navigation_mode
    :initarg :navigation_mode
    :type cl:string
    :initform "")
   (avoid_classes
    :reader avoid_classes
    :initarg :avoid_classes
    :type (cl:vector cl:string)
   :initform (cl:make-array 0 :element-type 'cl:string :initial-element ""))
   (prefer_classes
    :reader prefer_classes
    :initarg :prefer_classes
    :type (cl:vector cl:string)
   :initform (cl:make-array 0 :element-type 'cl:string :initial-element ""))
   (safety_distance
    :reader safety_distance
    :initarg :safety_distance
    :type cl:float
    :initform 0.0)
   (fire_avoidance_distance
    :reader fire_avoidance_distance
    :initarg :fire_avoidance_distance
    :type cl:float
    :initform 0.0)
   (priority
    :reader priority
    :initarg :priority
    :type cl:fixnum
    :initform 0)
   (emergency_mode
    :reader emergency_mode
    :initarg :emergency_mode
    :type cl:boolean
    :initform cl:nil)
   (timeout
    :reader timeout
    :initarg :timeout
    :type cl:real
    :initform 0))
)

(cl:defclass SemanticNavigationGoal (<SemanticNavigationGoal>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <SemanticNavigationGoal>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'SemanticNavigationGoal)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name semantic_navigation-msg:<SemanticNavigationGoal> is deprecated: use semantic_navigation-msg:SemanticNavigationGoal instead.")))

(cl:ensure-generic-function 'header-val :lambda-list '(m))
(cl:defmethod header-val ((m <SemanticNavigationGoal>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:header-val is deprecated.  Use semantic_navigation-msg:header instead.")
  (header m))

(cl:ensure-generic-function 'target_pose-val :lambda-list '(m))
(cl:defmethod target_pose-val ((m <SemanticNavigationGoal>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:target_pose-val is deprecated.  Use semantic_navigation-msg:target_pose instead.")
  (target_pose m))

(cl:ensure-generic-function 'navigation_mode-val :lambda-list '(m))
(cl:defmethod navigation_mode-val ((m <SemanticNavigationGoal>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:navigation_mode-val is deprecated.  Use semantic_navigation-msg:navigation_mode instead.")
  (navigation_mode m))

(cl:ensure-generic-function 'avoid_classes-val :lambda-list '(m))
(cl:defmethod avoid_classes-val ((m <SemanticNavigationGoal>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:avoid_classes-val is deprecated.  Use semantic_navigation-msg:avoid_classes instead.")
  (avoid_classes m))

(cl:ensure-generic-function 'prefer_classes-val :lambda-list '(m))
(cl:defmethod prefer_classes-val ((m <SemanticNavigationGoal>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:prefer_classes-val is deprecated.  Use semantic_navigation-msg:prefer_classes instead.")
  (prefer_classes m))

(cl:ensure-generic-function 'safety_distance-val :lambda-list '(m))
(cl:defmethod safety_distance-val ((m <SemanticNavigationGoal>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:safety_distance-val is deprecated.  Use semantic_navigation-msg:safety_distance instead.")
  (safety_distance m))

(cl:ensure-generic-function 'fire_avoidance_distance-val :lambda-list '(m))
(cl:defmethod fire_avoidance_distance-val ((m <SemanticNavigationGoal>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:fire_avoidance_distance-val is deprecated.  Use semantic_navigation-msg:fire_avoidance_distance instead.")
  (fire_avoidance_distance m))

(cl:ensure-generic-function 'priority-val :lambda-list '(m))
(cl:defmethod priority-val ((m <SemanticNavigationGoal>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:priority-val is deprecated.  Use semantic_navigation-msg:priority instead.")
  (priority m))

(cl:ensure-generic-function 'emergency_mode-val :lambda-list '(m))
(cl:defmethod emergency_mode-val ((m <SemanticNavigationGoal>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:emergency_mode-val is deprecated.  Use semantic_navigation-msg:emergency_mode instead.")
  (emergency_mode m))

(cl:ensure-generic-function 'timeout-val :lambda-list '(m))
(cl:defmethod timeout-val ((m <SemanticNavigationGoal>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:timeout-val is deprecated.  Use semantic_navigation-msg:timeout instead.")
  (timeout m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <SemanticNavigationGoal>) ostream)
  "Serializes a message object of type '<SemanticNavigationGoal>"
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'header) ostream)
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'target_pose) ostream)
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'navigation_mode))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'navigation_mode))
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'avoid_classes))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (cl:let ((__ros_str_len (cl:length ele)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) ele))
   (cl:slot-value msg 'avoid_classes))
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'prefer_classes))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (cl:let ((__ros_str_len (cl:length ele)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) ele))
   (cl:slot-value msg 'prefer_classes))
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'safety_distance))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'fire_avoidance_distance))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:slot-value msg 'priority)) ostream)
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'emergency_mode) 1 0)) ostream)
  (cl:let ((__sec (cl:floor (cl:slot-value msg 'timeout)))
        (__nsec (cl:round (cl:* 1e9 (cl:- (cl:slot-value msg 'timeout) (cl:floor (cl:slot-value msg 'timeout)))))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 0) __nsec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __nsec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __nsec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __nsec) ostream))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <SemanticNavigationGoal>) istream)
  "Deserializes a message object of type '<SemanticNavigationGoal>"
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'header) istream)
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'target_pose) istream)
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'navigation_mode) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'navigation_mode) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'avoid_classes) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'avoid_classes)))
    (cl:dotimes (i __ros_arr_len)
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:aref vals i) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:aref vals i) __ros_str_idx) (cl:code-char (cl:read-byte istream))))))))
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'prefer_classes) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'prefer_classes)))
    (cl:dotimes (i __ros_arr_len)
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:aref vals i) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:aref vals i) __ros_str_idx) (cl:code-char (cl:read-byte istream))))))))
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'safety_distance) (roslisp-utils:decode-single-float-bits bits)))
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'fire_avoidance_distance) (roslisp-utils:decode-single-float-bits bits)))
    (cl:setf (cl:ldb (cl:byte 8 0) (cl:slot-value msg 'priority)) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'emergency_mode) (cl:not (cl:zerop (cl:read-byte istream))))
    (cl:let ((__sec 0) (__nsec 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 0) __nsec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __nsec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __nsec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __nsec) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'timeout) (cl:+ (cl:coerce __sec 'cl:double-float) (cl:/ __nsec 1e9))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<SemanticNavigationGoal>)))
  "Returns string type for a message object of type '<SemanticNavigationGoal>"
  "semantic_navigation/SemanticNavigationGoal")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'SemanticNavigationGoal)))
  "Returns string type for a message object of type 'SemanticNavigationGoal"
  "semantic_navigation/SemanticNavigationGoal")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<SemanticNavigationGoal>)))
  "Returns md5sum for a message object of type '<SemanticNavigationGoal>"
  "f3344e67e6ced968d5c3cc9238c45392")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'SemanticNavigationGoal)))
  "Returns md5sum for a message object of type 'SemanticNavigationGoal"
  "f3344e67e6ced968d5c3cc9238c45392")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<SemanticNavigationGoal>)))
  "Returns full string definition for message of type '<SemanticNavigationGoal>"
  (cl:format cl:nil "# 语义导航目标消息~%Header header~%~%# 目标位置~%geometry_msgs/PoseStamped target_pose~%~%# 导航模式~%string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE~%~%# 语义约束~%string[] avoid_classes    # 需要避开的语义类别~%string[] prefer_classes   # 优先通过的语义类别~%~%# 安全参数~%float32 safety_distance   # 安全距离 (米)~%float32 fire_avoidance_distance  # 火焰避让距离 (米)~%~%# 优先级设置~%uint8 priority           # 导航优先级 (0-10, 10最高)~%bool emergency_mode      # 是否为应急模式~%~%# 超时设置~%duration timeout         # 导航超时时间~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'SemanticNavigationGoal)))
  "Returns full string definition for message of type 'SemanticNavigationGoal"
  (cl:format cl:nil "# 语义导航目标消息~%Header header~%~%# 目标位置~%geometry_msgs/PoseStamped target_pose~%~%# 导航模式~%string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE~%~%# 语义约束~%string[] avoid_classes    # 需要避开的语义类别~%string[] prefer_classes   # 优先通过的语义类别~%~%# 安全参数~%float32 safety_distance   # 安全距离 (米)~%float32 fire_avoidance_distance  # 火焰避让距离 (米)~%~%# 优先级设置~%uint8 priority           # 导航优先级 (0-10, 10最高)~%bool emergency_mode      # 是否为应急模式~%~%# 超时设置~%duration timeout         # 导航超时时间~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <SemanticNavigationGoal>))
  (cl:+ 0
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'header))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'target_pose))
     4 (cl:length (cl:slot-value msg 'navigation_mode))
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'avoid_classes) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ 4 (cl:length ele))))
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'prefer_classes) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ 4 (cl:length ele))))
     4
     4
     1
     1
     8
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <SemanticNavigationGoal>))
  "Converts a ROS message object to a list"
  (cl:list 'SemanticNavigationGoal
    (cl:cons ':header (header msg))
    (cl:cons ':target_pose (target_pose msg))
    (cl:cons ':navigation_mode (navigation_mode msg))
    (cl:cons ':avoid_classes (avoid_classes msg))
    (cl:cons ':prefer_classes (prefer_classes msg))
    (cl:cons ':safety_distance (safety_distance msg))
    (cl:cons ':fire_avoidance_distance (fire_avoidance_distance msg))
    (cl:cons ':priority (priority msg))
    (cl:cons ':emergency_mode (emergency_mode msg))
    (cl:cons ':timeout (timeout msg))
))
