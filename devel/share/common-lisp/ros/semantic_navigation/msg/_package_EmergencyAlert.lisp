(cl:in-package semantic_navigation-msg)
(cl:export '(HEADER-VAL
          HEADER
          ALERT_TYPE-VAL
          ALERT_TYPE
          SEVERITY-VAL
          SEVERITY
          LOCATION-VAL
          LOCATION
          AFFECTED_RADIUS-VAL
          AFFECTED_RADIUS
          DESCRIPTION-VAL
          DESCRIPTION
          RECOMMENDED_ACTION-VAL
          RECOMMENDED_ACTION
          DETECTION_TIME-VAL
          DETECTION_TIME
          ESTIMATED_DURATION-VAL
          ESTIMATED_DURATION
          CONFIDENCE-VA<PERSON>
          CONFIDENCE
          RELATED_OBJECTS-VAL
          RELATED_OBJECTS
))