; Auto-generated. Do not edit!


(cl:in-package semantic_navigation-msg)


;//! \htmlinclude SemanticPath.msg.html

(cl:defclass <SemanticPath> (roslisp-msg-protocol:ros-message)
  ((header
    :reader header
    :initarg :header
    :type std_msgs-msg:Header
    :initform (cl:make-instance 'std_msgs-msg:Header))
   (path
    :reader path
    :initarg :path
    :type nav_msgs-msg:Path
    :initform (cl:make-instance 'nav_msgs-msg:Path))
   (semantic_labels
    :reader semantic_labels
    :initarg :semantic_labels
    :type (cl:vector cl:string)
   :initform (cl:make-array 0 :element-type 'cl:string :initial-element ""))
   (semantic_costs
    :reader semantic_costs
    :initarg :semantic_costs
    :type (cl:vector cl:float)
   :initform (cl:make-array 0 :element-type 'cl:float :initial-element 0.0))
   (safety_scores
    :reader safety_scores
    :initarg :safety_scores
    :type (cl:vector cl:float)
   :initform (cl:make-array 0 :element-type 'cl:float :initial-element 0.0))
   (total_distance
    :reader total_distance
    :initarg :total_distance
    :type cl:float
    :initform 0.0)
   (total_cost
    :reader total_cost
    :initarg :total_cost
    :type cl:float
    :initform 0.0)
   (safety_rating
    :reader safety_rating
    :initarg :safety_rating
    :type cl:float
    :initform 0.0)
   (contains_fire_risk
    :reader contains_fire_risk
    :initarg :contains_fire_risk
    :type cl:boolean
    :initform cl:nil)
   (emergency_exits
    :reader emergency_exits
    :initarg :emergency_exits
    :type (cl:vector geometry_msgs-msg:Point)
   :initform (cl:make-array 0 :element-type 'geometry_msgs-msg:Point :initial-element (cl:make-instance 'geometry_msgs-msg:Point)))
   (risk_level
    :reader risk_level
    :initarg :risk_level
    :type cl:string
    :initform ""))
)

(cl:defclass SemanticPath (<SemanticPath>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <SemanticPath>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'SemanticPath)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name semantic_navigation-msg:<SemanticPath> is deprecated: use semantic_navigation-msg:SemanticPath instead.")))

(cl:ensure-generic-function 'header-val :lambda-list '(m))
(cl:defmethod header-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:header-val is deprecated.  Use semantic_navigation-msg:header instead.")
  (header m))

(cl:ensure-generic-function 'path-val :lambda-list '(m))
(cl:defmethod path-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:path-val is deprecated.  Use semantic_navigation-msg:path instead.")
  (path m))

(cl:ensure-generic-function 'semantic_labels-val :lambda-list '(m))
(cl:defmethod semantic_labels-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:semantic_labels-val is deprecated.  Use semantic_navigation-msg:semantic_labels instead.")
  (semantic_labels m))

(cl:ensure-generic-function 'semantic_costs-val :lambda-list '(m))
(cl:defmethod semantic_costs-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:semantic_costs-val is deprecated.  Use semantic_navigation-msg:semantic_costs instead.")
  (semantic_costs m))

(cl:ensure-generic-function 'safety_scores-val :lambda-list '(m))
(cl:defmethod safety_scores-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:safety_scores-val is deprecated.  Use semantic_navigation-msg:safety_scores instead.")
  (safety_scores m))

(cl:ensure-generic-function 'total_distance-val :lambda-list '(m))
(cl:defmethod total_distance-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:total_distance-val is deprecated.  Use semantic_navigation-msg:total_distance instead.")
  (total_distance m))

(cl:ensure-generic-function 'total_cost-val :lambda-list '(m))
(cl:defmethod total_cost-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:total_cost-val is deprecated.  Use semantic_navigation-msg:total_cost instead.")
  (total_cost m))

(cl:ensure-generic-function 'safety_rating-val :lambda-list '(m))
(cl:defmethod safety_rating-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:safety_rating-val is deprecated.  Use semantic_navigation-msg:safety_rating instead.")
  (safety_rating m))

(cl:ensure-generic-function 'contains_fire_risk-val :lambda-list '(m))
(cl:defmethod contains_fire_risk-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:contains_fire_risk-val is deprecated.  Use semantic_navigation-msg:contains_fire_risk instead.")
  (contains_fire_risk m))

(cl:ensure-generic-function 'emergency_exits-val :lambda-list '(m))
(cl:defmethod emergency_exits-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:emergency_exits-val is deprecated.  Use semantic_navigation-msg:emergency_exits instead.")
  (emergency_exits m))

(cl:ensure-generic-function 'risk_level-val :lambda-list '(m))
(cl:defmethod risk_level-val ((m <SemanticPath>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:risk_level-val is deprecated.  Use semantic_navigation-msg:risk_level instead.")
  (risk_level m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <SemanticPath>) ostream)
  "Serializes a message object of type '<SemanticPath>"
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'header) ostream)
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'path) ostream)
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'semantic_labels))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (cl:let ((__ros_str_len (cl:length ele)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) ele))
   (cl:slot-value msg 'semantic_labels))
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'semantic_costs))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (cl:let ((bits (roslisp-utils:encode-single-float-bits ele)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream)))
   (cl:slot-value msg 'semantic_costs))
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'safety_scores))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (cl:let ((bits (roslisp-utils:encode-single-float-bits ele)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream)))
   (cl:slot-value msg 'safety_scores))
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'total_distance))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'total_cost))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'safety_rating))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'contains_fire_risk) 1 0)) ostream)
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'emergency_exits))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (roslisp-msg-protocol:serialize ele ostream))
   (cl:slot-value msg 'emergency_exits))
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'risk_level))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'risk_level))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <SemanticPath>) istream)
  "Deserializes a message object of type '<SemanticPath>"
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'header) istream)
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'path) istream)
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'semantic_labels) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'semantic_labels)))
    (cl:dotimes (i __ros_arr_len)
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:aref vals i) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:aref vals i) __ros_str_idx) (cl:code-char (cl:read-byte istream))))))))
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'semantic_costs) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'semantic_costs)))
    (cl:dotimes (i __ros_arr_len)
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:aref vals i) (roslisp-utils:decode-single-float-bits bits))))))
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'safety_scores) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'safety_scores)))
    (cl:dotimes (i __ros_arr_len)
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:aref vals i) (roslisp-utils:decode-single-float-bits bits))))))
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'total_distance) (roslisp-utils:decode-single-float-bits bits)))
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'total_cost) (roslisp-utils:decode-single-float-bits bits)))
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'safety_rating) (roslisp-utils:decode-single-float-bits bits)))
    (cl:setf (cl:slot-value msg 'contains_fire_risk) (cl:not (cl:zerop (cl:read-byte istream))))
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'emergency_exits) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'emergency_exits)))
    (cl:dotimes (i __ros_arr_len)
    (cl:setf (cl:aref vals i) (cl:make-instance 'geometry_msgs-msg:Point))
  (roslisp-msg-protocol:deserialize (cl:aref vals i) istream))))
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'risk_level) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'risk_level) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<SemanticPath>)))
  "Returns string type for a message object of type '<SemanticPath>"
  "semantic_navigation/SemanticPath")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'SemanticPath)))
  "Returns string type for a message object of type 'SemanticPath"
  "semantic_navigation/SemanticPath")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<SemanticPath>)))
  "Returns md5sum for a message object of type '<SemanticPath>"
  "0320dcd1e20996da4804a029f13c56f2")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'SemanticPath)))
  "Returns md5sum for a message object of type 'SemanticPath"
  "0320dcd1e20996da4804a029f13c56f2")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<SemanticPath>)))
  "Returns full string definition for message of type '<SemanticPath>"
  (cl:format cl:nil "# 语义路径消息~%Header header~%~%# 路径信息~%nav_msgs/Path path~%~%# 语义信息~%string[] semantic_labels     # 路径上的语义标签~%float32[] semantic_costs     # 对应的语义代价~%float32[] safety_scores      # 安全性评分~%~%# 路径属性~%float32 total_distance       # 总距离~%float32 total_cost          # 总代价~%float32 safety_rating       # 安全评级 (0-1)~%bool contains_fire_risk     # 是否包含火灾风险~%~%# 应急信息~%geometry_msgs/Point[] emergency_exits  # 应急出口位置~%string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: nav_msgs/Path~%#An array of poses that represents a Path for a robot to follow~%Header header~%geometry_msgs/PoseStamped[] poses~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'SemanticPath)))
  "Returns full string definition for message of type 'SemanticPath"
  (cl:format cl:nil "# 语义路径消息~%Header header~%~%# 路径信息~%nav_msgs/Path path~%~%# 语义信息~%string[] semantic_labels     # 路径上的语义标签~%float32[] semantic_costs     # 对应的语义代价~%float32[] safety_scores      # 安全性评分~%~%# 路径属性~%float32 total_distance       # 总距离~%float32 total_cost          # 总代价~%float32 safety_rating       # 安全评级 (0-1)~%bool contains_fire_risk     # 是否包含火灾风险~%~%# 应急信息~%geometry_msgs/Point[] emergency_exits  # 应急出口位置~%string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: nav_msgs/Path~%#An array of poses that represents a Path for a robot to follow~%Header header~%geometry_msgs/PoseStamped[] poses~%~%================================================================================~%MSG: geometry_msgs/PoseStamped~%# A Pose with reference coordinate frame and timestamp~%Header header~%Pose pose~%~%================================================================================~%MSG: geometry_msgs/Pose~%# A representation of pose in free space, composed of position and orientation. ~%Point position~%Quaternion orientation~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Quaternion~%# This represents an orientation in free space in quaternion form.~%~%float64 x~%float64 y~%float64 z~%float64 w~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <SemanticPath>))
  (cl:+ 0
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'header))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'path))
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'semantic_labels) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ 4 (cl:length ele))))
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'semantic_costs) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ 4)))
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'safety_scores) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ 4)))
     4
     4
     4
     1
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'emergency_exits) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ (roslisp-msg-protocol:serialization-length ele))))
     4 (cl:length (cl:slot-value msg 'risk_level))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <SemanticPath>))
  "Converts a ROS message object to a list"
  (cl:list 'SemanticPath
    (cl:cons ':header (header msg))
    (cl:cons ':path (path msg))
    (cl:cons ':semantic_labels (semantic_labels msg))
    (cl:cons ':semantic_costs (semantic_costs msg))
    (cl:cons ':safety_scores (safety_scores msg))
    (cl:cons ':total_distance (total_distance msg))
    (cl:cons ':total_cost (total_cost msg))
    (cl:cons ':safety_rating (safety_rating msg))
    (cl:cons ':contains_fire_risk (contains_fire_risk msg))
    (cl:cons ':emergency_exits (emergency_exits msg))
    (cl:cons ':risk_level (risk_level msg))
))
