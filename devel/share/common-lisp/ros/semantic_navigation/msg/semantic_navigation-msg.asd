
(cl:in-package :asdf)

(defsystem "semantic_navigation-msg"
  :depends-on (:roslisp-msg-protocol :roslisp-utils :geometry_msgs-msg
               :nav_msgs-msg
               :std_msgs-msg
)
  :components ((:file "_package")
    (:file "EmergencyAlert" :depends-on ("_package_EmergencyAlert"))
    (:file "_package_EmergencyAlert" :depends-on ("_package"))
    (:file "NavigationStatus" :depends-on ("_package_NavigationStatus"))
    (:file "_package_NavigationStatus" :depends-on ("_package"))
    (:file "SemanticNavigationGoal" :depends-on ("_package_SemanticNavigationGoal"))
    (:file "_package_SemanticNavigationGoal" :depends-on ("_package"))
    (:file "SemanticPath" :depends-on ("_package_SemanticPath"))
    (:file "_package_SemanticPath" :depends-on ("_package"))
  ))