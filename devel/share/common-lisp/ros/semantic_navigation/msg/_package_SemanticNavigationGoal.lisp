(cl:in-package semantic_navigation-msg)
(cl:export '(HEADER-<PERSON><PERSON>
          HEADER
          TARGET_POSE-VAL
          TARGET_POSE
          NAVIGATION_MODE-VAL
          NAVIGATION_MODE
          AVOID_CLASSES-VAL
          AVOID_CLASSES
          PREFER_CLASSES-VAL
          PREFER_CLASSES
          SAFETY_DISTANCE-VAL
          SAFETY_DISTANCE
          FIRE_AVOIDANCE_DISTANCE-VAL
          FIRE_AVOIDANCE_DISTANCE
          PRIORITY-VAL
          PRIORITY
          EMERGENCY_MODE-VAL
          EMERGENCY_MODE
          TIMEOUT-<PERSON><PERSON>
          TIMEOUT
))