; Auto-generated. Do not edit!


(cl:in-package semantic_navigation-msg)


;//! \htmlinclude EmergencyAlert.msg.html

(cl:defclass <EmergencyAlert> (roslisp-msg-protocol:ros-message)
  ((header
    :reader header
    :initarg :header
    :type std_msgs-msg:Header
    :initform (cl:make-instance 'std_msgs-msg:Header))
   (alert_type
    :reader alert_type
    :initarg :alert_type
    :type cl:string
    :initform "")
   (severity
    :reader severity
    :initarg :severity
    :type cl:string
    :initform "")
   (location
    :reader location
    :initarg :location
    :type geometry_msgs-msg:Point
    :initform (cl:make-instance 'geometry_msgs-msg:Point))
   (affected_radius
    :reader affected_radius
    :initarg :affected_radius
    :type cl:float
    :initform 0.0)
   (description
    :reader description
    :initarg :description
    :type cl:string
    :initform "")
   (recommended_action
    :reader recommended_action
    :initarg :recommended_action
    :type cl:string
    :initform "")
   (detection_time
    :reader detection_time
    :initarg :detection_time
    :type cl:real
    :initform 0)
   (estimated_duration
    :reader estimated_duration
    :initarg :estimated_duration
    :type cl:real
    :initform 0)
   (confidence
    :reader confidence
    :initarg :confidence
    :type cl:float
    :initform 0.0)
   (related_objects
    :reader related_objects
    :initarg :related_objects
    :type (cl:vector cl:string)
   :initform (cl:make-array 0 :element-type 'cl:string :initial-element "")))
)

(cl:defclass EmergencyAlert (<EmergencyAlert>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <EmergencyAlert>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'EmergencyAlert)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name semantic_navigation-msg:<EmergencyAlert> is deprecated: use semantic_navigation-msg:EmergencyAlert instead.")))

(cl:ensure-generic-function 'header-val :lambda-list '(m))
(cl:defmethod header-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:header-val is deprecated.  Use semantic_navigation-msg:header instead.")
  (header m))

(cl:ensure-generic-function 'alert_type-val :lambda-list '(m))
(cl:defmethod alert_type-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:alert_type-val is deprecated.  Use semantic_navigation-msg:alert_type instead.")
  (alert_type m))

(cl:ensure-generic-function 'severity-val :lambda-list '(m))
(cl:defmethod severity-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:severity-val is deprecated.  Use semantic_navigation-msg:severity instead.")
  (severity m))

(cl:ensure-generic-function 'location-val :lambda-list '(m))
(cl:defmethod location-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:location-val is deprecated.  Use semantic_navigation-msg:location instead.")
  (location m))

(cl:ensure-generic-function 'affected_radius-val :lambda-list '(m))
(cl:defmethod affected_radius-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:affected_radius-val is deprecated.  Use semantic_navigation-msg:affected_radius instead.")
  (affected_radius m))

(cl:ensure-generic-function 'description-val :lambda-list '(m))
(cl:defmethod description-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:description-val is deprecated.  Use semantic_navigation-msg:description instead.")
  (description m))

(cl:ensure-generic-function 'recommended_action-val :lambda-list '(m))
(cl:defmethod recommended_action-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:recommended_action-val is deprecated.  Use semantic_navigation-msg:recommended_action instead.")
  (recommended_action m))

(cl:ensure-generic-function 'detection_time-val :lambda-list '(m))
(cl:defmethod detection_time-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:detection_time-val is deprecated.  Use semantic_navigation-msg:detection_time instead.")
  (detection_time m))

(cl:ensure-generic-function 'estimated_duration-val :lambda-list '(m))
(cl:defmethod estimated_duration-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:estimated_duration-val is deprecated.  Use semantic_navigation-msg:estimated_duration instead.")
  (estimated_duration m))

(cl:ensure-generic-function 'confidence-val :lambda-list '(m))
(cl:defmethod confidence-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:confidence-val is deprecated.  Use semantic_navigation-msg:confidence instead.")
  (confidence m))

(cl:ensure-generic-function 'related_objects-val :lambda-list '(m))
(cl:defmethod related_objects-val ((m <EmergencyAlert>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader semantic_navigation-msg:related_objects-val is deprecated.  Use semantic_navigation-msg:related_objects instead.")
  (related_objects m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <EmergencyAlert>) ostream)
  "Serializes a message object of type '<EmergencyAlert>"
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'header) ostream)
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'alert_type))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'alert_type))
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'severity))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'severity))
  (roslisp-msg-protocol:serialize (cl:slot-value msg 'location) ostream)
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'affected_radius))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'description))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'description))
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'recommended_action))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'recommended_action))
  (cl:let ((__sec (cl:floor (cl:slot-value msg 'detection_time)))
        (__nsec (cl:round (cl:* 1e9 (cl:- (cl:slot-value msg 'detection_time) (cl:floor (cl:slot-value msg 'detection_time)))))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 0) __nsec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __nsec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __nsec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __nsec) ostream))
  (cl:let ((__sec (cl:floor (cl:slot-value msg 'estimated_duration)))
        (__nsec (cl:round (cl:* 1e9 (cl:- (cl:slot-value msg 'estimated_duration) (cl:floor (cl:slot-value msg 'estimated_duration)))))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __sec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 0) __nsec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __nsec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __nsec) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __nsec) ostream))
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'confidence))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'related_objects))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (cl:let ((__ros_str_len (cl:length ele)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) ele))
   (cl:slot-value msg 'related_objects))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <EmergencyAlert>) istream)
  "Deserializes a message object of type '<EmergencyAlert>"
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'header) istream)
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'alert_type) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'alert_type) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'severity) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'severity) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  (roslisp-msg-protocol:deserialize (cl:slot-value msg 'location) istream)
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'affected_radius) (roslisp-utils:decode-single-float-bits bits)))
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'description) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'description) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'recommended_action) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'recommended_action) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
    (cl:let ((__sec 0) (__nsec 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 0) __nsec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __nsec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __nsec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __nsec) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'detection_time) (cl:+ (cl:coerce __sec 'cl:double-float) (cl:/ __nsec 1e9))))
    (cl:let ((__sec 0) (__nsec 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __sec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 0) __nsec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __nsec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __nsec) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __nsec) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'estimated_duration) (cl:+ (cl:coerce __sec 'cl:double-float) (cl:/ __nsec 1e9))))
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'confidence) (roslisp-utils:decode-single-float-bits bits)))
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'related_objects) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'related_objects)))
    (cl:dotimes (i __ros_arr_len)
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:aref vals i) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:aref vals i) __ros_str_idx) (cl:code-char (cl:read-byte istream))))))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<EmergencyAlert>)))
  "Returns string type for a message object of type '<EmergencyAlert>"
  "semantic_navigation/EmergencyAlert")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'EmergencyAlert)))
  "Returns string type for a message object of type 'EmergencyAlert"
  "semantic_navigation/EmergencyAlert")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<EmergencyAlert>)))
  "Returns md5sum for a message object of type '<EmergencyAlert>"
  "a8796edaa18ec76393edd2510a2b4bdd")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'EmergencyAlert)))
  "Returns md5sum for a message object of type 'EmergencyAlert"
  "a8796edaa18ec76393edd2510a2b4bdd")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<EmergencyAlert>)))
  "Returns full string definition for message of type '<EmergencyAlert>"
  (cl:format cl:nil "# 应急警报消息~%Header header~%~%# 警报类型~%string alert_type           # FIRE, SMOKE, OBSTACLE, SYSTEM_FAILURE~%~%# 警报等级~%string severity             # LOW, MEDIUM, HIGH, CRITICAL~%~%# 位置信息~%geometry_msgs/Point location~%float32 affected_radius     # 影响半径 (米)~%~%# 描述信息~%string description          # 警报描述~%string recommended_action   # 建议行动~%~%# 时间信息~%time detection_time         # 检测时间~%duration estimated_duration # 预计持续时间~%~%# 相关数据~%float32 confidence          # 置信度~%string[] related_objects    # 相关物体~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'EmergencyAlert)))
  "Returns full string definition for message of type 'EmergencyAlert"
  (cl:format cl:nil "# 应急警报消息~%Header header~%~%# 警报类型~%string alert_type           # FIRE, SMOKE, OBSTACLE, SYSTEM_FAILURE~%~%# 警报等级~%string severity             # LOW, MEDIUM, HIGH, CRITICAL~%~%# 位置信息~%geometry_msgs/Point location~%float32 affected_radius     # 影响半径 (米)~%~%# 描述信息~%string description          # 警报描述~%string recommended_action   # 建议行动~%~%# 时间信息~%time detection_time         # 检测时间~%duration estimated_duration # 预计持续时间~%~%# 相关数据~%float32 confidence          # 置信度~%string[] related_objects    # 相关物体~%~%================================================================================~%MSG: std_msgs/Header~%# Standard metadata for higher-level stamped data types.~%# This is generally used to communicate timestamped data ~%# in a particular coordinate frame.~%# ~%# sequence ID: consecutively increasing ID ~%uint32 seq~%#Two-integer timestamp that is expressed as:~%# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')~%# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')~%# time-handling sugar is provided by the client library~%time stamp~%#Frame this data is associated with~%string frame_id~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <EmergencyAlert>))
  (cl:+ 0
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'header))
     4 (cl:length (cl:slot-value msg 'alert_type))
     4 (cl:length (cl:slot-value msg 'severity))
     (roslisp-msg-protocol:serialization-length (cl:slot-value msg 'location))
     4
     4 (cl:length (cl:slot-value msg 'description))
     4 (cl:length (cl:slot-value msg 'recommended_action))
     8
     8
     4
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'related_objects) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ 4 (cl:length ele))))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <EmergencyAlert>))
  "Converts a ROS message object to a list"
  (cl:list 'EmergencyAlert
    (cl:cons ':header (header msg))
    (cl:cons ':alert_type (alert_type msg))
    (cl:cons ':severity (severity msg))
    (cl:cons ':location (location msg))
    (cl:cons ':affected_radius (affected_radius msg))
    (cl:cons ':description (description msg))
    (cl:cons ':recommended_action (recommended_action msg))
    (cl:cons ':detection_time (detection_time msg))
    (cl:cons ':estimated_duration (estimated_duration msg))
    (cl:cons ':confidence (confidence msg))
    (cl:cons ':related_objects (related_objects msg))
))
