(cl:in-package semantic_navigation-msg)
(cl:export '(HEADER-VAL
          HEADER
          STATE-VAL
          STATE
          MODE-VAL
          MODE
          CURRENT_POSE-VAL
          CURRENT_POSE
          TARGET_POSE-VAL
          TARGET_POSE
          PROGRESS-VAL
          PROGRESS
          DISTANCE_REMAINING-VAL
          DISTANCE_REMAINING
          TIME_REMAINING-VAL
          TIME_REMAINING
          FIRE_DETECTED-VAL
          FIRE_DETECTED
          EMERGENCY_ACTIVE-VAL
          EMERGENCY_ACTIVE
          DETECTED_HAZARDS-VAL
          DETECTED_HAZARDS
          CURRENT_SPEED-VAL
          CURRENT_SPEED
          PATH_DEVIATION-VAL
          PATH_DEVIATION
))