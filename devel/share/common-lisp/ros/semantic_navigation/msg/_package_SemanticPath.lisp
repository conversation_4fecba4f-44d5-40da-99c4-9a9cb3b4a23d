(cl:in-package semantic_navigation-msg)
(cl:export '(HEADER-<PERSON><PERSON>
          HEADER
          PATH-<PERSON>L
          PATH
          SEMANTIC_LABELS-VAL
          SEMANTIC_LABELS
          SEMANTIC_COSTS-<PERSON><PERSON>
          SEMANTIC_COSTS
          SAFETY_SCORES-<PERSON><PERSON>
          SAFETY_SCORES
          TOTAL_DISTANCE-VA<PERSON>
          TOTAL_DISTANCE
          TOTAL_COST-VAL
          TOTAL_COST
          SAFETY_RATING-VAL
          SAFETY_RATING
          CONTAINS_FIRE_RISK-VAL
          CONTAINS_FIRE_RISK
          EMERGENCY_EXITS-VAL
          EMERGENCY_EXITS
          RISK_LEVEL-VAL
          RISK_LEVEL
))