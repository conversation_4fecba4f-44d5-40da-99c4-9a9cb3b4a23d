// Auto-generated. Do not edit!

// (in-package semantic_navigation.msg)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;
let nav_msgs = _finder('nav_msgs');
let geometry_msgs = _finder('geometry_msgs');
let std_msgs = _finder('std_msgs');

//-----------------------------------------------------------

class SemanticPath {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.header = null;
      this.path = null;
      this.semantic_labels = null;
      this.semantic_costs = null;
      this.safety_scores = null;
      this.total_distance = null;
      this.total_cost = null;
      this.safety_rating = null;
      this.contains_fire_risk = null;
      this.emergency_exits = null;
      this.risk_level = null;
    }
    else {
      if (initObj.hasOwnProperty('header')) {
        this.header = initObj.header
      }
      else {
        this.header = new std_msgs.msg.Header();
      }
      if (initObj.hasOwnProperty('path')) {
        this.path = initObj.path
      }
      else {
        this.path = new nav_msgs.msg.Path();
      }
      if (initObj.hasOwnProperty('semantic_labels')) {
        this.semantic_labels = initObj.semantic_labels
      }
      else {
        this.semantic_labels = [];
      }
      if (initObj.hasOwnProperty('semantic_costs')) {
        this.semantic_costs = initObj.semantic_costs
      }
      else {
        this.semantic_costs = [];
      }
      if (initObj.hasOwnProperty('safety_scores')) {
        this.safety_scores = initObj.safety_scores
      }
      else {
        this.safety_scores = [];
      }
      if (initObj.hasOwnProperty('total_distance')) {
        this.total_distance = initObj.total_distance
      }
      else {
        this.total_distance = 0.0;
      }
      if (initObj.hasOwnProperty('total_cost')) {
        this.total_cost = initObj.total_cost
      }
      else {
        this.total_cost = 0.0;
      }
      if (initObj.hasOwnProperty('safety_rating')) {
        this.safety_rating = initObj.safety_rating
      }
      else {
        this.safety_rating = 0.0;
      }
      if (initObj.hasOwnProperty('contains_fire_risk')) {
        this.contains_fire_risk = initObj.contains_fire_risk
      }
      else {
        this.contains_fire_risk = false;
      }
      if (initObj.hasOwnProperty('emergency_exits')) {
        this.emergency_exits = initObj.emergency_exits
      }
      else {
        this.emergency_exits = [];
      }
      if (initObj.hasOwnProperty('risk_level')) {
        this.risk_level = initObj.risk_level
      }
      else {
        this.risk_level = '';
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type SemanticPath
    // Serialize message field [header]
    bufferOffset = std_msgs.msg.Header.serialize(obj.header, buffer, bufferOffset);
    // Serialize message field [path]
    bufferOffset = nav_msgs.msg.Path.serialize(obj.path, buffer, bufferOffset);
    // Serialize message field [semantic_labels]
    bufferOffset = _arraySerializer.string(obj.semantic_labels, buffer, bufferOffset, null);
    // Serialize message field [semantic_costs]
    bufferOffset = _arraySerializer.float32(obj.semantic_costs, buffer, bufferOffset, null);
    // Serialize message field [safety_scores]
    bufferOffset = _arraySerializer.float32(obj.safety_scores, buffer, bufferOffset, null);
    // Serialize message field [total_distance]
    bufferOffset = _serializer.float32(obj.total_distance, buffer, bufferOffset);
    // Serialize message field [total_cost]
    bufferOffset = _serializer.float32(obj.total_cost, buffer, bufferOffset);
    // Serialize message field [safety_rating]
    bufferOffset = _serializer.float32(obj.safety_rating, buffer, bufferOffset);
    // Serialize message field [contains_fire_risk]
    bufferOffset = _serializer.bool(obj.contains_fire_risk, buffer, bufferOffset);
    // Serialize message field [emergency_exits]
    // Serialize the length for message field [emergency_exits]
    bufferOffset = _serializer.uint32(obj.emergency_exits.length, buffer, bufferOffset);
    obj.emergency_exits.forEach((val) => {
      bufferOffset = geometry_msgs.msg.Point.serialize(val, buffer, bufferOffset);
    });
    // Serialize message field [risk_level]
    bufferOffset = _serializer.string(obj.risk_level, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type SemanticPath
    let len;
    let data = new SemanticPath(null);
    // Deserialize message field [header]
    data.header = std_msgs.msg.Header.deserialize(buffer, bufferOffset);
    // Deserialize message field [path]
    data.path = nav_msgs.msg.Path.deserialize(buffer, bufferOffset);
    // Deserialize message field [semantic_labels]
    data.semantic_labels = _arrayDeserializer.string(buffer, bufferOffset, null)
    // Deserialize message field [semantic_costs]
    data.semantic_costs = _arrayDeserializer.float32(buffer, bufferOffset, null)
    // Deserialize message field [safety_scores]
    data.safety_scores = _arrayDeserializer.float32(buffer, bufferOffset, null)
    // Deserialize message field [total_distance]
    data.total_distance = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [total_cost]
    data.total_cost = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [safety_rating]
    data.safety_rating = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [contains_fire_risk]
    data.contains_fire_risk = _deserializer.bool(buffer, bufferOffset);
    // Deserialize message field [emergency_exits]
    // Deserialize array length for message field [emergency_exits]
    len = _deserializer.uint32(buffer, bufferOffset);
    data.emergency_exits = new Array(len);
    for (let i = 0; i < len; ++i) {
      data.emergency_exits[i] = geometry_msgs.msg.Point.deserialize(buffer, bufferOffset)
    }
    // Deserialize message field [risk_level]
    data.risk_level = _deserializer.string(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += std_msgs.msg.Header.getMessageSize(object.header);
    length += nav_msgs.msg.Path.getMessageSize(object.path);
    object.semantic_labels.forEach((val) => {
      length += 4 + _getByteLength(val);
    });
    length += 4 * object.semantic_costs.length;
    length += 4 * object.safety_scores.length;
    length += 24 * object.emergency_exits.length;
    length += _getByteLength(object.risk_level);
    return length + 33;
  }

  static datatype() {
    // Returns string type for a message object
    return 'semantic_navigation/SemanticPath';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return '0320dcd1e20996da4804a029f13c56f2';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    # 语义路径消息
    Header header
    
    # 路径信息
    nav_msgs/Path path
    
    # 语义信息
    string[] semantic_labels     # 路径上的语义标签
    float32[] semantic_costs     # 对应的语义代价
    float32[] safety_scores      # 安全性评分
    
    # 路径属性
    float32 total_distance       # 总距离
    float32 total_cost          # 总代价
    float32 safety_rating       # 安全评级 (0-1)
    bool contains_fire_risk     # 是否包含火灾风险
    
    # 应急信息
    geometry_msgs/Point[] emergency_exits  # 应急出口位置
    string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL
    
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: nav_msgs/Path
    #An array of poses that represents a Path for a robot to follow
    Header header
    geometry_msgs/PoseStamped[] poses
    
    ================================================================================
    MSG: geometry_msgs/PoseStamped
    # A Pose with reference coordinate frame and timestamp
    Header header
    Pose pose
    
    ================================================================================
    MSG: geometry_msgs/Pose
    # A representation of pose in free space, composed of position and orientation. 
    Point position
    Quaternion orientation
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    ================================================================================
    MSG: geometry_msgs/Quaternion
    # This represents an orientation in free space in quaternion form.
    
    float64 x
    float64 y
    float64 z
    float64 w
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new SemanticPath(null);
    if (msg.header !== undefined) {
      resolved.header = std_msgs.msg.Header.Resolve(msg.header)
    }
    else {
      resolved.header = new std_msgs.msg.Header()
    }

    if (msg.path !== undefined) {
      resolved.path = nav_msgs.msg.Path.Resolve(msg.path)
    }
    else {
      resolved.path = new nav_msgs.msg.Path()
    }

    if (msg.semantic_labels !== undefined) {
      resolved.semantic_labels = msg.semantic_labels;
    }
    else {
      resolved.semantic_labels = []
    }

    if (msg.semantic_costs !== undefined) {
      resolved.semantic_costs = msg.semantic_costs;
    }
    else {
      resolved.semantic_costs = []
    }

    if (msg.safety_scores !== undefined) {
      resolved.safety_scores = msg.safety_scores;
    }
    else {
      resolved.safety_scores = []
    }

    if (msg.total_distance !== undefined) {
      resolved.total_distance = msg.total_distance;
    }
    else {
      resolved.total_distance = 0.0
    }

    if (msg.total_cost !== undefined) {
      resolved.total_cost = msg.total_cost;
    }
    else {
      resolved.total_cost = 0.0
    }

    if (msg.safety_rating !== undefined) {
      resolved.safety_rating = msg.safety_rating;
    }
    else {
      resolved.safety_rating = 0.0
    }

    if (msg.contains_fire_risk !== undefined) {
      resolved.contains_fire_risk = msg.contains_fire_risk;
    }
    else {
      resolved.contains_fire_risk = false
    }

    if (msg.emergency_exits !== undefined) {
      resolved.emergency_exits = new Array(msg.emergency_exits.length);
      for (let i = 0; i < resolved.emergency_exits.length; ++i) {
        resolved.emergency_exits[i] = geometry_msgs.msg.Point.Resolve(msg.emergency_exits[i]);
      }
    }
    else {
      resolved.emergency_exits = []
    }

    if (msg.risk_level !== undefined) {
      resolved.risk_level = msg.risk_level;
    }
    else {
      resolved.risk_level = ''
    }

    return resolved;
    }
};

module.exports = SemanticPath;
