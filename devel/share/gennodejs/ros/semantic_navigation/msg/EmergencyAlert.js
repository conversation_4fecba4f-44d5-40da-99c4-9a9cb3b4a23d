// Auto-generated. Do not edit!

// (in-package semantic_navigation.msg)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;
let geometry_msgs = _finder('geometry_msgs');
let std_msgs = _finder('std_msgs');

//-----------------------------------------------------------

class EmergencyAlert {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.header = null;
      this.alert_type = null;
      this.severity = null;
      this.location = null;
      this.affected_radius = null;
      this.description = null;
      this.recommended_action = null;
      this.detection_time = null;
      this.estimated_duration = null;
      this.confidence = null;
      this.related_objects = null;
    }
    else {
      if (initObj.hasOwnProperty('header')) {
        this.header = initObj.header
      }
      else {
        this.header = new std_msgs.msg.Header();
      }
      if (initObj.hasOwnProperty('alert_type')) {
        this.alert_type = initObj.alert_type
      }
      else {
        this.alert_type = '';
      }
      if (initObj.hasOwnProperty('severity')) {
        this.severity = initObj.severity
      }
      else {
        this.severity = '';
      }
      if (initObj.hasOwnProperty('location')) {
        this.location = initObj.location
      }
      else {
        this.location = new geometry_msgs.msg.Point();
      }
      if (initObj.hasOwnProperty('affected_radius')) {
        this.affected_radius = initObj.affected_radius
      }
      else {
        this.affected_radius = 0.0;
      }
      if (initObj.hasOwnProperty('description')) {
        this.description = initObj.description
      }
      else {
        this.description = '';
      }
      if (initObj.hasOwnProperty('recommended_action')) {
        this.recommended_action = initObj.recommended_action
      }
      else {
        this.recommended_action = '';
      }
      if (initObj.hasOwnProperty('detection_time')) {
        this.detection_time = initObj.detection_time
      }
      else {
        this.detection_time = {secs: 0, nsecs: 0};
      }
      if (initObj.hasOwnProperty('estimated_duration')) {
        this.estimated_duration = initObj.estimated_duration
      }
      else {
        this.estimated_duration = {secs: 0, nsecs: 0};
      }
      if (initObj.hasOwnProperty('confidence')) {
        this.confidence = initObj.confidence
      }
      else {
        this.confidence = 0.0;
      }
      if (initObj.hasOwnProperty('related_objects')) {
        this.related_objects = initObj.related_objects
      }
      else {
        this.related_objects = [];
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type EmergencyAlert
    // Serialize message field [header]
    bufferOffset = std_msgs.msg.Header.serialize(obj.header, buffer, bufferOffset);
    // Serialize message field [alert_type]
    bufferOffset = _serializer.string(obj.alert_type, buffer, bufferOffset);
    // Serialize message field [severity]
    bufferOffset = _serializer.string(obj.severity, buffer, bufferOffset);
    // Serialize message field [location]
    bufferOffset = geometry_msgs.msg.Point.serialize(obj.location, buffer, bufferOffset);
    // Serialize message field [affected_radius]
    bufferOffset = _serializer.float32(obj.affected_radius, buffer, bufferOffset);
    // Serialize message field [description]
    bufferOffset = _serializer.string(obj.description, buffer, bufferOffset);
    // Serialize message field [recommended_action]
    bufferOffset = _serializer.string(obj.recommended_action, buffer, bufferOffset);
    // Serialize message field [detection_time]
    bufferOffset = _serializer.time(obj.detection_time, buffer, bufferOffset);
    // Serialize message field [estimated_duration]
    bufferOffset = _serializer.duration(obj.estimated_duration, buffer, bufferOffset);
    // Serialize message field [confidence]
    bufferOffset = _serializer.float32(obj.confidence, buffer, bufferOffset);
    // Serialize message field [related_objects]
    bufferOffset = _arraySerializer.string(obj.related_objects, buffer, bufferOffset, null);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type EmergencyAlert
    let len;
    let data = new EmergencyAlert(null);
    // Deserialize message field [header]
    data.header = std_msgs.msg.Header.deserialize(buffer, bufferOffset);
    // Deserialize message field [alert_type]
    data.alert_type = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [severity]
    data.severity = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [location]
    data.location = geometry_msgs.msg.Point.deserialize(buffer, bufferOffset);
    // Deserialize message field [affected_radius]
    data.affected_radius = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [description]
    data.description = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [recommended_action]
    data.recommended_action = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [detection_time]
    data.detection_time = _deserializer.time(buffer, bufferOffset);
    // Deserialize message field [estimated_duration]
    data.estimated_duration = _deserializer.duration(buffer, bufferOffset);
    // Deserialize message field [confidence]
    data.confidence = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [related_objects]
    data.related_objects = _arrayDeserializer.string(buffer, bufferOffset, null)
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += std_msgs.msg.Header.getMessageSize(object.header);
    length += _getByteLength(object.alert_type);
    length += _getByteLength(object.severity);
    length += _getByteLength(object.description);
    length += _getByteLength(object.recommended_action);
    object.related_objects.forEach((val) => {
      length += 4 + _getByteLength(val);
    });
    return length + 68;
  }

  static datatype() {
    // Returns string type for a message object
    return 'semantic_navigation/EmergencyAlert';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return 'a8796edaa18ec76393edd2510a2b4bdd';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    # 应急警报消息
    Header header
    
    # 警报类型
    string alert_type           # FIRE, SMOKE, OBSTACLE, SYSTEM_FAILURE
    
    # 警报等级
    string severity             # LOW, MEDIUM, HIGH, CRITICAL
    
    # 位置信息
    geometry_msgs/Point location
    float32 affected_radius     # 影响半径 (米)
    
    # 描述信息
    string description          # 警报描述
    string recommended_action   # 建议行动
    
    # 时间信息
    time detection_time         # 检测时间
    duration estimated_duration # 预计持续时间
    
    # 相关数据
    float32 confidence          # 置信度
    string[] related_objects    # 相关物体
    
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new EmergencyAlert(null);
    if (msg.header !== undefined) {
      resolved.header = std_msgs.msg.Header.Resolve(msg.header)
    }
    else {
      resolved.header = new std_msgs.msg.Header()
    }

    if (msg.alert_type !== undefined) {
      resolved.alert_type = msg.alert_type;
    }
    else {
      resolved.alert_type = ''
    }

    if (msg.severity !== undefined) {
      resolved.severity = msg.severity;
    }
    else {
      resolved.severity = ''
    }

    if (msg.location !== undefined) {
      resolved.location = geometry_msgs.msg.Point.Resolve(msg.location)
    }
    else {
      resolved.location = new geometry_msgs.msg.Point()
    }

    if (msg.affected_radius !== undefined) {
      resolved.affected_radius = msg.affected_radius;
    }
    else {
      resolved.affected_radius = 0.0
    }

    if (msg.description !== undefined) {
      resolved.description = msg.description;
    }
    else {
      resolved.description = ''
    }

    if (msg.recommended_action !== undefined) {
      resolved.recommended_action = msg.recommended_action;
    }
    else {
      resolved.recommended_action = ''
    }

    if (msg.detection_time !== undefined) {
      resolved.detection_time = msg.detection_time;
    }
    else {
      resolved.detection_time = {secs: 0, nsecs: 0}
    }

    if (msg.estimated_duration !== undefined) {
      resolved.estimated_duration = msg.estimated_duration;
    }
    else {
      resolved.estimated_duration = {secs: 0, nsecs: 0}
    }

    if (msg.confidence !== undefined) {
      resolved.confidence = msg.confidence;
    }
    else {
      resolved.confidence = 0.0
    }

    if (msg.related_objects !== undefined) {
      resolved.related_objects = msg.related_objects;
    }
    else {
      resolved.related_objects = []
    }

    return resolved;
    }
};

module.exports = EmergencyAlert;
