// Auto-generated. Do not edit!

// (in-package semantic_navigation.msg)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;
let std_msgs = _finder('std_msgs');
let geometry_msgs = _finder('geometry_msgs');

//-----------------------------------------------------------

class NavigationStatus {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.header = null;
      this.state = null;
      this.mode = null;
      this.current_pose = null;
      this.target_pose = null;
      this.progress = null;
      this.distance_remaining = null;
      this.time_remaining = null;
      this.fire_detected = null;
      this.emergency_active = null;
      this.detected_hazards = null;
      this.current_speed = null;
      this.path_deviation = null;
    }
    else {
      if (initObj.hasOwnProperty('header')) {
        this.header = initObj.header
      }
      else {
        this.header = new std_msgs.msg.Header();
      }
      if (initObj.hasOwnProperty('state')) {
        this.state = initObj.state
      }
      else {
        this.state = '';
      }
      if (initObj.hasOwnProperty('mode')) {
        this.mode = initObj.mode
      }
      else {
        this.mode = '';
      }
      if (initObj.hasOwnProperty('current_pose')) {
        this.current_pose = initObj.current_pose
      }
      else {
        this.current_pose = new geometry_msgs.msg.PoseStamped();
      }
      if (initObj.hasOwnProperty('target_pose')) {
        this.target_pose = initObj.target_pose
      }
      else {
        this.target_pose = new geometry_msgs.msg.PoseStamped();
      }
      if (initObj.hasOwnProperty('progress')) {
        this.progress = initObj.progress
      }
      else {
        this.progress = 0.0;
      }
      if (initObj.hasOwnProperty('distance_remaining')) {
        this.distance_remaining = initObj.distance_remaining
      }
      else {
        this.distance_remaining = 0.0;
      }
      if (initObj.hasOwnProperty('time_remaining')) {
        this.time_remaining = initObj.time_remaining
      }
      else {
        this.time_remaining = {secs: 0, nsecs: 0};
      }
      if (initObj.hasOwnProperty('fire_detected')) {
        this.fire_detected = initObj.fire_detected
      }
      else {
        this.fire_detected = false;
      }
      if (initObj.hasOwnProperty('emergency_active')) {
        this.emergency_active = initObj.emergency_active
      }
      else {
        this.emergency_active = false;
      }
      if (initObj.hasOwnProperty('detected_hazards')) {
        this.detected_hazards = initObj.detected_hazards
      }
      else {
        this.detected_hazards = [];
      }
      if (initObj.hasOwnProperty('current_speed')) {
        this.current_speed = initObj.current_speed
      }
      else {
        this.current_speed = 0.0;
      }
      if (initObj.hasOwnProperty('path_deviation')) {
        this.path_deviation = initObj.path_deviation
      }
      else {
        this.path_deviation = 0.0;
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type NavigationStatus
    // Serialize message field [header]
    bufferOffset = std_msgs.msg.Header.serialize(obj.header, buffer, bufferOffset);
    // Serialize message field [state]
    bufferOffset = _serializer.string(obj.state, buffer, bufferOffset);
    // Serialize message field [mode]
    bufferOffset = _serializer.string(obj.mode, buffer, bufferOffset);
    // Serialize message field [current_pose]
    bufferOffset = geometry_msgs.msg.PoseStamped.serialize(obj.current_pose, buffer, bufferOffset);
    // Serialize message field [target_pose]
    bufferOffset = geometry_msgs.msg.PoseStamped.serialize(obj.target_pose, buffer, bufferOffset);
    // Serialize message field [progress]
    bufferOffset = _serializer.float32(obj.progress, buffer, bufferOffset);
    // Serialize message field [distance_remaining]
    bufferOffset = _serializer.float32(obj.distance_remaining, buffer, bufferOffset);
    // Serialize message field [time_remaining]
    bufferOffset = _serializer.duration(obj.time_remaining, buffer, bufferOffset);
    // Serialize message field [fire_detected]
    bufferOffset = _serializer.bool(obj.fire_detected, buffer, bufferOffset);
    // Serialize message field [emergency_active]
    bufferOffset = _serializer.bool(obj.emergency_active, buffer, bufferOffset);
    // Serialize message field [detected_hazards]
    bufferOffset = _arraySerializer.string(obj.detected_hazards, buffer, bufferOffset, null);
    // Serialize message field [current_speed]
    bufferOffset = _serializer.float32(obj.current_speed, buffer, bufferOffset);
    // Serialize message field [path_deviation]
    bufferOffset = _serializer.float32(obj.path_deviation, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type NavigationStatus
    let len;
    let data = new NavigationStatus(null);
    // Deserialize message field [header]
    data.header = std_msgs.msg.Header.deserialize(buffer, bufferOffset);
    // Deserialize message field [state]
    data.state = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [mode]
    data.mode = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [current_pose]
    data.current_pose = geometry_msgs.msg.PoseStamped.deserialize(buffer, bufferOffset);
    // Deserialize message field [target_pose]
    data.target_pose = geometry_msgs.msg.PoseStamped.deserialize(buffer, bufferOffset);
    // Deserialize message field [progress]
    data.progress = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [distance_remaining]
    data.distance_remaining = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [time_remaining]
    data.time_remaining = _deserializer.duration(buffer, bufferOffset);
    // Deserialize message field [fire_detected]
    data.fire_detected = _deserializer.bool(buffer, bufferOffset);
    // Deserialize message field [emergency_active]
    data.emergency_active = _deserializer.bool(buffer, bufferOffset);
    // Deserialize message field [detected_hazards]
    data.detected_hazards = _arrayDeserializer.string(buffer, bufferOffset, null)
    // Deserialize message field [current_speed]
    data.current_speed = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [path_deviation]
    data.path_deviation = _deserializer.float32(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += std_msgs.msg.Header.getMessageSize(object.header);
    length += _getByteLength(object.state);
    length += _getByteLength(object.mode);
    length += geometry_msgs.msg.PoseStamped.getMessageSize(object.current_pose);
    length += geometry_msgs.msg.PoseStamped.getMessageSize(object.target_pose);
    object.detected_hazards.forEach((val) => {
      length += 4 + _getByteLength(val);
    });
    return length + 38;
  }

  static datatype() {
    // Returns string type for a message object
    return 'semantic_navigation/NavigationStatus';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return 'cdbdd9a0f2482e0f81f898630e3a3988';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    # 导航状态消息
    Header header
    
    # 导航状态
    string state                # IDLE, PLANNING, NAVIGATING, EMERGENCY, STOPPED, FAILED
    string mode                 # NORMAL, EMERGENCY, FIRE_ESCAPE
    
    # 当前位置和目标
    geometry_msgs/PoseStamped current_pose
    geometry_msgs/PoseStamped target_pose
    
    # 进度信息
    float32 progress            # 完成进度 (0-1)
    float32 distance_remaining  # 剩余距离
    duration time_remaining     # 预计剩余时间
    
    # 安全状态
    bool fire_detected          # 是否检测到火焰
    bool emergency_active       # 是否处于应急状态
    string[] detected_hazards   # 检测到的危险
    
    # 性能指标
    float32 current_speed       # 当前速度
    float32 path_deviation      # 路径偏差
    
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: geometry_msgs/PoseStamped
    # A Pose with reference coordinate frame and timestamp
    Header header
    Pose pose
    
    ================================================================================
    MSG: geometry_msgs/Pose
    # A representation of pose in free space, composed of position and orientation. 
    Point position
    Quaternion orientation
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    ================================================================================
    MSG: geometry_msgs/Quaternion
    # This represents an orientation in free space in quaternion form.
    
    float64 x
    float64 y
    float64 z
    float64 w
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new NavigationStatus(null);
    if (msg.header !== undefined) {
      resolved.header = std_msgs.msg.Header.Resolve(msg.header)
    }
    else {
      resolved.header = new std_msgs.msg.Header()
    }

    if (msg.state !== undefined) {
      resolved.state = msg.state;
    }
    else {
      resolved.state = ''
    }

    if (msg.mode !== undefined) {
      resolved.mode = msg.mode;
    }
    else {
      resolved.mode = ''
    }

    if (msg.current_pose !== undefined) {
      resolved.current_pose = geometry_msgs.msg.PoseStamped.Resolve(msg.current_pose)
    }
    else {
      resolved.current_pose = new geometry_msgs.msg.PoseStamped()
    }

    if (msg.target_pose !== undefined) {
      resolved.target_pose = geometry_msgs.msg.PoseStamped.Resolve(msg.target_pose)
    }
    else {
      resolved.target_pose = new geometry_msgs.msg.PoseStamped()
    }

    if (msg.progress !== undefined) {
      resolved.progress = msg.progress;
    }
    else {
      resolved.progress = 0.0
    }

    if (msg.distance_remaining !== undefined) {
      resolved.distance_remaining = msg.distance_remaining;
    }
    else {
      resolved.distance_remaining = 0.0
    }

    if (msg.time_remaining !== undefined) {
      resolved.time_remaining = msg.time_remaining;
    }
    else {
      resolved.time_remaining = {secs: 0, nsecs: 0}
    }

    if (msg.fire_detected !== undefined) {
      resolved.fire_detected = msg.fire_detected;
    }
    else {
      resolved.fire_detected = false
    }

    if (msg.emergency_active !== undefined) {
      resolved.emergency_active = msg.emergency_active;
    }
    else {
      resolved.emergency_active = false
    }

    if (msg.detected_hazards !== undefined) {
      resolved.detected_hazards = msg.detected_hazards;
    }
    else {
      resolved.detected_hazards = []
    }

    if (msg.current_speed !== undefined) {
      resolved.current_speed = msg.current_speed;
    }
    else {
      resolved.current_speed = 0.0
    }

    if (msg.path_deviation !== undefined) {
      resolved.path_deviation = msg.path_deviation;
    }
    else {
      resolved.path_deviation = 0.0
    }

    return resolved;
    }
};

module.exports = NavigationStatus;
