// Auto-generated. Do not edit!

// (in-package semantic_navigation.msg)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;
let std_msgs = _finder('std_msgs');
let geometry_msgs = _finder('geometry_msgs');

//-----------------------------------------------------------

class SemanticNavigationGoal {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.header = null;
      this.target_pose = null;
      this.navigation_mode = null;
      this.avoid_classes = null;
      this.prefer_classes = null;
      this.safety_distance = null;
      this.fire_avoidance_distance = null;
      this.priority = null;
      this.emergency_mode = null;
      this.timeout = null;
    }
    else {
      if (initObj.hasOwnProperty('header')) {
        this.header = initObj.header
      }
      else {
        this.header = new std_msgs.msg.Header();
      }
      if (initObj.hasOwnProperty('target_pose')) {
        this.target_pose = initObj.target_pose
      }
      else {
        this.target_pose = new geometry_msgs.msg.PoseStamped();
      }
      if (initObj.hasOwnProperty('navigation_mode')) {
        this.navigation_mode = initObj.navigation_mode
      }
      else {
        this.navigation_mode = '';
      }
      if (initObj.hasOwnProperty('avoid_classes')) {
        this.avoid_classes = initObj.avoid_classes
      }
      else {
        this.avoid_classes = [];
      }
      if (initObj.hasOwnProperty('prefer_classes')) {
        this.prefer_classes = initObj.prefer_classes
      }
      else {
        this.prefer_classes = [];
      }
      if (initObj.hasOwnProperty('safety_distance')) {
        this.safety_distance = initObj.safety_distance
      }
      else {
        this.safety_distance = 0.0;
      }
      if (initObj.hasOwnProperty('fire_avoidance_distance')) {
        this.fire_avoidance_distance = initObj.fire_avoidance_distance
      }
      else {
        this.fire_avoidance_distance = 0.0;
      }
      if (initObj.hasOwnProperty('priority')) {
        this.priority = initObj.priority
      }
      else {
        this.priority = 0;
      }
      if (initObj.hasOwnProperty('emergency_mode')) {
        this.emergency_mode = initObj.emergency_mode
      }
      else {
        this.emergency_mode = false;
      }
      if (initObj.hasOwnProperty('timeout')) {
        this.timeout = initObj.timeout
      }
      else {
        this.timeout = {secs: 0, nsecs: 0};
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type SemanticNavigationGoal
    // Serialize message field [header]
    bufferOffset = std_msgs.msg.Header.serialize(obj.header, buffer, bufferOffset);
    // Serialize message field [target_pose]
    bufferOffset = geometry_msgs.msg.PoseStamped.serialize(obj.target_pose, buffer, bufferOffset);
    // Serialize message field [navigation_mode]
    bufferOffset = _serializer.string(obj.navigation_mode, buffer, bufferOffset);
    // Serialize message field [avoid_classes]
    bufferOffset = _arraySerializer.string(obj.avoid_classes, buffer, bufferOffset, null);
    // Serialize message field [prefer_classes]
    bufferOffset = _arraySerializer.string(obj.prefer_classes, buffer, bufferOffset, null);
    // Serialize message field [safety_distance]
    bufferOffset = _serializer.float32(obj.safety_distance, buffer, bufferOffset);
    // Serialize message field [fire_avoidance_distance]
    bufferOffset = _serializer.float32(obj.fire_avoidance_distance, buffer, bufferOffset);
    // Serialize message field [priority]
    bufferOffset = _serializer.uint8(obj.priority, buffer, bufferOffset);
    // Serialize message field [emergency_mode]
    bufferOffset = _serializer.bool(obj.emergency_mode, buffer, bufferOffset);
    // Serialize message field [timeout]
    bufferOffset = _serializer.duration(obj.timeout, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type SemanticNavigationGoal
    let len;
    let data = new SemanticNavigationGoal(null);
    // Deserialize message field [header]
    data.header = std_msgs.msg.Header.deserialize(buffer, bufferOffset);
    // Deserialize message field [target_pose]
    data.target_pose = geometry_msgs.msg.PoseStamped.deserialize(buffer, bufferOffset);
    // Deserialize message field [navigation_mode]
    data.navigation_mode = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [avoid_classes]
    data.avoid_classes = _arrayDeserializer.string(buffer, bufferOffset, null)
    // Deserialize message field [prefer_classes]
    data.prefer_classes = _arrayDeserializer.string(buffer, bufferOffset, null)
    // Deserialize message field [safety_distance]
    data.safety_distance = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [fire_avoidance_distance]
    data.fire_avoidance_distance = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [priority]
    data.priority = _deserializer.uint8(buffer, bufferOffset);
    // Deserialize message field [emergency_mode]
    data.emergency_mode = _deserializer.bool(buffer, bufferOffset);
    // Deserialize message field [timeout]
    data.timeout = _deserializer.duration(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += std_msgs.msg.Header.getMessageSize(object.header);
    length += geometry_msgs.msg.PoseStamped.getMessageSize(object.target_pose);
    length += _getByteLength(object.navigation_mode);
    object.avoid_classes.forEach((val) => {
      length += 4 + _getByteLength(val);
    });
    object.prefer_classes.forEach((val) => {
      length += 4 + _getByteLength(val);
    });
    return length + 30;
  }

  static datatype() {
    // Returns string type for a message object
    return 'semantic_navigation/SemanticNavigationGoal';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return 'f3344e67e6ced968d5c3cc9238c45392';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    # 语义导航目标消息
    Header header
    
    # 目标位置
    geometry_msgs/PoseStamped target_pose
    
    # 导航模式
    string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE
    
    # 语义约束
    string[] avoid_classes    # 需要避开的语义类别
    string[] prefer_classes   # 优先通过的语义类别
    
    # 安全参数
    float32 safety_distance   # 安全距离 (米)
    float32 fire_avoidance_distance  # 火焰避让距离 (米)
    
    # 优先级设置
    uint8 priority           # 导航优先级 (0-10, 10最高)
    bool emergency_mode      # 是否为应急模式
    
    # 超时设置
    duration timeout         # 导航超时时间
    
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: geometry_msgs/PoseStamped
    # A Pose with reference coordinate frame and timestamp
    Header header
    Pose pose
    
    ================================================================================
    MSG: geometry_msgs/Pose
    # A representation of pose in free space, composed of position and orientation. 
    Point position
    Quaternion orientation
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    ================================================================================
    MSG: geometry_msgs/Quaternion
    # This represents an orientation in free space in quaternion form.
    
    float64 x
    float64 y
    float64 z
    float64 w
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new SemanticNavigationGoal(null);
    if (msg.header !== undefined) {
      resolved.header = std_msgs.msg.Header.Resolve(msg.header)
    }
    else {
      resolved.header = new std_msgs.msg.Header()
    }

    if (msg.target_pose !== undefined) {
      resolved.target_pose = geometry_msgs.msg.PoseStamped.Resolve(msg.target_pose)
    }
    else {
      resolved.target_pose = new geometry_msgs.msg.PoseStamped()
    }

    if (msg.navigation_mode !== undefined) {
      resolved.navigation_mode = msg.navigation_mode;
    }
    else {
      resolved.navigation_mode = ''
    }

    if (msg.avoid_classes !== undefined) {
      resolved.avoid_classes = msg.avoid_classes;
    }
    else {
      resolved.avoid_classes = []
    }

    if (msg.prefer_classes !== undefined) {
      resolved.prefer_classes = msg.prefer_classes;
    }
    else {
      resolved.prefer_classes = []
    }

    if (msg.safety_distance !== undefined) {
      resolved.safety_distance = msg.safety_distance;
    }
    else {
      resolved.safety_distance = 0.0
    }

    if (msg.fire_avoidance_distance !== undefined) {
      resolved.fire_avoidance_distance = msg.fire_avoidance_distance;
    }
    else {
      resolved.fire_avoidance_distance = 0.0
    }

    if (msg.priority !== undefined) {
      resolved.priority = msg.priority;
    }
    else {
      resolved.priority = 0
    }

    if (msg.emergency_mode !== undefined) {
      resolved.emergency_mode = msg.emergency_mode;
    }
    else {
      resolved.emergency_mode = false
    }

    if (msg.timeout !== undefined) {
      resolved.timeout = msg.timeout;
    }
    else {
      resolved.timeout = {secs: 0, nsecs: 0}
    }

    return resolved;
    }
};

module.exports = SemanticNavigationGoal;
