// Auto-generated. Do not edit!

// (in-package semantic_navigation.srv)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;
let SemanticNavigationGoal = require('../msg/SemanticNavigationGoal.js');

//-----------------------------------------------------------

let SemanticPath = require('../msg/SemanticPath.js');

//-----------------------------------------------------------

class SetNavigationGoalRequest {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.goal = null;
    }
    else {
      if (initObj.hasOwnProperty('goal')) {
        this.goal = initObj.goal
      }
      else {
        this.goal = new SemanticNavigationGoal();
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type SetNavigationGoalRequest
    // Serialize message field [goal]
    bufferOffset = SemanticNavigationGoal.serialize(obj.goal, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type SetNavigationGoalRequest
    let len;
    let data = new SetNavigationGoalRequest(null);
    // Deserialize message field [goal]
    data.goal = SemanticNavigationGoal.deserialize(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += SemanticNavigationGoal.getMessageSize(object.goal);
    return length;
  }

  static datatype() {
    // Returns string type for a service object
    return 'semantic_navigation/SetNavigationGoalRequest';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return '3e127fafc98b335a09570dc426dce88b';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    # 设置导航目标服务
    # 请求
    SemanticNavigationGoal goal
    
    
    ================================================================================
    MSG: semantic_navigation/SemanticNavigationGoal
    # 语义导航目标消息
    Header header
    
    # 目标位置
    geometry_msgs/PoseStamped target_pose
    
    # 导航模式
    string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE
    
    # 语义约束
    string[] avoid_classes    # 需要避开的语义类别
    string[] prefer_classes   # 优先通过的语义类别
    
    # 安全参数
    float32 safety_distance   # 安全距离 (米)
    float32 fire_avoidance_distance  # 火焰避让距离 (米)
    
    # 优先级设置
    uint8 priority           # 导航优先级 (0-10, 10最高)
    bool emergency_mode      # 是否为应急模式
    
    # 超时设置
    duration timeout         # 导航超时时间
    
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: geometry_msgs/PoseStamped
    # A Pose with reference coordinate frame and timestamp
    Header header
    Pose pose
    
    ================================================================================
    MSG: geometry_msgs/Pose
    # A representation of pose in free space, composed of position and orientation. 
    Point position
    Quaternion orientation
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    ================================================================================
    MSG: geometry_msgs/Quaternion
    # This represents an orientation in free space in quaternion form.
    
    float64 x
    float64 y
    float64 z
    float64 w
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new SetNavigationGoalRequest(null);
    if (msg.goal !== undefined) {
      resolved.goal = SemanticNavigationGoal.Resolve(msg.goal)
    }
    else {
      resolved.goal = new SemanticNavigationGoal()
    }

    return resolved;
    }
};

class SetNavigationGoalResponse {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.success = null;
      this.message = null;
      this.goal_id = null;
      this.estimated_time = null;
      this.planned_path = null;
    }
    else {
      if (initObj.hasOwnProperty('success')) {
        this.success = initObj.success
      }
      else {
        this.success = false;
      }
      if (initObj.hasOwnProperty('message')) {
        this.message = initObj.message
      }
      else {
        this.message = '';
      }
      if (initObj.hasOwnProperty('goal_id')) {
        this.goal_id = initObj.goal_id
      }
      else {
        this.goal_id = '';
      }
      if (initObj.hasOwnProperty('estimated_time')) {
        this.estimated_time = initObj.estimated_time
      }
      else {
        this.estimated_time = 0.0;
      }
      if (initObj.hasOwnProperty('planned_path')) {
        this.planned_path = initObj.planned_path
      }
      else {
        this.planned_path = new SemanticPath();
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type SetNavigationGoalResponse
    // Serialize message field [success]
    bufferOffset = _serializer.bool(obj.success, buffer, bufferOffset);
    // Serialize message field [message]
    bufferOffset = _serializer.string(obj.message, buffer, bufferOffset);
    // Serialize message field [goal_id]
    bufferOffset = _serializer.string(obj.goal_id, buffer, bufferOffset);
    // Serialize message field [estimated_time]
    bufferOffset = _serializer.float32(obj.estimated_time, buffer, bufferOffset);
    // Serialize message field [planned_path]
    bufferOffset = SemanticPath.serialize(obj.planned_path, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type SetNavigationGoalResponse
    let len;
    let data = new SetNavigationGoalResponse(null);
    // Deserialize message field [success]
    data.success = _deserializer.bool(buffer, bufferOffset);
    // Deserialize message field [message]
    data.message = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [goal_id]
    data.goal_id = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [estimated_time]
    data.estimated_time = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [planned_path]
    data.planned_path = SemanticPath.deserialize(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += _getByteLength(object.message);
    length += _getByteLength(object.goal_id);
    length += SemanticPath.getMessageSize(object.planned_path);
    return length + 13;
  }

  static datatype() {
    // Returns string type for a service object
    return 'semantic_navigation/SetNavigationGoalResponse';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return 'ab557ff716958fd7a5cc224181312313';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    
    # 响应
    bool success
    string message
    string goal_id              # 目标ID，用于跟踪
    float32 estimated_time      # 预计完成时间
    SemanticPath planned_path   # 规划的路径
    
    
    ================================================================================
    MSG: semantic_navigation/SemanticPath
    # 语义路径消息
    Header header
    
    # 路径信息
    nav_msgs/Path path
    
    # 语义信息
    string[] semantic_labels     # 路径上的语义标签
    float32[] semantic_costs     # 对应的语义代价
    float32[] safety_scores      # 安全性评分
    
    # 路径属性
    float32 total_distance       # 总距离
    float32 total_cost          # 总代价
    float32 safety_rating       # 安全评级 (0-1)
    bool contains_fire_risk     # 是否包含火灾风险
    
    # 应急信息
    geometry_msgs/Point[] emergency_exits  # 应急出口位置
    string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL
    
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: nav_msgs/Path
    #An array of poses that represents a Path for a robot to follow
    Header header
    geometry_msgs/PoseStamped[] poses
    
    ================================================================================
    MSG: geometry_msgs/PoseStamped
    # A Pose with reference coordinate frame and timestamp
    Header header
    Pose pose
    
    ================================================================================
    MSG: geometry_msgs/Pose
    # A representation of pose in free space, composed of position and orientation. 
    Point position
    Quaternion orientation
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    ================================================================================
    MSG: geometry_msgs/Quaternion
    # This represents an orientation in free space in quaternion form.
    
    float64 x
    float64 y
    float64 z
    float64 w
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new SetNavigationGoalResponse(null);
    if (msg.success !== undefined) {
      resolved.success = msg.success;
    }
    else {
      resolved.success = false
    }

    if (msg.message !== undefined) {
      resolved.message = msg.message;
    }
    else {
      resolved.message = ''
    }

    if (msg.goal_id !== undefined) {
      resolved.goal_id = msg.goal_id;
    }
    else {
      resolved.goal_id = ''
    }

    if (msg.estimated_time !== undefined) {
      resolved.estimated_time = msg.estimated_time;
    }
    else {
      resolved.estimated_time = 0.0
    }

    if (msg.planned_path !== undefined) {
      resolved.planned_path = SemanticPath.Resolve(msg.planned_path)
    }
    else {
      resolved.planned_path = new SemanticPath()
    }

    return resolved;
    }
};

module.exports = {
  Request: SetNavigationGoalRequest,
  Response: SetNavigationGoalResponse,
  md5sum() { return '9b33341aa6d4bf3615200fa760500108'; },
  datatype() { return 'semantic_navigation/SetNavigationGoal'; }
};
