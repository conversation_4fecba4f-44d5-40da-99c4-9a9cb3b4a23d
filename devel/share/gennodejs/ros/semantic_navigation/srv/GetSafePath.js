// Auto-generated. Do not edit!

// (in-package semantic_navigation.srv)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;
let geometry_msgs = _finder('geometry_msgs');

//-----------------------------------------------------------

let SemanticPath = require('../msg/SemanticPath.js');

//-----------------------------------------------------------

class GetSafePathRequest {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.start_pose = null;
      this.goal_pose = null;
      this.hazard_types = null;
      this.safety_margin = null;
      this.emergency_mode = null;
    }
    else {
      if (initObj.hasOwnProperty('start_pose')) {
        this.start_pose = initObj.start_pose
      }
      else {
        this.start_pose = new geometry_msgs.msg.PoseStamped();
      }
      if (initObj.hasOwnProperty('goal_pose')) {
        this.goal_pose = initObj.goal_pose
      }
      else {
        this.goal_pose = new geometry_msgs.msg.PoseStamped();
      }
      if (initObj.hasOwnProperty('hazard_types')) {
        this.hazard_types = initObj.hazard_types
      }
      else {
        this.hazard_types = [];
      }
      if (initObj.hasOwnProperty('safety_margin')) {
        this.safety_margin = initObj.safety_margin
      }
      else {
        this.safety_margin = 0.0;
      }
      if (initObj.hasOwnProperty('emergency_mode')) {
        this.emergency_mode = initObj.emergency_mode
      }
      else {
        this.emergency_mode = false;
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type GetSafePathRequest
    // Serialize message field [start_pose]
    bufferOffset = geometry_msgs.msg.PoseStamped.serialize(obj.start_pose, buffer, bufferOffset);
    // Serialize message field [goal_pose]
    bufferOffset = geometry_msgs.msg.PoseStamped.serialize(obj.goal_pose, buffer, bufferOffset);
    // Serialize message field [hazard_types]
    bufferOffset = _arraySerializer.string(obj.hazard_types, buffer, bufferOffset, null);
    // Serialize message field [safety_margin]
    bufferOffset = _serializer.float32(obj.safety_margin, buffer, bufferOffset);
    // Serialize message field [emergency_mode]
    bufferOffset = _serializer.bool(obj.emergency_mode, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type GetSafePathRequest
    let len;
    let data = new GetSafePathRequest(null);
    // Deserialize message field [start_pose]
    data.start_pose = geometry_msgs.msg.PoseStamped.deserialize(buffer, bufferOffset);
    // Deserialize message field [goal_pose]
    data.goal_pose = geometry_msgs.msg.PoseStamped.deserialize(buffer, bufferOffset);
    // Deserialize message field [hazard_types]
    data.hazard_types = _arrayDeserializer.string(buffer, bufferOffset, null)
    // Deserialize message field [safety_margin]
    data.safety_margin = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [emergency_mode]
    data.emergency_mode = _deserializer.bool(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += geometry_msgs.msg.PoseStamped.getMessageSize(object.start_pose);
    length += geometry_msgs.msg.PoseStamped.getMessageSize(object.goal_pose);
    object.hazard_types.forEach((val) => {
      length += 4 + _getByteLength(val);
    });
    return length + 9;
  }

  static datatype() {
    // Returns string type for a service object
    return 'semantic_navigation/GetSafePathRequest';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return 'e74d023e9b083ae9fef6c469b92b6275';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    # 获取安全路径服务
    # 请求
    geometry_msgs/PoseStamped start_pose
    geometry_msgs/PoseStamped goal_pose
    string[] hazard_types       # 需要避开的危险类型
    float32 safety_margin       # 安全边距
    bool emergency_mode         # 是否为应急模式
    
    
    ================================================================================
    MSG: geometry_msgs/PoseStamped
    # A Pose with reference coordinate frame and timestamp
    Header header
    Pose pose
    
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: geometry_msgs/Pose
    # A representation of pose in free space, composed of position and orientation. 
    Point position
    Quaternion orientation
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    ================================================================================
    MSG: geometry_msgs/Quaternion
    # This represents an orientation in free space in quaternion form.
    
    float64 x
    float64 y
    float64 z
    float64 w
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new GetSafePathRequest(null);
    if (msg.start_pose !== undefined) {
      resolved.start_pose = geometry_msgs.msg.PoseStamped.Resolve(msg.start_pose)
    }
    else {
      resolved.start_pose = new geometry_msgs.msg.PoseStamped()
    }

    if (msg.goal_pose !== undefined) {
      resolved.goal_pose = geometry_msgs.msg.PoseStamped.Resolve(msg.goal_pose)
    }
    else {
      resolved.goal_pose = new geometry_msgs.msg.PoseStamped()
    }

    if (msg.hazard_types !== undefined) {
      resolved.hazard_types = msg.hazard_types;
    }
    else {
      resolved.hazard_types = []
    }

    if (msg.safety_margin !== undefined) {
      resolved.safety_margin = msg.safety_margin;
    }
    else {
      resolved.safety_margin = 0.0
    }

    if (msg.emergency_mode !== undefined) {
      resolved.emergency_mode = msg.emergency_mode;
    }
    else {
      resolved.emergency_mode = false
    }

    return resolved;
    }
};

class GetSafePathResponse {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.success = null;
      this.message = null;
      this.safe_path = null;
      this.alternative_paths = null;
      this.safety_score = null;
      this.warnings = null;
    }
    else {
      if (initObj.hasOwnProperty('success')) {
        this.success = initObj.success
      }
      else {
        this.success = false;
      }
      if (initObj.hasOwnProperty('message')) {
        this.message = initObj.message
      }
      else {
        this.message = '';
      }
      if (initObj.hasOwnProperty('safe_path')) {
        this.safe_path = initObj.safe_path
      }
      else {
        this.safe_path = new SemanticPath();
      }
      if (initObj.hasOwnProperty('alternative_paths')) {
        this.alternative_paths = initObj.alternative_paths
      }
      else {
        this.alternative_paths = [];
      }
      if (initObj.hasOwnProperty('safety_score')) {
        this.safety_score = initObj.safety_score
      }
      else {
        this.safety_score = 0.0;
      }
      if (initObj.hasOwnProperty('warnings')) {
        this.warnings = initObj.warnings
      }
      else {
        this.warnings = [];
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type GetSafePathResponse
    // Serialize message field [success]
    bufferOffset = _serializer.bool(obj.success, buffer, bufferOffset);
    // Serialize message field [message]
    bufferOffset = _serializer.string(obj.message, buffer, bufferOffset);
    // Serialize message field [safe_path]
    bufferOffset = SemanticPath.serialize(obj.safe_path, buffer, bufferOffset);
    // Serialize message field [alternative_paths]
    // Serialize the length for message field [alternative_paths]
    bufferOffset = _serializer.uint32(obj.alternative_paths.length, buffer, bufferOffset);
    obj.alternative_paths.forEach((val) => {
      bufferOffset = SemanticPath.serialize(val, buffer, bufferOffset);
    });
    // Serialize message field [safety_score]
    bufferOffset = _serializer.float32(obj.safety_score, buffer, bufferOffset);
    // Serialize message field [warnings]
    bufferOffset = _arraySerializer.string(obj.warnings, buffer, bufferOffset, null);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type GetSafePathResponse
    let len;
    let data = new GetSafePathResponse(null);
    // Deserialize message field [success]
    data.success = _deserializer.bool(buffer, bufferOffset);
    // Deserialize message field [message]
    data.message = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [safe_path]
    data.safe_path = SemanticPath.deserialize(buffer, bufferOffset);
    // Deserialize message field [alternative_paths]
    // Deserialize array length for message field [alternative_paths]
    len = _deserializer.uint32(buffer, bufferOffset);
    data.alternative_paths = new Array(len);
    for (let i = 0; i < len; ++i) {
      data.alternative_paths[i] = SemanticPath.deserialize(buffer, bufferOffset)
    }
    // Deserialize message field [safety_score]
    data.safety_score = _deserializer.float32(buffer, bufferOffset);
    // Deserialize message field [warnings]
    data.warnings = _arrayDeserializer.string(buffer, bufferOffset, null)
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += _getByteLength(object.message);
    length += SemanticPath.getMessageSize(object.safe_path);
    object.alternative_paths.forEach((val) => {
      length += SemanticPath.getMessageSize(val);
    });
    object.warnings.forEach((val) => {
      length += 4 + _getByteLength(val);
    });
    return length + 17;
  }

  static datatype() {
    // Returns string type for a service object
    return 'semantic_navigation/GetSafePathResponse';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return '2fe71afaae051d5769cd4b1deee26420';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    
    # 响应
    bool success
    string message
    SemanticPath safe_path      # 安全路径
    SemanticPath[] alternative_paths  # 备选路径
    float32 safety_score        # 安全评分
    string[] warnings           # 警告信息
    
    
    ================================================================================
    MSG: semantic_navigation/SemanticPath
    # 语义路径消息
    Header header
    
    # 路径信息
    nav_msgs/Path path
    
    # 语义信息
    string[] semantic_labels     # 路径上的语义标签
    float32[] semantic_costs     # 对应的语义代价
    float32[] safety_scores      # 安全性评分
    
    # 路径属性
    float32 total_distance       # 总距离
    float32 total_cost          # 总代价
    float32 safety_rating       # 安全评级 (0-1)
    bool contains_fire_risk     # 是否包含火灾风险
    
    # 应急信息
    geometry_msgs/Point[] emergency_exits  # 应急出口位置
    string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL
    
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: nav_msgs/Path
    #An array of poses that represents a Path for a robot to follow
    Header header
    geometry_msgs/PoseStamped[] poses
    
    ================================================================================
    MSG: geometry_msgs/PoseStamped
    # A Pose with reference coordinate frame and timestamp
    Header header
    Pose pose
    
    ================================================================================
    MSG: geometry_msgs/Pose
    # A representation of pose in free space, composed of position and orientation. 
    Point position
    Quaternion orientation
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    ================================================================================
    MSG: geometry_msgs/Quaternion
    # This represents an orientation in free space in quaternion form.
    
    float64 x
    float64 y
    float64 z
    float64 w
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new GetSafePathResponse(null);
    if (msg.success !== undefined) {
      resolved.success = msg.success;
    }
    else {
      resolved.success = false
    }

    if (msg.message !== undefined) {
      resolved.message = msg.message;
    }
    else {
      resolved.message = ''
    }

    if (msg.safe_path !== undefined) {
      resolved.safe_path = SemanticPath.Resolve(msg.safe_path)
    }
    else {
      resolved.safe_path = new SemanticPath()
    }

    if (msg.alternative_paths !== undefined) {
      resolved.alternative_paths = new Array(msg.alternative_paths.length);
      for (let i = 0; i < resolved.alternative_paths.length; ++i) {
        resolved.alternative_paths[i] = SemanticPath.Resolve(msg.alternative_paths[i]);
      }
    }
    else {
      resolved.alternative_paths = []
    }

    if (msg.safety_score !== undefined) {
      resolved.safety_score = msg.safety_score;
    }
    else {
      resolved.safety_score = 0.0
    }

    if (msg.warnings !== undefined) {
      resolved.warnings = msg.warnings;
    }
    else {
      resolved.warnings = []
    }

    return resolved;
    }
};

module.exports = {
  Request: GetSafePathRequest,
  Response: GetSafePathResponse,
  md5sum() { return '677123b52cef0c55e41c66d038e4b3a2'; },
  datatype() { return 'semantic_navigation/GetSafePath'; }
};
