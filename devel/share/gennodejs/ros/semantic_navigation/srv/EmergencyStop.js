// Auto-generated. Do not edit!

// (in-package semantic_navigation.srv)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;
let geometry_msgs = _finder('geometry_msgs');

//-----------------------------------------------------------


//-----------------------------------------------------------

class EmergencyStopRequest {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.reason = null;
      this.immediate = null;
      this.hazard_location = null;
    }
    else {
      if (initObj.hasOwnProperty('reason')) {
        this.reason = initObj.reason
      }
      else {
        this.reason = '';
      }
      if (initObj.hasOwnProperty('immediate')) {
        this.immediate = initObj.immediate
      }
      else {
        this.immediate = false;
      }
      if (initObj.hasOwnProperty('hazard_location')) {
        this.hazard_location = initObj.hazard_location
      }
      else {
        this.hazard_location = new geometry_msgs.msg.Point();
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type EmergencyStopRequest
    // Serialize message field [reason]
    bufferOffset = _serializer.string(obj.reason, buffer, bufferOffset);
    // Serialize message field [immediate]
    bufferOffset = _serializer.bool(obj.immediate, buffer, bufferOffset);
    // Serialize message field [hazard_location]
    bufferOffset = geometry_msgs.msg.Point.serialize(obj.hazard_location, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type EmergencyStopRequest
    let len;
    let data = new EmergencyStopRequest(null);
    // Deserialize message field [reason]
    data.reason = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [immediate]
    data.immediate = _deserializer.bool(buffer, bufferOffset);
    // Deserialize message field [hazard_location]
    data.hazard_location = geometry_msgs.msg.Point.deserialize(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += _getByteLength(object.reason);
    return length + 29;
  }

  static datatype() {
    // Returns string type for a service object
    return 'semantic_navigation/EmergencyStopRequest';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return '6b8b6a2e505f7fd67061116b01ac9322';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    # 应急停止服务
    # 请求
    string reason               # 停止原因
    bool immediate              # 是否立即停止
    geometry_msgs/Point hazard_location  # 危险位置（可选）
    
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new EmergencyStopRequest(null);
    if (msg.reason !== undefined) {
      resolved.reason = msg.reason;
    }
    else {
      resolved.reason = ''
    }

    if (msg.immediate !== undefined) {
      resolved.immediate = msg.immediate;
    }
    else {
      resolved.immediate = false
    }

    if (msg.hazard_location !== undefined) {
      resolved.hazard_location = geometry_msgs.msg.Point.Resolve(msg.hazard_location)
    }
    else {
      resolved.hazard_location = new geometry_msgs.msg.Point()
    }

    return resolved;
    }
};

class EmergencyStopResponse {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.success = null;
      this.message = null;
      this.stop_time = null;
      this.final_pose = null;
    }
    else {
      if (initObj.hasOwnProperty('success')) {
        this.success = initObj.success
      }
      else {
        this.success = false;
      }
      if (initObj.hasOwnProperty('message')) {
        this.message = initObj.message
      }
      else {
        this.message = '';
      }
      if (initObj.hasOwnProperty('stop_time')) {
        this.stop_time = initObj.stop_time
      }
      else {
        this.stop_time = {secs: 0, nsecs: 0};
      }
      if (initObj.hasOwnProperty('final_pose')) {
        this.final_pose = initObj.final_pose
      }
      else {
        this.final_pose = new geometry_msgs.msg.PoseStamped();
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type EmergencyStopResponse
    // Serialize message field [success]
    bufferOffset = _serializer.bool(obj.success, buffer, bufferOffset);
    // Serialize message field [message]
    bufferOffset = _serializer.string(obj.message, buffer, bufferOffset);
    // Serialize message field [stop_time]
    bufferOffset = _serializer.time(obj.stop_time, buffer, bufferOffset);
    // Serialize message field [final_pose]
    bufferOffset = geometry_msgs.msg.PoseStamped.serialize(obj.final_pose, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type EmergencyStopResponse
    let len;
    let data = new EmergencyStopResponse(null);
    // Deserialize message field [success]
    data.success = _deserializer.bool(buffer, bufferOffset);
    // Deserialize message field [message]
    data.message = _deserializer.string(buffer, bufferOffset);
    // Deserialize message field [stop_time]
    data.stop_time = _deserializer.time(buffer, bufferOffset);
    // Deserialize message field [final_pose]
    data.final_pose = geometry_msgs.msg.PoseStamped.deserialize(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += _getByteLength(object.message);
    length += geometry_msgs.msg.PoseStamped.getMessageSize(object.final_pose);
    return length + 13;
  }

  static datatype() {
    // Returns string type for a service object
    return 'semantic_navigation/EmergencyStopResponse';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return 'cf2b4c3181dfd9b92e1cc65d57d3a7eb';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    
    # 响应
    bool success
    string message
    time stop_time              # 停止时间
    geometry_msgs/PoseStamped final_pose  # 最终位置
    
    
    ================================================================================
    MSG: geometry_msgs/PoseStamped
    # A Pose with reference coordinate frame and timestamp
    Header header
    Pose pose
    
    ================================================================================
    MSG: std_msgs/Header
    # Standard metadata for higher-level stamped data types.
    # This is generally used to communicate timestamped data 
    # in a particular coordinate frame.
    # 
    # sequence ID: consecutively increasing ID 
    uint32 seq
    #Two-integer timestamp that is expressed as:
    # * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
    # * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
    # time-handling sugar is provided by the client library
    time stamp
    #Frame this data is associated with
    string frame_id
    
    ================================================================================
    MSG: geometry_msgs/Pose
    # A representation of pose in free space, composed of position and orientation. 
    Point position
    Quaternion orientation
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    ================================================================================
    MSG: geometry_msgs/Quaternion
    # This represents an orientation in free space in quaternion form.
    
    float64 x
    float64 y
    float64 z
    float64 w
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new EmergencyStopResponse(null);
    if (msg.success !== undefined) {
      resolved.success = msg.success;
    }
    else {
      resolved.success = false
    }

    if (msg.message !== undefined) {
      resolved.message = msg.message;
    }
    else {
      resolved.message = ''
    }

    if (msg.stop_time !== undefined) {
      resolved.stop_time = msg.stop_time;
    }
    else {
      resolved.stop_time = {secs: 0, nsecs: 0}
    }

    if (msg.final_pose !== undefined) {
      resolved.final_pose = geometry_msgs.msg.PoseStamped.Resolve(msg.final_pose)
    }
    else {
      resolved.final_pose = new geometry_msgs.msg.PoseStamped()
    }

    return resolved;
    }
};

module.exports = {
  Request: EmergencyStopRequest,
  Response: EmergencyStopResponse,
  md5sum() { return '13ca1b92fd866cf0141e505652497177'; },
  datatype() { return 'semantic_navigation/EmergencyStop'; }
};
