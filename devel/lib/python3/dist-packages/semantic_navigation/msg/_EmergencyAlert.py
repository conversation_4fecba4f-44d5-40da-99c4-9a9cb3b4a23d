# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from semantic_navigation/EmergencyAlert.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import genpy
import geometry_msgs.msg
import std_msgs.msg

class EmergencyAlert(genpy.Message):
  _md5sum = "a8796edaa18ec76393edd2510a2b4bdd"
  _type = "semantic_navigation/EmergencyAlert"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """# 应急警报消息
Header header

# 警报类型
string alert_type           # FIRE, SMOKE, OBSTACLE, SYSTEM_FAILURE

# 警报等级
string severity             # LOW, MEDIUM, HIGH, CRITICAL

# 位置信息
geometry_msgs/Point location
float32 affected_radius     # 影响半径 (米)

# 描述信息
string description          # 警报描述
string recommended_action   # 建议行动

# 时间信息
time detection_time         # 检测时间
duration estimated_duration # 预计持续时间

# 相关数据
float32 confidence          # 置信度
string[] related_objects    # 相关物体

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z
"""
  __slots__ = ['header','alert_type','severity','location','affected_radius','description','recommended_action','detection_time','estimated_duration','confidence','related_objects']
  _slot_types = ['std_msgs/Header','string','string','geometry_msgs/Point','float32','string','string','time','duration','float32','string[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,alert_type,severity,location,affected_radius,description,recommended_action,detection_time,estimated_duration,confidence,related_objects

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(EmergencyAlert, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.alert_type is None:
        self.alert_type = ''
      if self.severity is None:
        self.severity = ''
      if self.location is None:
        self.location = geometry_msgs.msg.Point()
      if self.affected_radius is None:
        self.affected_radius = 0.
      if self.description is None:
        self.description = ''
      if self.recommended_action is None:
        self.recommended_action = ''
      if self.detection_time is None:
        self.detection_time = genpy.Time()
      if self.estimated_duration is None:
        self.estimated_duration = genpy.Duration()
      if self.confidence is None:
        self.confidence = 0.
      if self.related_objects is None:
        self.related_objects = []
    else:
      self.header = std_msgs.msg.Header()
      self.alert_type = ''
      self.severity = ''
      self.location = geometry_msgs.msg.Point()
      self.affected_radius = 0.
      self.description = ''
      self.recommended_action = ''
      self.detection_time = genpy.Time()
      self.estimated_duration = genpy.Duration()
      self.confidence = 0.
      self.related_objects = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.alert_type
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.severity
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3df().pack(_x.location.x, _x.location.y, _x.location.z, _x.affected_radius))
      _x = self.description
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.recommended_action
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_2I2if().pack(_x.detection_time.secs, _x.detection_time.nsecs, _x.estimated_duration.secs, _x.estimated_duration.nsecs, _x.confidence))
      length = len(self.related_objects)
      buff.write(_struct_I.pack(length))
      for val1 in self.related_objects:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.location is None:
        self.location = geometry_msgs.msg.Point()
      if self.detection_time is None:
        self.detection_time = genpy.Time()
      if self.estimated_duration is None:
        self.estimated_duration = genpy.Duration()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.alert_type = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.alert_type = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.severity = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.severity = str[start:end]
      _x = self
      start = end
      end += 28
      (_x.location.x, _x.location.y, _x.location.z, _x.affected_radius,) = _get_struct_3df().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.description = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.description = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.recommended_action = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.recommended_action = str[start:end]
      _x = self
      start = end
      end += 20
      (_x.detection_time.secs, _x.detection_time.nsecs, _x.estimated_duration.secs, _x.estimated_duration.nsecs, _x.confidence,) = _get_struct_2I2if().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.related_objects = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.related_objects.append(val1)
      self.detection_time.canon()
      self.estimated_duration.canon()
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.alert_type
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.severity
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3df().pack(_x.location.x, _x.location.y, _x.location.z, _x.affected_radius))
      _x = self.description
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.recommended_action
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_2I2if().pack(_x.detection_time.secs, _x.detection_time.nsecs, _x.estimated_duration.secs, _x.estimated_duration.nsecs, _x.confidence))
      length = len(self.related_objects)
      buff.write(_struct_I.pack(length))
      for val1 in self.related_objects:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.location is None:
        self.location = geometry_msgs.msg.Point()
      if self.detection_time is None:
        self.detection_time = genpy.Time()
      if self.estimated_duration is None:
        self.estimated_duration = genpy.Duration()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.alert_type = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.alert_type = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.severity = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.severity = str[start:end]
      _x = self
      start = end
      end += 28
      (_x.location.x, _x.location.y, _x.location.z, _x.affected_radius,) = _get_struct_3df().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.description = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.description = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.recommended_action = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.recommended_action = str[start:end]
      _x = self
      start = end
      end += 20
      (_x.detection_time.secs, _x.detection_time.nsecs, _x.estimated_duration.secs, _x.estimated_duration.nsecs, _x.confidence,) = _get_struct_2I2if().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.related_objects = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.related_objects.append(val1)
      self.detection_time.canon()
      self.estimated_duration.canon()
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_2I2if = None
def _get_struct_2I2if():
    global _struct_2I2if
    if _struct_2I2if is None:
        _struct_2I2if = struct.Struct("<2I2if")
    return _struct_2I2if
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_3df = None
def _get_struct_3df():
    global _struct_3df
    if _struct_3df is None:
        _struct_3df = struct.Struct("<3df")
    return _struct_3df
