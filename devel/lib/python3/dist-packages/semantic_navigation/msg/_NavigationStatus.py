# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from semantic_navigation/NavigationStatus.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import genpy
import geometry_msgs.msg
import std_msgs.msg

class NavigationStatus(genpy.Message):
  _md5sum = "cdbdd9a0f2482e0f81f898630e3a3988"
  _type = "semantic_navigation/NavigationStatus"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """# 导航状态消息
Header header

# 导航状态
string state                # IDLE, PLANNING, NAVIGATING, EMERGENCY, STOPPED, FAILED
string mode                 # NORMAL, EMERGENCY, FIRE_ESCAPE

# 当前位置和目标
geometry_msgs/PoseStamped current_pose
geometry_msgs/PoseStamped target_pose

# 进度信息
float32 progress            # 完成进度 (0-1)
float32 distance_remaining  # 剩余距离
duration time_remaining     # 预计剩余时间

# 安全状态
bool fire_detected          # 是否检测到火焰
bool emergency_active       # 是否处于应急状态
string[] detected_hazards   # 检测到的危险

# 性能指标
float32 current_speed       # 当前速度
float32 path_deviation      # 路径偏差

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
"""
  __slots__ = ['header','state','mode','current_pose','target_pose','progress','distance_remaining','time_remaining','fire_detected','emergency_active','detected_hazards','current_speed','path_deviation']
  _slot_types = ['std_msgs/Header','string','string','geometry_msgs/PoseStamped','geometry_msgs/PoseStamped','float32','float32','duration','bool','bool','string[]','float32','float32']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,state,mode,current_pose,target_pose,progress,distance_remaining,time_remaining,fire_detected,emergency_active,detected_hazards,current_speed,path_deviation

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(NavigationStatus, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.state is None:
        self.state = ''
      if self.mode is None:
        self.mode = ''
      if self.current_pose is None:
        self.current_pose = geometry_msgs.msg.PoseStamped()
      if self.target_pose is None:
        self.target_pose = geometry_msgs.msg.PoseStamped()
      if self.progress is None:
        self.progress = 0.
      if self.distance_remaining is None:
        self.distance_remaining = 0.
      if self.time_remaining is None:
        self.time_remaining = genpy.Duration()
      if self.fire_detected is None:
        self.fire_detected = False
      if self.emergency_active is None:
        self.emergency_active = False
      if self.detected_hazards is None:
        self.detected_hazards = []
      if self.current_speed is None:
        self.current_speed = 0.
      if self.path_deviation is None:
        self.path_deviation = 0.
    else:
      self.header = std_msgs.msg.Header()
      self.state = ''
      self.mode = ''
      self.current_pose = geometry_msgs.msg.PoseStamped()
      self.target_pose = geometry_msgs.msg.PoseStamped()
      self.progress = 0.
      self.distance_remaining = 0.
      self.time_remaining = genpy.Duration()
      self.fire_detected = False
      self.emergency_active = False
      self.detected_hazards = []
      self.current_speed = 0.
      self.path_deviation = 0.

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.state
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.mode
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3I().pack(_x.current_pose.header.seq, _x.current_pose.header.stamp.secs, _x.current_pose.header.stamp.nsecs))
      _x = self.current_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d3I().pack(_x.current_pose.pose.position.x, _x.current_pose.pose.position.y, _x.current_pose.pose.position.z, _x.current_pose.pose.orientation.x, _x.current_pose.pose.orientation.y, _x.current_pose.pose.orientation.z, _x.current_pose.pose.orientation.w, _x.target_pose.header.seq, _x.target_pose.header.stamp.secs, _x.target_pose.header.stamp.nsecs))
      _x = self.target_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d2f2i2B().pack(_x.target_pose.pose.position.x, _x.target_pose.pose.position.y, _x.target_pose.pose.position.z, _x.target_pose.pose.orientation.x, _x.target_pose.pose.orientation.y, _x.target_pose.pose.orientation.z, _x.target_pose.pose.orientation.w, _x.progress, _x.distance_remaining, _x.time_remaining.secs, _x.time_remaining.nsecs, _x.fire_detected, _x.emergency_active))
      length = len(self.detected_hazards)
      buff.write(_struct_I.pack(length))
      for val1 in self.detected_hazards:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      _x = self
      buff.write(_get_struct_2f().pack(_x.current_speed, _x.path_deviation))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.current_pose is None:
        self.current_pose = geometry_msgs.msg.PoseStamped()
      if self.target_pose is None:
        self.target_pose = geometry_msgs.msg.PoseStamped()
      if self.time_remaining is None:
        self.time_remaining = genpy.Duration()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.state = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.state = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.mode = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.mode = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.current_pose.header.seq, _x.current_pose.header.stamp.secs, _x.current_pose.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.current_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.current_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 68
      (_x.current_pose.pose.position.x, _x.current_pose.pose.position.y, _x.current_pose.pose.position.z, _x.current_pose.pose.orientation.x, _x.current_pose.pose.orientation.y, _x.current_pose.pose.orientation.z, _x.current_pose.pose.orientation.w, _x.target_pose.header.seq, _x.target_pose.header.stamp.secs, _x.target_pose.header.stamp.nsecs,) = _get_struct_7d3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.target_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.target_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 74
      (_x.target_pose.pose.position.x, _x.target_pose.pose.position.y, _x.target_pose.pose.position.z, _x.target_pose.pose.orientation.x, _x.target_pose.pose.orientation.y, _x.target_pose.pose.orientation.z, _x.target_pose.pose.orientation.w, _x.progress, _x.distance_remaining, _x.time_remaining.secs, _x.time_remaining.nsecs, _x.fire_detected, _x.emergency_active,) = _get_struct_7d2f2i2B().unpack(str[start:end])
      self.fire_detected = bool(self.fire_detected)
      self.emergency_active = bool(self.emergency_active)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.detected_hazards = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.detected_hazards.append(val1)
      _x = self
      start = end
      end += 8
      (_x.current_speed, _x.path_deviation,) = _get_struct_2f().unpack(str[start:end])
      self.time_remaining.canon()
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.state
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.mode
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3I().pack(_x.current_pose.header.seq, _x.current_pose.header.stamp.secs, _x.current_pose.header.stamp.nsecs))
      _x = self.current_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d3I().pack(_x.current_pose.pose.position.x, _x.current_pose.pose.position.y, _x.current_pose.pose.position.z, _x.current_pose.pose.orientation.x, _x.current_pose.pose.orientation.y, _x.current_pose.pose.orientation.z, _x.current_pose.pose.orientation.w, _x.target_pose.header.seq, _x.target_pose.header.stamp.secs, _x.target_pose.header.stamp.nsecs))
      _x = self.target_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d2f2i2B().pack(_x.target_pose.pose.position.x, _x.target_pose.pose.position.y, _x.target_pose.pose.position.z, _x.target_pose.pose.orientation.x, _x.target_pose.pose.orientation.y, _x.target_pose.pose.orientation.z, _x.target_pose.pose.orientation.w, _x.progress, _x.distance_remaining, _x.time_remaining.secs, _x.time_remaining.nsecs, _x.fire_detected, _x.emergency_active))
      length = len(self.detected_hazards)
      buff.write(_struct_I.pack(length))
      for val1 in self.detected_hazards:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      _x = self
      buff.write(_get_struct_2f().pack(_x.current_speed, _x.path_deviation))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.current_pose is None:
        self.current_pose = geometry_msgs.msg.PoseStamped()
      if self.target_pose is None:
        self.target_pose = geometry_msgs.msg.PoseStamped()
      if self.time_remaining is None:
        self.time_remaining = genpy.Duration()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.state = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.state = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.mode = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.mode = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.current_pose.header.seq, _x.current_pose.header.stamp.secs, _x.current_pose.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.current_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.current_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 68
      (_x.current_pose.pose.position.x, _x.current_pose.pose.position.y, _x.current_pose.pose.position.z, _x.current_pose.pose.orientation.x, _x.current_pose.pose.orientation.y, _x.current_pose.pose.orientation.z, _x.current_pose.pose.orientation.w, _x.target_pose.header.seq, _x.target_pose.header.stamp.secs, _x.target_pose.header.stamp.nsecs,) = _get_struct_7d3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.target_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.target_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 74
      (_x.target_pose.pose.position.x, _x.target_pose.pose.position.y, _x.target_pose.pose.position.z, _x.target_pose.pose.orientation.x, _x.target_pose.pose.orientation.y, _x.target_pose.pose.orientation.z, _x.target_pose.pose.orientation.w, _x.progress, _x.distance_remaining, _x.time_remaining.secs, _x.time_remaining.nsecs, _x.fire_detected, _x.emergency_active,) = _get_struct_7d2f2i2B().unpack(str[start:end])
      self.fire_detected = bool(self.fire_detected)
      self.emergency_active = bool(self.emergency_active)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.detected_hazards = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.detected_hazards.append(val1)
      _x = self
      start = end
      end += 8
      (_x.current_speed, _x.path_deviation,) = _get_struct_2f().unpack(str[start:end])
      self.time_remaining.canon()
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_2f = None
def _get_struct_2f():
    global _struct_2f
    if _struct_2f is None:
        _struct_2f = struct.Struct("<2f")
    return _struct_2f
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_7d2f2i2B = None
def _get_struct_7d2f2i2B():
    global _struct_7d2f2i2B
    if _struct_7d2f2i2B is None:
        _struct_7d2f2i2B = struct.Struct("<7d2f2i2B")
    return _struct_7d2f2i2B
_struct_7d3I = None
def _get_struct_7d3I():
    global _struct_7d3I
    if _struct_7d3I is None:
        _struct_7d3I = struct.Struct("<7d3I")
    return _struct_7d3I
