# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from semantic_navigation/GetSafePathRequest.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg
import std_msgs.msg

class GetSafePathRequest(genpy.Message):
  _md5sum = "e74d023e9b083ae9fef6c469b92b6275"
  _type = "semantic_navigation/GetSafePathRequest"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """# 获取安全路径服务
# 请求
geometry_msgs/PoseStamped start_pose
geometry_msgs/PoseStamped goal_pose
string[] hazard_types       # 需要避开的危险类型
float32 safety_margin       # 安全边距
bool emergency_mode         # 是否为应急模式


================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
"""
  __slots__ = ['start_pose','goal_pose','hazard_types','safety_margin','emergency_mode']
  _slot_types = ['geometry_msgs/PoseStamped','geometry_msgs/PoseStamped','string[]','float32','bool']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       start_pose,goal_pose,hazard_types,safety_margin,emergency_mode

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(GetSafePathRequest, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.start_pose is None:
        self.start_pose = geometry_msgs.msg.PoseStamped()
      if self.goal_pose is None:
        self.goal_pose = geometry_msgs.msg.PoseStamped()
      if self.hazard_types is None:
        self.hazard_types = []
      if self.safety_margin is None:
        self.safety_margin = 0.
      if self.emergency_mode is None:
        self.emergency_mode = False
    else:
      self.start_pose = geometry_msgs.msg.PoseStamped()
      self.goal_pose = geometry_msgs.msg.PoseStamped()
      self.hazard_types = []
      self.safety_margin = 0.
      self.emergency_mode = False

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.start_pose.header.seq, _x.start_pose.header.stamp.secs, _x.start_pose.header.stamp.nsecs))
      _x = self.start_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d3I().pack(_x.start_pose.pose.position.x, _x.start_pose.pose.position.y, _x.start_pose.pose.position.z, _x.start_pose.pose.orientation.x, _x.start_pose.pose.orientation.y, _x.start_pose.pose.orientation.z, _x.start_pose.pose.orientation.w, _x.goal_pose.header.seq, _x.goal_pose.header.stamp.secs, _x.goal_pose.header.stamp.nsecs))
      _x = self.goal_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d().pack(_x.goal_pose.pose.position.x, _x.goal_pose.pose.position.y, _x.goal_pose.pose.position.z, _x.goal_pose.pose.orientation.x, _x.goal_pose.pose.orientation.y, _x.goal_pose.pose.orientation.z, _x.goal_pose.pose.orientation.w))
      length = len(self.hazard_types)
      buff.write(_struct_I.pack(length))
      for val1 in self.hazard_types:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      _x = self
      buff.write(_get_struct_fB().pack(_x.safety_margin, _x.emergency_mode))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.start_pose is None:
        self.start_pose = geometry_msgs.msg.PoseStamped()
      if self.goal_pose is None:
        self.goal_pose = geometry_msgs.msg.PoseStamped()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.start_pose.header.seq, _x.start_pose.header.stamp.secs, _x.start_pose.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.start_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.start_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 68
      (_x.start_pose.pose.position.x, _x.start_pose.pose.position.y, _x.start_pose.pose.position.z, _x.start_pose.pose.orientation.x, _x.start_pose.pose.orientation.y, _x.start_pose.pose.orientation.z, _x.start_pose.pose.orientation.w, _x.goal_pose.header.seq, _x.goal_pose.header.stamp.secs, _x.goal_pose.header.stamp.nsecs,) = _get_struct_7d3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.goal_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.goal_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 56
      (_x.goal_pose.pose.position.x, _x.goal_pose.pose.position.y, _x.goal_pose.pose.position.z, _x.goal_pose.pose.orientation.x, _x.goal_pose.pose.orientation.y, _x.goal_pose.pose.orientation.z, _x.goal_pose.pose.orientation.w,) = _get_struct_7d().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.hazard_types = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.hazard_types.append(val1)
      _x = self
      start = end
      end += 5
      (_x.safety_margin, _x.emergency_mode,) = _get_struct_fB().unpack(str[start:end])
      self.emergency_mode = bool(self.emergency_mode)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.start_pose.header.seq, _x.start_pose.header.stamp.secs, _x.start_pose.header.stamp.nsecs))
      _x = self.start_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d3I().pack(_x.start_pose.pose.position.x, _x.start_pose.pose.position.y, _x.start_pose.pose.position.z, _x.start_pose.pose.orientation.x, _x.start_pose.pose.orientation.y, _x.start_pose.pose.orientation.z, _x.start_pose.pose.orientation.w, _x.goal_pose.header.seq, _x.goal_pose.header.stamp.secs, _x.goal_pose.header.stamp.nsecs))
      _x = self.goal_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d().pack(_x.goal_pose.pose.position.x, _x.goal_pose.pose.position.y, _x.goal_pose.pose.position.z, _x.goal_pose.pose.orientation.x, _x.goal_pose.pose.orientation.y, _x.goal_pose.pose.orientation.z, _x.goal_pose.pose.orientation.w))
      length = len(self.hazard_types)
      buff.write(_struct_I.pack(length))
      for val1 in self.hazard_types:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      _x = self
      buff.write(_get_struct_fB().pack(_x.safety_margin, _x.emergency_mode))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.start_pose is None:
        self.start_pose = geometry_msgs.msg.PoseStamped()
      if self.goal_pose is None:
        self.goal_pose = geometry_msgs.msg.PoseStamped()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.start_pose.header.seq, _x.start_pose.header.stamp.secs, _x.start_pose.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.start_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.start_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 68
      (_x.start_pose.pose.position.x, _x.start_pose.pose.position.y, _x.start_pose.pose.position.z, _x.start_pose.pose.orientation.x, _x.start_pose.pose.orientation.y, _x.start_pose.pose.orientation.z, _x.start_pose.pose.orientation.w, _x.goal_pose.header.seq, _x.goal_pose.header.stamp.secs, _x.goal_pose.header.stamp.nsecs,) = _get_struct_7d3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.goal_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.goal_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 56
      (_x.goal_pose.pose.position.x, _x.goal_pose.pose.position.y, _x.goal_pose.pose.position.z, _x.goal_pose.pose.orientation.x, _x.goal_pose.pose.orientation.y, _x.goal_pose.pose.orientation.z, _x.goal_pose.pose.orientation.w,) = _get_struct_7d().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.hazard_types = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.hazard_types.append(val1)
      _x = self
      start = end
      end += 5
      (_x.safety_margin, _x.emergency_mode,) = _get_struct_fB().unpack(str[start:end])
      self.emergency_mode = bool(self.emergency_mode)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_7d = None
def _get_struct_7d():
    global _struct_7d
    if _struct_7d is None:
        _struct_7d = struct.Struct("<7d")
    return _struct_7d
_struct_7d3I = None
def _get_struct_7d3I():
    global _struct_7d3I
    if _struct_7d3I is None:
        _struct_7d3I = struct.Struct("<7d3I")
    return _struct_7d3I
_struct_fB = None
def _get_struct_fB():
    global _struct_fB
    if _struct_fB is None:
        _struct_fB = struct.Struct("<fB")
    return _struct_fB
# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from semantic_navigation/GetSafePathResponse.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg
import nav_msgs.msg
import semantic_navigation.msg
import std_msgs.msg

class GetSafePathResponse(genpy.Message):
  _md5sum = "2fe71afaae051d5769cd4b1deee26420"
  _type = "semantic_navigation/GetSafePathResponse"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """
# 响应
bool success
string message
SemanticPath safe_path      # 安全路径
SemanticPath[] alternative_paths  # 备选路径
float32 safety_score        # 安全评分
string[] warnings           # 警告信息


================================================================================
MSG: semantic_navigation/SemanticPath
# 语义路径消息
Header header

# 路径信息
nav_msgs/Path path

# 语义信息
string[] semantic_labels     # 路径上的语义标签
float32[] semantic_costs     # 对应的语义代价
float32[] safety_scores      # 安全性评分

# 路径属性
float32 total_distance       # 总距离
float32 total_cost          # 总代价
float32 safety_rating       # 安全评级 (0-1)
bool contains_fire_risk     # 是否包含火灾风险

# 应急信息
geometry_msgs/Point[] emergency_exits  # 应急出口位置
string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: nav_msgs/Path
#An array of poses that represents a Path for a robot to follow
Header header
geometry_msgs/PoseStamped[] poses

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
"""
  __slots__ = ['success','message','safe_path','alternative_paths','safety_score','warnings']
  _slot_types = ['bool','string','semantic_navigation/SemanticPath','semantic_navigation/SemanticPath[]','float32','string[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       success,message,safe_path,alternative_paths,safety_score,warnings

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(GetSafePathResponse, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.success is None:
        self.success = False
      if self.message is None:
        self.message = ''
      if self.safe_path is None:
        self.safe_path = semantic_navigation.msg.SemanticPath()
      if self.alternative_paths is None:
        self.alternative_paths = []
      if self.safety_score is None:
        self.safety_score = 0.
      if self.warnings is None:
        self.warnings = []
    else:
      self.success = False
      self.message = ''
      self.safe_path = semantic_navigation.msg.SemanticPath()
      self.alternative_paths = []
      self.safety_score = 0.
      self.warnings = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self.success
      buff.write(_get_struct_B().pack(_x))
      _x = self.message
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3I().pack(_x.safe_path.header.seq, _x.safe_path.header.stamp.secs, _x.safe_path.header.stamp.nsecs))
      _x = self.safe_path.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3I().pack(_x.safe_path.path.header.seq, _x.safe_path.path.header.stamp.secs, _x.safe_path.path.header.stamp.nsecs))
      _x = self.safe_path.path.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.safe_path.path.poses)
      buff.write(_struct_I.pack(length))
      for val1 in self.safe_path.path.poses:
        _v1 = val1.header
        _x = _v1.seq
        buff.write(_get_struct_I().pack(_x))
        _v2 = _v1.stamp
        _x = _v2
        buff.write(_get_struct_2I().pack(_x.secs, _x.nsecs))
        _x = _v1.frame_id
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
        _v3 = val1.pose
        _v4 = _v3.position
        _x = _v4
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        _v5 = _v3.orientation
        _x = _v5
        buff.write(_get_struct_4d().pack(_x.x, _x.y, _x.z, _x.w))
      length = len(self.safe_path.semantic_labels)
      buff.write(_struct_I.pack(length))
      for val1 in self.safe_path.semantic_labels:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      length = len(self.safe_path.semantic_costs)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(struct.Struct(pattern).pack(*self.safe_path.semantic_costs))
      length = len(self.safe_path.safety_scores)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(struct.Struct(pattern).pack(*self.safe_path.safety_scores))
      _x = self
      buff.write(_get_struct_3fB().pack(_x.safe_path.total_distance, _x.safe_path.total_cost, _x.safe_path.safety_rating, _x.safe_path.contains_fire_risk))
      length = len(self.safe_path.emergency_exits)
      buff.write(_struct_I.pack(length))
      for val1 in self.safe_path.emergency_exits:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
      _x = self.safe_path.risk_level
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.alternative_paths)
      buff.write(_struct_I.pack(length))
      for val1 in self.alternative_paths:
        _v6 = val1.header
        _x = _v6.seq
        buff.write(_get_struct_I().pack(_x))
        _v7 = _v6.stamp
        _x = _v7
        buff.write(_get_struct_2I().pack(_x.secs, _x.nsecs))
        _x = _v6.frame_id
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
        _v8 = val1.path
        _v9 = _v8.header
        _x = _v9.seq
        buff.write(_get_struct_I().pack(_x))
        _v10 = _v9.stamp
        _x = _v10
        buff.write(_get_struct_2I().pack(_x.secs, _x.nsecs))
        _x = _v9.frame_id
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
        length = len(_v8.poses)
        buff.write(_struct_I.pack(length))
        for val3 in _v8.poses:
          _v11 = val3.header
          _x = _v11.seq
          buff.write(_get_struct_I().pack(_x))
          _v12 = _v11.stamp
          _x = _v12
          buff.write(_get_struct_2I().pack(_x.secs, _x.nsecs))
          _x = _v11.frame_id
          length = len(_x)
          if python3 or type(_x) == unicode:
            _x = _x.encode('utf-8')
            length = len(_x)
          buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
          _v13 = val3.pose
          _v14 = _v13.position
          _x = _v14
          buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
          _v15 = _v13.orientation
          _x = _v15
          buff.write(_get_struct_4d().pack(_x.x, _x.y, _x.z, _x.w))
        length = len(val1.semantic_labels)
        buff.write(_struct_I.pack(length))
        for val2 in val1.semantic_labels:
          length = len(val2)
          if python3 or type(val2) == unicode:
            val2 = val2.encode('utf-8')
            length = len(val2)
          buff.write(struct.Struct('<I%ss'%length).pack(length, val2))
        length = len(val1.semantic_costs)
        buff.write(_struct_I.pack(length))
        pattern = '<%sf'%length
        buff.write(struct.Struct(pattern).pack(*val1.semantic_costs))
        length = len(val1.safety_scores)
        buff.write(_struct_I.pack(length))
        pattern = '<%sf'%length
        buff.write(struct.Struct(pattern).pack(*val1.safety_scores))
        _x = val1
        buff.write(_get_struct_3fB().pack(_x.total_distance, _x.total_cost, _x.safety_rating, _x.contains_fire_risk))
        length = len(val1.emergency_exits)
        buff.write(_struct_I.pack(length))
        for val2 in val1.emergency_exits:
          _x = val2
          buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        _x = val1.risk_level
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.safety_score
      buff.write(_get_struct_f().pack(_x))
      length = len(self.warnings)
      buff.write(_struct_I.pack(length))
      for val1 in self.warnings:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.safe_path is None:
        self.safe_path = semantic_navigation.msg.SemanticPath()
      if self.alternative_paths is None:
        self.alternative_paths = None
      end = 0
      start = end
      end += 1
      (self.success,) = _get_struct_B().unpack(str[start:end])
      self.success = bool(self.success)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.message = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.message = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.safe_path.header.seq, _x.safe_path.header.stamp.secs, _x.safe_path.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.safe_path.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.safe_path.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.safe_path.path.header.seq, _x.safe_path.path.header.stamp.secs, _x.safe_path.path.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.safe_path.path.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.safe_path.path.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.safe_path.path.poses = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.PoseStamped()
        _v16 = val1.header
        start = end
        end += 4
        (_v16.seq,) = _get_struct_I().unpack(str[start:end])
        _v17 = _v16.stamp
        _x = _v17
        start = end
        end += 8
        (_x.secs, _x.nsecs,) = _get_struct_2I().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          _v16.frame_id = str[start:end].decode('utf-8', 'rosmsg')
        else:
          _v16.frame_id = str[start:end]
        _v18 = val1.pose
        _v19 = _v18.position
        _x = _v19
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        _v20 = _v18.orientation
        _x = _v20
        start = end
        end += 32
        (_x.x, _x.y, _x.z, _x.w,) = _get_struct_4d().unpack(str[start:end])
        self.safe_path.path.poses.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.safe_path.semantic_labels = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.safe_path.semantic_labels.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.safe_path.semantic_costs = s.unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.safe_path.safety_scores = s.unpack(str[start:end])
      _x = self
      start = end
      end += 13
      (_x.safe_path.total_distance, _x.safe_path.total_cost, _x.safe_path.safety_rating, _x.safe_path.contains_fire_risk,) = _get_struct_3fB().unpack(str[start:end])
      self.safe_path.contains_fire_risk = bool(self.safe_path.contains_fire_risk)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.safe_path.emergency_exits = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.Point()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.safe_path.emergency_exits.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.safe_path.risk_level = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.safe_path.risk_level = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.alternative_paths = []
      for i in range(0, length):
        val1 = semantic_navigation.msg.SemanticPath()
        _v21 = val1.header
        start = end
        end += 4
        (_v21.seq,) = _get_struct_I().unpack(str[start:end])
        _v22 = _v21.stamp
        _x = _v22
        start = end
        end += 8
        (_x.secs, _x.nsecs,) = _get_struct_2I().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          _v21.frame_id = str[start:end].decode('utf-8', 'rosmsg')
        else:
          _v21.frame_id = str[start:end]
        _v23 = val1.path
        _v24 = _v23.header
        start = end
        end += 4
        (_v24.seq,) = _get_struct_I().unpack(str[start:end])
        _v25 = _v24.stamp
        _x = _v25
        start = end
        end += 8
        (_x.secs, _x.nsecs,) = _get_struct_2I().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          _v24.frame_id = str[start:end].decode('utf-8', 'rosmsg')
        else:
          _v24.frame_id = str[start:end]
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        _v23.poses = []
        for i in range(0, length):
          val3 = geometry_msgs.msg.PoseStamped()
          _v26 = val3.header
          start = end
          end += 4
          (_v26.seq,) = _get_struct_I().unpack(str[start:end])
          _v27 = _v26.stamp
          _x = _v27
          start = end
          end += 8
          (_x.secs, _x.nsecs,) = _get_struct_2I().unpack(str[start:end])
          start = end
          end += 4
          (length,) = _struct_I.unpack(str[start:end])
          start = end
          end += length
          if python3:
            _v26.frame_id = str[start:end].decode('utf-8', 'rosmsg')
          else:
            _v26.frame_id = str[start:end]
          _v28 = val3.pose
          _v29 = _v28.position
          _x = _v29
          start = end
          end += 24
          (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
          _v30 = _v28.orientation
          _x = _v30
          start = end
          end += 32
          (_x.x, _x.y, _x.z, _x.w,) = _get_struct_4d().unpack(str[start:end])
          _v23.poses.append(val3)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.semantic_labels = []
        for i in range(0, length):
          start = end
          end += 4
          (length,) = _struct_I.unpack(str[start:end])
          start = end
          end += length
          if python3:
            val2 = str[start:end].decode('utf-8', 'rosmsg')
          else:
            val2 = str[start:end]
          val1.semantic_labels.append(val2)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        pattern = '<%sf'%length
        start = end
        s = struct.Struct(pattern)
        end += s.size
        val1.semantic_costs = s.unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        pattern = '<%sf'%length
        start = end
        s = struct.Struct(pattern)
        end += s.size
        val1.safety_scores = s.unpack(str[start:end])
        _x = val1
        start = end
        end += 13
        (_x.total_distance, _x.total_cost, _x.safety_rating, _x.contains_fire_risk,) = _get_struct_3fB().unpack(str[start:end])
        val1.contains_fire_risk = bool(val1.contains_fire_risk)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.emergency_exits = []
        for i in range(0, length):
          val2 = geometry_msgs.msg.Point()
          _x = val2
          start = end
          end += 24
          (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
          val1.emergency_exits.append(val2)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1.risk_level = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1.risk_level = str[start:end]
        self.alternative_paths.append(val1)
      start = end
      end += 4
      (self.safety_score,) = _get_struct_f().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.warnings = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.warnings.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self.success
      buff.write(_get_struct_B().pack(_x))
      _x = self.message
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3I().pack(_x.safe_path.header.seq, _x.safe_path.header.stamp.secs, _x.safe_path.header.stamp.nsecs))
      _x = self.safe_path.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3I().pack(_x.safe_path.path.header.seq, _x.safe_path.path.header.stamp.secs, _x.safe_path.path.header.stamp.nsecs))
      _x = self.safe_path.path.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.safe_path.path.poses)
      buff.write(_struct_I.pack(length))
      for val1 in self.safe_path.path.poses:
        _v31 = val1.header
        _x = _v31.seq
        buff.write(_get_struct_I().pack(_x))
        _v32 = _v31.stamp
        _x = _v32
        buff.write(_get_struct_2I().pack(_x.secs, _x.nsecs))
        _x = _v31.frame_id
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
        _v33 = val1.pose
        _v34 = _v33.position
        _x = _v34
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        _v35 = _v33.orientation
        _x = _v35
        buff.write(_get_struct_4d().pack(_x.x, _x.y, _x.z, _x.w))
      length = len(self.safe_path.semantic_labels)
      buff.write(_struct_I.pack(length))
      for val1 in self.safe_path.semantic_labels:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      length = len(self.safe_path.semantic_costs)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(self.safe_path.semantic_costs.tostring())
      length = len(self.safe_path.safety_scores)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(self.safe_path.safety_scores.tostring())
      _x = self
      buff.write(_get_struct_3fB().pack(_x.safe_path.total_distance, _x.safe_path.total_cost, _x.safe_path.safety_rating, _x.safe_path.contains_fire_risk))
      length = len(self.safe_path.emergency_exits)
      buff.write(_struct_I.pack(length))
      for val1 in self.safe_path.emergency_exits:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
      _x = self.safe_path.risk_level
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.alternative_paths)
      buff.write(_struct_I.pack(length))
      for val1 in self.alternative_paths:
        _v36 = val1.header
        _x = _v36.seq
        buff.write(_get_struct_I().pack(_x))
        _v37 = _v36.stamp
        _x = _v37
        buff.write(_get_struct_2I().pack(_x.secs, _x.nsecs))
        _x = _v36.frame_id
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
        _v38 = val1.path
        _v39 = _v38.header
        _x = _v39.seq
        buff.write(_get_struct_I().pack(_x))
        _v40 = _v39.stamp
        _x = _v40
        buff.write(_get_struct_2I().pack(_x.secs, _x.nsecs))
        _x = _v39.frame_id
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
        length = len(_v38.poses)
        buff.write(_struct_I.pack(length))
        for val3 in _v38.poses:
          _v41 = val3.header
          _x = _v41.seq
          buff.write(_get_struct_I().pack(_x))
          _v42 = _v41.stamp
          _x = _v42
          buff.write(_get_struct_2I().pack(_x.secs, _x.nsecs))
          _x = _v41.frame_id
          length = len(_x)
          if python3 or type(_x) == unicode:
            _x = _x.encode('utf-8')
            length = len(_x)
          buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
          _v43 = val3.pose
          _v44 = _v43.position
          _x = _v44
          buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
          _v45 = _v43.orientation
          _x = _v45
          buff.write(_get_struct_4d().pack(_x.x, _x.y, _x.z, _x.w))
        length = len(val1.semantic_labels)
        buff.write(_struct_I.pack(length))
        for val2 in val1.semantic_labels:
          length = len(val2)
          if python3 or type(val2) == unicode:
            val2 = val2.encode('utf-8')
            length = len(val2)
          buff.write(struct.Struct('<I%ss'%length).pack(length, val2))
        length = len(val1.semantic_costs)
        buff.write(_struct_I.pack(length))
        pattern = '<%sf'%length
        buff.write(val1.semantic_costs.tostring())
        length = len(val1.safety_scores)
        buff.write(_struct_I.pack(length))
        pattern = '<%sf'%length
        buff.write(val1.safety_scores.tostring())
        _x = val1
        buff.write(_get_struct_3fB().pack(_x.total_distance, _x.total_cost, _x.safety_rating, _x.contains_fire_risk))
        length = len(val1.emergency_exits)
        buff.write(_struct_I.pack(length))
        for val2 in val1.emergency_exits:
          _x = val2
          buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        _x = val1.risk_level
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.safety_score
      buff.write(_get_struct_f().pack(_x))
      length = len(self.warnings)
      buff.write(_struct_I.pack(length))
      for val1 in self.warnings:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.safe_path is None:
        self.safe_path = semantic_navigation.msg.SemanticPath()
      if self.alternative_paths is None:
        self.alternative_paths = None
      end = 0
      start = end
      end += 1
      (self.success,) = _get_struct_B().unpack(str[start:end])
      self.success = bool(self.success)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.message = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.message = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.safe_path.header.seq, _x.safe_path.header.stamp.secs, _x.safe_path.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.safe_path.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.safe_path.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.safe_path.path.header.seq, _x.safe_path.path.header.stamp.secs, _x.safe_path.path.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.safe_path.path.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.safe_path.path.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.safe_path.path.poses = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.PoseStamped()
        _v46 = val1.header
        start = end
        end += 4
        (_v46.seq,) = _get_struct_I().unpack(str[start:end])
        _v47 = _v46.stamp
        _x = _v47
        start = end
        end += 8
        (_x.secs, _x.nsecs,) = _get_struct_2I().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          _v46.frame_id = str[start:end].decode('utf-8', 'rosmsg')
        else:
          _v46.frame_id = str[start:end]
        _v48 = val1.pose
        _v49 = _v48.position
        _x = _v49
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        _v50 = _v48.orientation
        _x = _v50
        start = end
        end += 32
        (_x.x, _x.y, _x.z, _x.w,) = _get_struct_4d().unpack(str[start:end])
        self.safe_path.path.poses.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.safe_path.semantic_labels = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.safe_path.semantic_labels.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.safe_path.semantic_costs = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.safe_path.safety_scores = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
      _x = self
      start = end
      end += 13
      (_x.safe_path.total_distance, _x.safe_path.total_cost, _x.safe_path.safety_rating, _x.safe_path.contains_fire_risk,) = _get_struct_3fB().unpack(str[start:end])
      self.safe_path.contains_fire_risk = bool(self.safe_path.contains_fire_risk)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.safe_path.emergency_exits = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.Point()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.safe_path.emergency_exits.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.safe_path.risk_level = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.safe_path.risk_level = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.alternative_paths = []
      for i in range(0, length):
        val1 = semantic_navigation.msg.SemanticPath()
        _v51 = val1.header
        start = end
        end += 4
        (_v51.seq,) = _get_struct_I().unpack(str[start:end])
        _v52 = _v51.stamp
        _x = _v52
        start = end
        end += 8
        (_x.secs, _x.nsecs,) = _get_struct_2I().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          _v51.frame_id = str[start:end].decode('utf-8', 'rosmsg')
        else:
          _v51.frame_id = str[start:end]
        _v53 = val1.path
        _v54 = _v53.header
        start = end
        end += 4
        (_v54.seq,) = _get_struct_I().unpack(str[start:end])
        _v55 = _v54.stamp
        _x = _v55
        start = end
        end += 8
        (_x.secs, _x.nsecs,) = _get_struct_2I().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          _v54.frame_id = str[start:end].decode('utf-8', 'rosmsg')
        else:
          _v54.frame_id = str[start:end]
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        _v53.poses = []
        for i in range(0, length):
          val3 = geometry_msgs.msg.PoseStamped()
          _v56 = val3.header
          start = end
          end += 4
          (_v56.seq,) = _get_struct_I().unpack(str[start:end])
          _v57 = _v56.stamp
          _x = _v57
          start = end
          end += 8
          (_x.secs, _x.nsecs,) = _get_struct_2I().unpack(str[start:end])
          start = end
          end += 4
          (length,) = _struct_I.unpack(str[start:end])
          start = end
          end += length
          if python3:
            _v56.frame_id = str[start:end].decode('utf-8', 'rosmsg')
          else:
            _v56.frame_id = str[start:end]
          _v58 = val3.pose
          _v59 = _v58.position
          _x = _v59
          start = end
          end += 24
          (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
          _v60 = _v58.orientation
          _x = _v60
          start = end
          end += 32
          (_x.x, _x.y, _x.z, _x.w,) = _get_struct_4d().unpack(str[start:end])
          _v53.poses.append(val3)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.semantic_labels = []
        for i in range(0, length):
          start = end
          end += 4
          (length,) = _struct_I.unpack(str[start:end])
          start = end
          end += length
          if python3:
            val2 = str[start:end].decode('utf-8', 'rosmsg')
          else:
            val2 = str[start:end]
          val1.semantic_labels.append(val2)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        pattern = '<%sf'%length
        start = end
        s = struct.Struct(pattern)
        end += s.size
        val1.semantic_costs = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        pattern = '<%sf'%length
        start = end
        s = struct.Struct(pattern)
        end += s.size
        val1.safety_scores = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
        _x = val1
        start = end
        end += 13
        (_x.total_distance, _x.total_cost, _x.safety_rating, _x.contains_fire_risk,) = _get_struct_3fB().unpack(str[start:end])
        val1.contains_fire_risk = bool(val1.contains_fire_risk)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        val1.emergency_exits = []
        for i in range(0, length):
          val2 = geometry_msgs.msg.Point()
          _x = val2
          start = end
          end += 24
          (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
          val1.emergency_exits.append(val2)
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1.risk_level = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1.risk_level = str[start:end]
        self.alternative_paths.append(val1)
      start = end
      end += 4
      (self.safety_score,) = _get_struct_f().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.warnings = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.warnings.append(val1)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_2I = None
def _get_struct_2I():
    global _struct_2I
    if _struct_2I is None:
        _struct_2I = struct.Struct("<2I")
    return _struct_2I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_3d = None
def _get_struct_3d():
    global _struct_3d
    if _struct_3d is None:
        _struct_3d = struct.Struct("<3d")
    return _struct_3d
_struct_3fB = None
def _get_struct_3fB():
    global _struct_3fB
    if _struct_3fB is None:
        _struct_3fB = struct.Struct("<3fB")
    return _struct_3fB
_struct_4d = None
def _get_struct_4d():
    global _struct_4d
    if _struct_4d is None:
        _struct_4d = struct.Struct("<4d")
    return _struct_4d
_struct_B = None
def _get_struct_B():
    global _struct_B
    if _struct_B is None:
        _struct_B = struct.Struct("<B")
    return _struct_B
_struct_f = None
def _get_struct_f():
    global _struct_f
    if _struct_f is None:
        _struct_f = struct.Struct("<f")
    return _struct_f
class GetSafePath(object):
  _type          = 'semantic_navigation/GetSafePath'
  _md5sum = '677123b52cef0c55e41c66d038e4b3a2'
  _request_class  = GetSafePathRequest
  _response_class = GetSafePathResponse
