# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from semantic_navigation/SetNavigationGoalRequest.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import genpy
import geometry_msgs.msg
import semantic_navigation.msg
import std_msgs.msg

class SetNavigationGoalRequest(genpy.Message):
  _md5sum = "3e127fafc98b335a09570dc426dce88b"
  _type = "semantic_navigation/SetNavigationGoalRequest"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """# 设置导航目标服务
# 请求
SemanticNavigationGoal goal


================================================================================
MSG: semantic_navigation/SemanticNavigationGoal
# 语义导航目标消息
Header header

# 目标位置
geometry_msgs/PoseStamped target_pose

# 导航模式
string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE

# 语义约束
string[] avoid_classes    # 需要避开的语义类别
string[] prefer_classes   # 优先通过的语义类别

# 安全参数
float32 safety_distance   # 安全距离 (米)
float32 fire_avoidance_distance  # 火焰避让距离 (米)

# 优先级设置
uint8 priority           # 导航优先级 (0-10, 10最高)
bool emergency_mode      # 是否为应急模式

# 超时设置
duration timeout         # 导航超时时间

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
"""
  __slots__ = ['goal']
  _slot_types = ['semantic_navigation/SemanticNavigationGoal']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       goal

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(SetNavigationGoalRequest, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.goal is None:
        self.goal = semantic_navigation.msg.SemanticNavigationGoal()
    else:
      self.goal = semantic_navigation.msg.SemanticNavigationGoal()

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.goal.header.seq, _x.goal.header.stamp.secs, _x.goal.header.stamp.nsecs))
      _x = self.goal.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3I().pack(_x.goal.target_pose.header.seq, _x.goal.target_pose.header.stamp.secs, _x.goal.target_pose.header.stamp.nsecs))
      _x = self.goal.target_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d().pack(_x.goal.target_pose.pose.position.x, _x.goal.target_pose.pose.position.y, _x.goal.target_pose.pose.position.z, _x.goal.target_pose.pose.orientation.x, _x.goal.target_pose.pose.orientation.y, _x.goal.target_pose.pose.orientation.z, _x.goal.target_pose.pose.orientation.w))
      _x = self.goal.navigation_mode
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.goal.avoid_classes)
      buff.write(_struct_I.pack(length))
      for val1 in self.goal.avoid_classes:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      length = len(self.goal.prefer_classes)
      buff.write(_struct_I.pack(length))
      for val1 in self.goal.prefer_classes:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      _x = self
      buff.write(_get_struct_2f2B2i().pack(_x.goal.safety_distance, _x.goal.fire_avoidance_distance, _x.goal.priority, _x.goal.emergency_mode, _x.goal.timeout.secs, _x.goal.timeout.nsecs))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.goal is None:
        self.goal = semantic_navigation.msg.SemanticNavigationGoal()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.goal.header.seq, _x.goal.header.stamp.secs, _x.goal.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.goal.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.goal.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.goal.target_pose.header.seq, _x.goal.target_pose.header.stamp.secs, _x.goal.target_pose.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.goal.target_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.goal.target_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 56
      (_x.goal.target_pose.pose.position.x, _x.goal.target_pose.pose.position.y, _x.goal.target_pose.pose.position.z, _x.goal.target_pose.pose.orientation.x, _x.goal.target_pose.pose.orientation.y, _x.goal.target_pose.pose.orientation.z, _x.goal.target_pose.pose.orientation.w,) = _get_struct_7d().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.goal.navigation_mode = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.goal.navigation_mode = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.goal.avoid_classes = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.goal.avoid_classes.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.goal.prefer_classes = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.goal.prefer_classes.append(val1)
      _x = self
      start = end
      end += 18
      (_x.goal.safety_distance, _x.goal.fire_avoidance_distance, _x.goal.priority, _x.goal.emergency_mode, _x.goal.timeout.secs, _x.goal.timeout.nsecs,) = _get_struct_2f2B2i().unpack(str[start:end])
      self.goal.emergency_mode = bool(self.goal.emergency_mode)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.goal.header.seq, _x.goal.header.stamp.secs, _x.goal.header.stamp.nsecs))
      _x = self.goal.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3I().pack(_x.goal.target_pose.header.seq, _x.goal.target_pose.header.stamp.secs, _x.goal.target_pose.header.stamp.nsecs))
      _x = self.goal.target_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d().pack(_x.goal.target_pose.pose.position.x, _x.goal.target_pose.pose.position.y, _x.goal.target_pose.pose.position.z, _x.goal.target_pose.pose.orientation.x, _x.goal.target_pose.pose.orientation.y, _x.goal.target_pose.pose.orientation.z, _x.goal.target_pose.pose.orientation.w))
      _x = self.goal.navigation_mode
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.goal.avoid_classes)
      buff.write(_struct_I.pack(length))
      for val1 in self.goal.avoid_classes:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      length = len(self.goal.prefer_classes)
      buff.write(_struct_I.pack(length))
      for val1 in self.goal.prefer_classes:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      _x = self
      buff.write(_get_struct_2f2B2i().pack(_x.goal.safety_distance, _x.goal.fire_avoidance_distance, _x.goal.priority, _x.goal.emergency_mode, _x.goal.timeout.secs, _x.goal.timeout.nsecs))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.goal is None:
        self.goal = semantic_navigation.msg.SemanticNavigationGoal()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.goal.header.seq, _x.goal.header.stamp.secs, _x.goal.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.goal.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.goal.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.goal.target_pose.header.seq, _x.goal.target_pose.header.stamp.secs, _x.goal.target_pose.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.goal.target_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.goal.target_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 56
      (_x.goal.target_pose.pose.position.x, _x.goal.target_pose.pose.position.y, _x.goal.target_pose.pose.position.z, _x.goal.target_pose.pose.orientation.x, _x.goal.target_pose.pose.orientation.y, _x.goal.target_pose.pose.orientation.z, _x.goal.target_pose.pose.orientation.w,) = _get_struct_7d().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.goal.navigation_mode = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.goal.navigation_mode = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.goal.avoid_classes = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.goal.avoid_classes.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.goal.prefer_classes = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.goal.prefer_classes.append(val1)
      _x = self
      start = end
      end += 18
      (_x.goal.safety_distance, _x.goal.fire_avoidance_distance, _x.goal.priority, _x.goal.emergency_mode, _x.goal.timeout.secs, _x.goal.timeout.nsecs,) = _get_struct_2f2B2i().unpack(str[start:end])
      self.goal.emergency_mode = bool(self.goal.emergency_mode)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_2f2B2i = None
def _get_struct_2f2B2i():
    global _struct_2f2B2i
    if _struct_2f2B2i is None:
        _struct_2f2B2i = struct.Struct("<2f2B2i")
    return _struct_2f2B2i
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_7d = None
def _get_struct_7d():
    global _struct_7d
    if _struct_7d is None:
        _struct_7d = struct.Struct("<7d")
    return _struct_7d
# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from semantic_navigation/SetNavigationGoalResponse.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg
import nav_msgs.msg
import semantic_navigation.msg
import std_msgs.msg

class SetNavigationGoalResponse(genpy.Message):
  _md5sum = "ab557ff716958fd7a5cc224181312313"
  _type = "semantic_navigation/SetNavigationGoalResponse"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """
# 响应
bool success
string message
string goal_id              # 目标ID，用于跟踪
float32 estimated_time      # 预计完成时间
SemanticPath planned_path   # 规划的路径


================================================================================
MSG: semantic_navigation/SemanticPath
# 语义路径消息
Header header

# 路径信息
nav_msgs/Path path

# 语义信息
string[] semantic_labels     # 路径上的语义标签
float32[] semantic_costs     # 对应的语义代价
float32[] safety_scores      # 安全性评分

# 路径属性
float32 total_distance       # 总距离
float32 total_cost          # 总代价
float32 safety_rating       # 安全评级 (0-1)
bool contains_fire_risk     # 是否包含火灾风险

# 应急信息
geometry_msgs/Point[] emergency_exits  # 应急出口位置
string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: nav_msgs/Path
#An array of poses that represents a Path for a robot to follow
Header header
geometry_msgs/PoseStamped[] poses

================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
"""
  __slots__ = ['success','message','goal_id','estimated_time','planned_path']
  _slot_types = ['bool','string','string','float32','semantic_navigation/SemanticPath']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       success,message,goal_id,estimated_time,planned_path

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(SetNavigationGoalResponse, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.success is None:
        self.success = False
      if self.message is None:
        self.message = ''
      if self.goal_id is None:
        self.goal_id = ''
      if self.estimated_time is None:
        self.estimated_time = 0.
      if self.planned_path is None:
        self.planned_path = semantic_navigation.msg.SemanticPath()
    else:
      self.success = False
      self.message = ''
      self.goal_id = ''
      self.estimated_time = 0.
      self.planned_path = semantic_navigation.msg.SemanticPath()

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self.success
      buff.write(_get_struct_B().pack(_x))
      _x = self.message
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.goal_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_f3I().pack(_x.estimated_time, _x.planned_path.header.seq, _x.planned_path.header.stamp.secs, _x.planned_path.header.stamp.nsecs))
      _x = self.planned_path.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3I().pack(_x.planned_path.path.header.seq, _x.planned_path.path.header.stamp.secs, _x.planned_path.path.header.stamp.nsecs))
      _x = self.planned_path.path.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.planned_path.path.poses)
      buff.write(_struct_I.pack(length))
      for val1 in self.planned_path.path.poses:
        _v1 = val1.header
        _x = _v1.seq
        buff.write(_get_struct_I().pack(_x))
        _v2 = _v1.stamp
        _x = _v2
        buff.write(_get_struct_2I().pack(_x.secs, _x.nsecs))
        _x = _v1.frame_id
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
        _v3 = val1.pose
        _v4 = _v3.position
        _x = _v4
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        _v5 = _v3.orientation
        _x = _v5
        buff.write(_get_struct_4d().pack(_x.x, _x.y, _x.z, _x.w))
      length = len(self.planned_path.semantic_labels)
      buff.write(_struct_I.pack(length))
      for val1 in self.planned_path.semantic_labels:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      length = len(self.planned_path.semantic_costs)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(struct.Struct(pattern).pack(*self.planned_path.semantic_costs))
      length = len(self.planned_path.safety_scores)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(struct.Struct(pattern).pack(*self.planned_path.safety_scores))
      _x = self
      buff.write(_get_struct_3fB().pack(_x.planned_path.total_distance, _x.planned_path.total_cost, _x.planned_path.safety_rating, _x.planned_path.contains_fire_risk))
      length = len(self.planned_path.emergency_exits)
      buff.write(_struct_I.pack(length))
      for val1 in self.planned_path.emergency_exits:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
      _x = self.planned_path.risk_level
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.planned_path is None:
        self.planned_path = semantic_navigation.msg.SemanticPath()
      end = 0
      start = end
      end += 1
      (self.success,) = _get_struct_B().unpack(str[start:end])
      self.success = bool(self.success)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.message = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.message = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.goal_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.goal_id = str[start:end]
      _x = self
      start = end
      end += 16
      (_x.estimated_time, _x.planned_path.header.seq, _x.planned_path.header.stamp.secs, _x.planned_path.header.stamp.nsecs,) = _get_struct_f3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.planned_path.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.planned_path.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.planned_path.path.header.seq, _x.planned_path.path.header.stamp.secs, _x.planned_path.path.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.planned_path.path.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.planned_path.path.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.planned_path.path.poses = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.PoseStamped()
        _v6 = val1.header
        start = end
        end += 4
        (_v6.seq,) = _get_struct_I().unpack(str[start:end])
        _v7 = _v6.stamp
        _x = _v7
        start = end
        end += 8
        (_x.secs, _x.nsecs,) = _get_struct_2I().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          _v6.frame_id = str[start:end].decode('utf-8', 'rosmsg')
        else:
          _v6.frame_id = str[start:end]
        _v8 = val1.pose
        _v9 = _v8.position
        _x = _v9
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        _v10 = _v8.orientation
        _x = _v10
        start = end
        end += 32
        (_x.x, _x.y, _x.z, _x.w,) = _get_struct_4d().unpack(str[start:end])
        self.planned_path.path.poses.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.planned_path.semantic_labels = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.planned_path.semantic_labels.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.planned_path.semantic_costs = s.unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.planned_path.safety_scores = s.unpack(str[start:end])
      _x = self
      start = end
      end += 13
      (_x.planned_path.total_distance, _x.planned_path.total_cost, _x.planned_path.safety_rating, _x.planned_path.contains_fire_risk,) = _get_struct_3fB().unpack(str[start:end])
      self.planned_path.contains_fire_risk = bool(self.planned_path.contains_fire_risk)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.planned_path.emergency_exits = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.Point()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.planned_path.emergency_exits.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.planned_path.risk_level = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.planned_path.risk_level = str[start:end]
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self.success
      buff.write(_get_struct_B().pack(_x))
      _x = self.message
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.goal_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_f3I().pack(_x.estimated_time, _x.planned_path.header.seq, _x.planned_path.header.stamp.secs, _x.planned_path.header.stamp.nsecs))
      _x = self.planned_path.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3I().pack(_x.planned_path.path.header.seq, _x.planned_path.path.header.stamp.secs, _x.planned_path.path.header.stamp.nsecs))
      _x = self.planned_path.path.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      length = len(self.planned_path.path.poses)
      buff.write(_struct_I.pack(length))
      for val1 in self.planned_path.path.poses:
        _v11 = val1.header
        _x = _v11.seq
        buff.write(_get_struct_I().pack(_x))
        _v12 = _v11.stamp
        _x = _v12
        buff.write(_get_struct_2I().pack(_x.secs, _x.nsecs))
        _x = _v11.frame_id
        length = len(_x)
        if python3 or type(_x) == unicode:
          _x = _x.encode('utf-8')
          length = len(_x)
        buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
        _v13 = val1.pose
        _v14 = _v13.position
        _x = _v14
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
        _v15 = _v13.orientation
        _x = _v15
        buff.write(_get_struct_4d().pack(_x.x, _x.y, _x.z, _x.w))
      length = len(self.planned_path.semantic_labels)
      buff.write(_struct_I.pack(length))
      for val1 in self.planned_path.semantic_labels:
        length = len(val1)
        if python3 or type(val1) == unicode:
          val1 = val1.encode('utf-8')
          length = len(val1)
        buff.write(struct.Struct('<I%ss'%length).pack(length, val1))
      length = len(self.planned_path.semantic_costs)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(self.planned_path.semantic_costs.tostring())
      length = len(self.planned_path.safety_scores)
      buff.write(_struct_I.pack(length))
      pattern = '<%sf'%length
      buff.write(self.planned_path.safety_scores.tostring())
      _x = self
      buff.write(_get_struct_3fB().pack(_x.planned_path.total_distance, _x.planned_path.total_cost, _x.planned_path.safety_rating, _x.planned_path.contains_fire_risk))
      length = len(self.planned_path.emergency_exits)
      buff.write(_struct_I.pack(length))
      for val1 in self.planned_path.emergency_exits:
        _x = val1
        buff.write(_get_struct_3d().pack(_x.x, _x.y, _x.z))
      _x = self.planned_path.risk_level
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.planned_path is None:
        self.planned_path = semantic_navigation.msg.SemanticPath()
      end = 0
      start = end
      end += 1
      (self.success,) = _get_struct_B().unpack(str[start:end])
      self.success = bool(self.success)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.message = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.message = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.goal_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.goal_id = str[start:end]
      _x = self
      start = end
      end += 16
      (_x.estimated_time, _x.planned_path.header.seq, _x.planned_path.header.stamp.secs, _x.planned_path.header.stamp.nsecs,) = _get_struct_f3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.planned_path.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.planned_path.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.planned_path.path.header.seq, _x.planned_path.path.header.stamp.secs, _x.planned_path.path.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.planned_path.path.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.planned_path.path.header.frame_id = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.planned_path.path.poses = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.PoseStamped()
        _v16 = val1.header
        start = end
        end += 4
        (_v16.seq,) = _get_struct_I().unpack(str[start:end])
        _v17 = _v16.stamp
        _x = _v17
        start = end
        end += 8
        (_x.secs, _x.nsecs,) = _get_struct_2I().unpack(str[start:end])
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          _v16.frame_id = str[start:end].decode('utf-8', 'rosmsg')
        else:
          _v16.frame_id = str[start:end]
        _v18 = val1.pose
        _v19 = _v18.position
        _x = _v19
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        _v20 = _v18.orientation
        _x = _v20
        start = end
        end += 32
        (_x.x, _x.y, _x.z, _x.w,) = _get_struct_4d().unpack(str[start:end])
        self.planned_path.path.poses.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.planned_path.semantic_labels = []
      for i in range(0, length):
        start = end
        end += 4
        (length,) = _struct_I.unpack(str[start:end])
        start = end
        end += length
        if python3:
          val1 = str[start:end].decode('utf-8', 'rosmsg')
        else:
          val1 = str[start:end]
        self.planned_path.semantic_labels.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.planned_path.semantic_costs = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sf'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.planned_path.safety_scores = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=length)
      _x = self
      start = end
      end += 13
      (_x.planned_path.total_distance, _x.planned_path.total_cost, _x.planned_path.safety_rating, _x.planned_path.contains_fire_risk,) = _get_struct_3fB().unpack(str[start:end])
      self.planned_path.contains_fire_risk = bool(self.planned_path.contains_fire_risk)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.planned_path.emergency_exits = []
      for i in range(0, length):
        val1 = geometry_msgs.msg.Point()
        _x = val1
        start = end
        end += 24
        (_x.x, _x.y, _x.z,) = _get_struct_3d().unpack(str[start:end])
        self.planned_path.emergency_exits.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.planned_path.risk_level = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.planned_path.risk_level = str[start:end]
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_2I = None
def _get_struct_2I():
    global _struct_2I
    if _struct_2I is None:
        _struct_2I = struct.Struct("<2I")
    return _struct_2I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_3d = None
def _get_struct_3d():
    global _struct_3d
    if _struct_3d is None:
        _struct_3d = struct.Struct("<3d")
    return _struct_3d
_struct_3fB = None
def _get_struct_3fB():
    global _struct_3fB
    if _struct_3fB is None:
        _struct_3fB = struct.Struct("<3fB")
    return _struct_3fB
_struct_4d = None
def _get_struct_4d():
    global _struct_4d
    if _struct_4d is None:
        _struct_4d = struct.Struct("<4d")
    return _struct_4d
_struct_B = None
def _get_struct_B():
    global _struct_B
    if _struct_B is None:
        _struct_B = struct.Struct("<B")
    return _struct_B
_struct_f3I = None
def _get_struct_f3I():
    global _struct_f3I
    if _struct_f3I is None:
        _struct_f3I = struct.Struct("<f3I")
    return _struct_f3I
class SetNavigationGoal(object):
  _type          = 'semantic_navigation/SetNavigationGoal'
  _md5sum = '9b33341aa6d4bf3615200fa760500108'
  _request_class  = SetNavigationGoalRequest
  _response_class = SetNavigationGoalResponse
