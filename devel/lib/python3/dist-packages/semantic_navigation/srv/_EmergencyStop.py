# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from semantic_navigation/EmergencyStopRequest.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg

class EmergencyStopRequest(genpy.Message):
  _md5sum = "6b8b6a2e505f7fd67061116b01ac9322"
  _type = "semantic_navigation/EmergencyStopRequest"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """# 应急停止服务
# 请求
string reason               # 停止原因
bool immediate              # 是否立即停止
geometry_msgs/Point hazard_location  # 危险位置（可选）


================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z
"""
  __slots__ = ['reason','immediate','hazard_location']
  _slot_types = ['string','bool','geometry_msgs/Point']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       reason,immediate,hazard_location

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(EmergencyStopRequest, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.reason is None:
        self.reason = ''
      if self.immediate is None:
        self.immediate = False
      if self.hazard_location is None:
        self.hazard_location = geometry_msgs.msg.Point()
    else:
      self.reason = ''
      self.immediate = False
      self.hazard_location = geometry_msgs.msg.Point()

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self.reason
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_B3d().pack(_x.immediate, _x.hazard_location.x, _x.hazard_location.y, _x.hazard_location.z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.hazard_location is None:
        self.hazard_location = geometry_msgs.msg.Point()
      end = 0
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.reason = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.reason = str[start:end]
      _x = self
      start = end
      end += 25
      (_x.immediate, _x.hazard_location.x, _x.hazard_location.y, _x.hazard_location.z,) = _get_struct_B3d().unpack(str[start:end])
      self.immediate = bool(self.immediate)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self.reason
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_B3d().pack(_x.immediate, _x.hazard_location.x, _x.hazard_location.y, _x.hazard_location.z))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.hazard_location is None:
        self.hazard_location = geometry_msgs.msg.Point()
      end = 0
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.reason = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.reason = str[start:end]
      _x = self
      start = end
      end += 25
      (_x.immediate, _x.hazard_location.x, _x.hazard_location.y, _x.hazard_location.z,) = _get_struct_B3d().unpack(str[start:end])
      self.immediate = bool(self.immediate)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_B3d = None
def _get_struct_B3d():
    global _struct_B3d
    if _struct_B3d is None:
        _struct_B3d = struct.Struct("<B3d")
    return _struct_B3d
# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from semantic_navigation/EmergencyStopResponse.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import genpy
import geometry_msgs.msg
import std_msgs.msg

class EmergencyStopResponse(genpy.Message):
  _md5sum = "cf2b4c3181dfd9b92e1cc65d57d3a7eb"
  _type = "semantic_navigation/EmergencyStopResponse"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """
# 响应
bool success
string message
time stop_time              # 停止时间
geometry_msgs/PoseStamped final_pose  # 最终位置


================================================================================
MSG: geometry_msgs/PoseStamped
# A Pose with reference coordinate frame and timestamp
Header header
Pose pose

================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: geometry_msgs/Pose
# A representation of pose in free space, composed of position and orientation. 
Point position
Quaternion orientation

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Quaternion
# This represents an orientation in free space in quaternion form.

float64 x
float64 y
float64 z
float64 w
"""
  __slots__ = ['success','message','stop_time','final_pose']
  _slot_types = ['bool','string','time','geometry_msgs/PoseStamped']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       success,message,stop_time,final_pose

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(EmergencyStopResponse, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.success is None:
        self.success = False
      if self.message is None:
        self.message = ''
      if self.stop_time is None:
        self.stop_time = genpy.Time()
      if self.final_pose is None:
        self.final_pose = geometry_msgs.msg.PoseStamped()
    else:
      self.success = False
      self.message = ''
      self.stop_time = genpy.Time()
      self.final_pose = geometry_msgs.msg.PoseStamped()

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self.success
      buff.write(_get_struct_B().pack(_x))
      _x = self.message
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_5I().pack(_x.stop_time.secs, _x.stop_time.nsecs, _x.final_pose.header.seq, _x.final_pose.header.stamp.secs, _x.final_pose.header.stamp.nsecs))
      _x = self.final_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d().pack(_x.final_pose.pose.position.x, _x.final_pose.pose.position.y, _x.final_pose.pose.position.z, _x.final_pose.pose.orientation.x, _x.final_pose.pose.orientation.y, _x.final_pose.pose.orientation.z, _x.final_pose.pose.orientation.w))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.stop_time is None:
        self.stop_time = genpy.Time()
      if self.final_pose is None:
        self.final_pose = geometry_msgs.msg.PoseStamped()
      end = 0
      start = end
      end += 1
      (self.success,) = _get_struct_B().unpack(str[start:end])
      self.success = bool(self.success)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.message = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.message = str[start:end]
      _x = self
      start = end
      end += 20
      (_x.stop_time.secs, _x.stop_time.nsecs, _x.final_pose.header.seq, _x.final_pose.header.stamp.secs, _x.final_pose.header.stamp.nsecs,) = _get_struct_5I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.final_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.final_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 56
      (_x.final_pose.pose.position.x, _x.final_pose.pose.position.y, _x.final_pose.pose.position.z, _x.final_pose.pose.orientation.x, _x.final_pose.pose.orientation.y, _x.final_pose.pose.orientation.z, _x.final_pose.pose.orientation.w,) = _get_struct_7d().unpack(str[start:end])
      self.stop_time.canon()
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self.success
      buff.write(_get_struct_B().pack(_x))
      _x = self.message
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_5I().pack(_x.stop_time.secs, _x.stop_time.nsecs, _x.final_pose.header.seq, _x.final_pose.header.stamp.secs, _x.final_pose.header.stamp.nsecs))
      _x = self.final_pose.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_7d().pack(_x.final_pose.pose.position.x, _x.final_pose.pose.position.y, _x.final_pose.pose.position.z, _x.final_pose.pose.orientation.x, _x.final_pose.pose.orientation.y, _x.final_pose.pose.orientation.z, _x.final_pose.pose.orientation.w))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.stop_time is None:
        self.stop_time = genpy.Time()
      if self.final_pose is None:
        self.final_pose = geometry_msgs.msg.PoseStamped()
      end = 0
      start = end
      end += 1
      (self.success,) = _get_struct_B().unpack(str[start:end])
      self.success = bool(self.success)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.message = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.message = str[start:end]
      _x = self
      start = end
      end += 20
      (_x.stop_time.secs, _x.stop_time.nsecs, _x.final_pose.header.seq, _x.final_pose.header.stamp.secs, _x.final_pose.header.stamp.nsecs,) = _get_struct_5I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.final_pose.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.final_pose.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 56
      (_x.final_pose.pose.position.x, _x.final_pose.pose.position.y, _x.final_pose.pose.position.z, _x.final_pose.pose.orientation.x, _x.final_pose.pose.orientation.y, _x.final_pose.pose.orientation.z, _x.final_pose.pose.orientation.w,) = _get_struct_7d().unpack(str[start:end])
      self.stop_time.canon()
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_5I = None
def _get_struct_5I():
    global _struct_5I
    if _struct_5I is None:
        _struct_5I = struct.Struct("<5I")
    return _struct_5I
_struct_7d = None
def _get_struct_7d():
    global _struct_7d
    if _struct_7d is None:
        _struct_7d = struct.Struct("<7d")
    return _struct_7d
_struct_B = None
def _get_struct_B():
    global _struct_B
    if _struct_B is None:
        _struct_B = struct.Struct("<B")
    return _struct_B
class EmergencyStop(object):
  _type          = 'semantic_navigation/EmergencyStop'
  _md5sum = '13ca1b92fd866cf0141e505652497177'
  _request_class  = EmergencyStopRequest
  _response_class = EmergencyStopResponse
