// Generated by gencpp from file semantic_navigation/EmergencyStop.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYSTOP_H
#define SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYSTOP_H

#include <ros/service_traits.h>


#include <semantic_navigation/EmergencyStopRequest.h>
#include <semantic_navigation/EmergencyStopResponse.h>


namespace semantic_navigation
{

struct EmergencyStop
{

typedef EmergencyStopRequest Request;
typedef EmergencyStopResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct EmergencyStop
} // namespace semantic_navigation


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::semantic_navigation::EmergencyStop > {
  static const char* value()
  {
    return "13ca1b92fd866cf0141e505652497177";
  }

  static const char* value(const ::semantic_navigation::EmergencyStop&) { return value(); }
};

template<>
struct DataType< ::semantic_navigation::EmergencyStop > {
  static const char* value()
  {
    return "semantic_navigation/EmergencyStop";
  }

  static const char* value(const ::semantic_navigation::EmergencyStop&) { return value(); }
};


// service_traits::MD5Sum< ::semantic_navigation::EmergencyStopRequest> should match
// service_traits::MD5Sum< ::semantic_navigation::EmergencyStop >
template<>
struct MD5Sum< ::semantic_navigation::EmergencyStopRequest>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_navigation::EmergencyStop >::value();
  }
  static const char* value(const ::semantic_navigation::EmergencyStopRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_navigation::EmergencyStopRequest> should match
// service_traits::DataType< ::semantic_navigation::EmergencyStop >
template<>
struct DataType< ::semantic_navigation::EmergencyStopRequest>
{
  static const char* value()
  {
    return DataType< ::semantic_navigation::EmergencyStop >::value();
  }
  static const char* value(const ::semantic_navigation::EmergencyStopRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::semantic_navigation::EmergencyStopResponse> should match
// service_traits::MD5Sum< ::semantic_navigation::EmergencyStop >
template<>
struct MD5Sum< ::semantic_navigation::EmergencyStopResponse>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_navigation::EmergencyStop >::value();
  }
  static const char* value(const ::semantic_navigation::EmergencyStopResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_navigation::EmergencyStopResponse> should match
// service_traits::DataType< ::semantic_navigation::EmergencyStop >
template<>
struct DataType< ::semantic_navigation::EmergencyStopResponse>
{
  static const char* value()
  {
    return DataType< ::semantic_navigation::EmergencyStop >::value();
  }
  static const char* value(const ::semantic_navigation::EmergencyStopResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYSTOP_H
