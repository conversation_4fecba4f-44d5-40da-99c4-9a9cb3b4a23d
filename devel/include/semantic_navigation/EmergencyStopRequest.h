// Generated by gencpp from file semantic_navigation/EmergencyStopRequest.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYSTOPREQUEST_H
#define SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYSTOPREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Point.h>

namespace semantic_navigation
{
template <class ContainerAllocator>
struct EmergencyStopRequest_
{
  typedef EmergencyStopRequest_<ContainerAllocator> Type;

  EmergencyStopRequest_()
    : reason()
    , immediate(false)
    , hazard_location()  {
    }
  EmergencyStopRequest_(const ContainerAllocator& _alloc)
    : reason(_alloc)
    , immediate(false)
    , hazard_location(_alloc)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _reason_type;
  _reason_type reason;

   typedef uint8_t _immediate_type;
  _immediate_type immediate;

   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _hazard_location_type;
  _hazard_location_type hazard_location;





  typedef boost::shared_ptr< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> const> ConstPtr;

}; // struct EmergencyStopRequest_

typedef ::semantic_navigation::EmergencyStopRequest_<std::allocator<void> > EmergencyStopRequest;

typedef boost::shared_ptr< ::semantic_navigation::EmergencyStopRequest > EmergencyStopRequestPtr;
typedef boost::shared_ptr< ::semantic_navigation::EmergencyStopRequest const> EmergencyStopRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator1> & lhs, const ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator2> & rhs)
{
  return lhs.reason == rhs.reason &&
    lhs.immediate == rhs.immediate &&
    lhs.hazard_location == rhs.hazard_location;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator1> & lhs, const ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_navigation

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "6b8b6a2e505f7fd67061116b01ac9322";
  }

  static const char* value(const ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x6b8b6a2e505f7fd6ULL;
  static const uint64_t static_value2 = 0x7061116b01ac9322ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_navigation/EmergencyStopRequest";
  }

  static const char* value(const ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 应急停止服务\n"
"# 请求\n"
"string reason               # 停止原因\n"
"bool immediate              # 是否立即停止\n"
"geometry_msgs/Point hazard_location  # 危险位置（可选）\n"
"\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.reason);
      stream.next(m.immediate);
      stream.next(m.hazard_location);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct EmergencyStopRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_navigation::EmergencyStopRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "reason: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.reason);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "immediate: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.immediate);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "hazard_location: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.hazard_location);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYSTOPREQUEST_H
