// Generated by gencpp from file semantic_navigation/NavigationStatus.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_NAVIGATIONSTATUS_H
#define SEMANTIC_NAVIGATION_MESSAGE_NAVIGATIONSTATUS_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/PoseStamped.h>

namespace semantic_navigation
{
template <class ContainerAllocator>
struct NavigationStatus_
{
  typedef NavigationStatus_<ContainerAllocator> Type;

  NavigationStatus_()
    : header()
    , state()
    , mode()
    , current_pose()
    , target_pose()
    , progress(0.0)
    , distance_remaining(0.0)
    , time_remaining()
    , fire_detected(false)
    , emergency_active(false)
    , detected_hazards()
    , current_speed(0.0)
    , path_deviation(0.0)  {
    }
  NavigationStatus_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , state(_alloc)
    , mode(_alloc)
    , current_pose(_alloc)
    , target_pose(_alloc)
    , progress(0.0)
    , distance_remaining(0.0)
    , time_remaining()
    , fire_detected(false)
    , emergency_active(false)
    , detected_hazards(_alloc)
    , current_speed(0.0)
    , path_deviation(0.0)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _state_type;
  _state_type state;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _mode_type;
  _mode_type mode;

   typedef  ::geometry_msgs::PoseStamped_<ContainerAllocator>  _current_pose_type;
  _current_pose_type current_pose;

   typedef  ::geometry_msgs::PoseStamped_<ContainerAllocator>  _target_pose_type;
  _target_pose_type target_pose;

   typedef float _progress_type;
  _progress_type progress;

   typedef float _distance_remaining_type;
  _distance_remaining_type distance_remaining;

   typedef ros::Duration _time_remaining_type;
  _time_remaining_type time_remaining;

   typedef uint8_t _fire_detected_type;
  _fire_detected_type fire_detected;

   typedef uint8_t _emergency_active_type;
  _emergency_active_type emergency_active;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _detected_hazards_type;
  _detected_hazards_type detected_hazards;

   typedef float _current_speed_type;
  _current_speed_type current_speed;

   typedef float _path_deviation_type;
  _path_deviation_type path_deviation;





  typedef boost::shared_ptr< ::semantic_navigation::NavigationStatus_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_navigation::NavigationStatus_<ContainerAllocator> const> ConstPtr;

}; // struct NavigationStatus_

typedef ::semantic_navigation::NavigationStatus_<std::allocator<void> > NavigationStatus;

typedef boost::shared_ptr< ::semantic_navigation::NavigationStatus > NavigationStatusPtr;
typedef boost::shared_ptr< ::semantic_navigation::NavigationStatus const> NavigationStatusConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_navigation::NavigationStatus_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_navigation::NavigationStatus_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_navigation::NavigationStatus_<ContainerAllocator1> & lhs, const ::semantic_navigation::NavigationStatus_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.state == rhs.state &&
    lhs.mode == rhs.mode &&
    lhs.current_pose == rhs.current_pose &&
    lhs.target_pose == rhs.target_pose &&
    lhs.progress == rhs.progress &&
    lhs.distance_remaining == rhs.distance_remaining &&
    lhs.time_remaining == rhs.time_remaining &&
    lhs.fire_detected == rhs.fire_detected &&
    lhs.emergency_active == rhs.emergency_active &&
    lhs.detected_hazards == rhs.detected_hazards &&
    lhs.current_speed == rhs.current_speed &&
    lhs.path_deviation == rhs.path_deviation;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_navigation::NavigationStatus_<ContainerAllocator1> & lhs, const ::semantic_navigation::NavigationStatus_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_navigation

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::NavigationStatus_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::NavigationStatus_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::NavigationStatus_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::NavigationStatus_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::NavigationStatus_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::NavigationStatus_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_navigation::NavigationStatus_<ContainerAllocator> >
{
  static const char* value()
  {
    return "cdbdd9a0f2482e0f81f898630e3a3988";
  }

  static const char* value(const ::semantic_navigation::NavigationStatus_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xcdbdd9a0f2482e0fULL;
  static const uint64_t static_value2 = 0x81f898630e3a3988ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_navigation::NavigationStatus_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_navigation/NavigationStatus";
  }

  static const char* value(const ::semantic_navigation::NavigationStatus_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_navigation::NavigationStatus_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 导航状态消息\n"
"Header header\n"
"\n"
"# 导航状态\n"
"string state                # IDLE, PLANNING, NAVIGATING, EMERGENCY, STOPPED, FAILED\n"
"string mode                 # NORMAL, EMERGENCY, FIRE_ESCAPE\n"
"\n"
"# 当前位置和目标\n"
"geometry_msgs/PoseStamped current_pose\n"
"geometry_msgs/PoseStamped target_pose\n"
"\n"
"# 进度信息\n"
"float32 progress            # 完成进度 (0-1)\n"
"float32 distance_remaining  # 剩余距离\n"
"duration time_remaining     # 预计剩余时间\n"
"\n"
"# 安全状态\n"
"bool fire_detected          # 是否检测到火焰\n"
"bool emergency_active       # 是否处于应急状态\n"
"string[] detected_hazards   # 检测到的危险\n"
"\n"
"# 性能指标\n"
"float32 current_speed       # 当前速度\n"
"float32 path_deviation      # 路径偏差\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/PoseStamped\n"
"# A Pose with reference coordinate frame and timestamp\n"
"Header header\n"
"Pose pose\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_navigation::NavigationStatus_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_navigation::NavigationStatus_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.state);
      stream.next(m.mode);
      stream.next(m.current_pose);
      stream.next(m.target_pose);
      stream.next(m.progress);
      stream.next(m.distance_remaining);
      stream.next(m.time_remaining);
      stream.next(m.fire_detected);
      stream.next(m.emergency_active);
      stream.next(m.detected_hazards);
      stream.next(m.current_speed);
      stream.next(m.path_deviation);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct NavigationStatus_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_navigation::NavigationStatus_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_navigation::NavigationStatus_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "state: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.state);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "mode: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.mode);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "current_pose: ";
    Printer< ::geometry_msgs::PoseStamped_<ContainerAllocator> >::stream(s, indent + "  ", v.current_pose);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "target_pose: ";
    Printer< ::geometry_msgs::PoseStamped_<ContainerAllocator> >::stream(s, indent + "  ", v.target_pose);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "progress: ";
    Printer<float>::stream(s, indent + "  ", v.progress);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "distance_remaining: ";
    Printer<float>::stream(s, indent + "  ", v.distance_remaining);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "time_remaining: ";
    Printer<ros::Duration>::stream(s, indent + "  ", v.time_remaining);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "fire_detected: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.fire_detected);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "emergency_active: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.emergency_active);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "detected_hazards: ";
    if (v.detected_hazards.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.detected_hazards.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.detected_hazards[i]);
    }
    if (v.detected_hazards.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "current_speed: ";
    Printer<float>::stream(s, indent + "  ", v.current_speed);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "path_deviation: ";
    Printer<float>::stream(s, indent + "  ", v.path_deviation);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_NAVIGATIONSTATUS_H
