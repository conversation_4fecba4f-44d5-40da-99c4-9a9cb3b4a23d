// Generated by gencpp from file semantic_navigation/EmergencyStopResponse.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYSTOPRESPONSE_H
#define SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYSTOPRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/PoseStamped.h>

namespace semantic_navigation
{
template <class ContainerAllocator>
struct EmergencyStopResponse_
{
  typedef EmergencyStopResponse_<ContainerAllocator> Type;

  EmergencyStopResponse_()
    : success(false)
    , message()
    , stop_time()
    , final_pose()  {
    }
  EmergencyStopResponse_(const ContainerAllocator& _alloc)
    : success(false)
    , message(_alloc)
    , stop_time()
    , final_pose(_alloc)  {
  (void)_alloc;
    }



   typedef uint8_t _success_type;
  _success_type success;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _message_type;
  _message_type message;

   typedef ros::Time _stop_time_type;
  _stop_time_type stop_time;

   typedef  ::geometry_msgs::PoseStamped_<ContainerAllocator>  _final_pose_type;
  _final_pose_type final_pose;





  typedef boost::shared_ptr< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> const> ConstPtr;

}; // struct EmergencyStopResponse_

typedef ::semantic_navigation::EmergencyStopResponse_<std::allocator<void> > EmergencyStopResponse;

typedef boost::shared_ptr< ::semantic_navigation::EmergencyStopResponse > EmergencyStopResponsePtr;
typedef boost::shared_ptr< ::semantic_navigation::EmergencyStopResponse const> EmergencyStopResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator1> & lhs, const ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator2> & rhs)
{
  return lhs.success == rhs.success &&
    lhs.message == rhs.message &&
    lhs.stop_time == rhs.stop_time &&
    lhs.final_pose == rhs.final_pose;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator1> & lhs, const ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_navigation

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "cf2b4c3181dfd9b92e1cc65d57d3a7eb";
  }

  static const char* value(const ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xcf2b4c3181dfd9b9ULL;
  static const uint64_t static_value2 = 0x2e1cc65d57d3a7ebULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_navigation/EmergencyStopResponse";
  }

  static const char* value(const ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
"# 响应\n"
"bool success\n"
"string message\n"
"time stop_time              # 停止时间\n"
"geometry_msgs/PoseStamped final_pose  # 最终位置\n"
"\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/PoseStamped\n"
"# A Pose with reference coordinate frame and timestamp\n"
"Header header\n"
"Pose pose\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.success);
      stream.next(m.message);
      stream.next(m.stop_time);
      stream.next(m.final_pose);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct EmergencyStopResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_navigation::EmergencyStopResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "message: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.message);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "stop_time: ";
    Printer<ros::Time>::stream(s, indent + "  ", v.stop_time);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "final_pose: ";
    Printer< ::geometry_msgs::PoseStamped_<ContainerAllocator> >::stream(s, indent + "  ", v.final_pose);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYSTOPRESPONSE_H
