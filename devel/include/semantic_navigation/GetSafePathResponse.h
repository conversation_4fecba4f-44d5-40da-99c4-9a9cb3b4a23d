// Generated by gencpp from file semantic_navigation/GetSafePathResponse.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_GETSAFEPATHRESPONSE_H
#define SEMANTIC_NAVIGATION_MESSAGE_GETSAFEPATHRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <semantic_navigation/SemanticPath.h>
#include <semantic_navigation/SemanticPath.h>

namespace semantic_navigation
{
template <class ContainerAllocator>
struct GetSafePathResponse_
{
  typedef GetSafePathResponse_<ContainerAllocator> Type;

  GetSafePathResponse_()
    : success(false)
    , message()
    , safe_path()
    , alternative_paths()
    , safety_score(0.0)
    , warnings()  {
    }
  GetSafePathResponse_(const ContainerAllocator& _alloc)
    : success(false)
    , message(_alloc)
    , safe_path(_alloc)
    , alternative_paths(_alloc)
    , safety_score(0.0)
    , warnings(_alloc)  {
  (void)_alloc;
    }



   typedef uint8_t _success_type;
  _success_type success;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _message_type;
  _message_type message;

   typedef  ::semantic_navigation::SemanticPath_<ContainerAllocator>  _safe_path_type;
  _safe_path_type safe_path;

   typedef std::vector< ::semantic_navigation::SemanticPath_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::semantic_navigation::SemanticPath_<ContainerAllocator> >> _alternative_paths_type;
  _alternative_paths_type alternative_paths;

   typedef float _safety_score_type;
  _safety_score_type safety_score;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _warnings_type;
  _warnings_type warnings;





  typedef boost::shared_ptr< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> const> ConstPtr;

}; // struct GetSafePathResponse_

typedef ::semantic_navigation::GetSafePathResponse_<std::allocator<void> > GetSafePathResponse;

typedef boost::shared_ptr< ::semantic_navigation::GetSafePathResponse > GetSafePathResponsePtr;
typedef boost::shared_ptr< ::semantic_navigation::GetSafePathResponse const> GetSafePathResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_navigation::GetSafePathResponse_<ContainerAllocator1> & lhs, const ::semantic_navigation::GetSafePathResponse_<ContainerAllocator2> & rhs)
{
  return lhs.success == rhs.success &&
    lhs.message == rhs.message &&
    lhs.safe_path == rhs.safe_path &&
    lhs.alternative_paths == rhs.alternative_paths &&
    lhs.safety_score == rhs.safety_score &&
    lhs.warnings == rhs.warnings;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_navigation::GetSafePathResponse_<ContainerAllocator1> & lhs, const ::semantic_navigation::GetSafePathResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_navigation

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "2fe71afaae051d5769cd4b1deee26420";
  }

  static const char* value(const ::semantic_navigation::GetSafePathResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x2fe71afaae051d57ULL;
  static const uint64_t static_value2 = 0x69cd4b1deee26420ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_navigation/GetSafePathResponse";
  }

  static const char* value(const ::semantic_navigation::GetSafePathResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
"# 响应\n"
"bool success\n"
"string message\n"
"SemanticPath safe_path      # 安全路径\n"
"SemanticPath[] alternative_paths  # 备选路径\n"
"float32 safety_score        # 安全评分\n"
"string[] warnings           # 警告信息\n"
"\n"
"\n"
"================================================================================\n"
"MSG: semantic_navigation/SemanticPath\n"
"# 语义路径消息\n"
"Header header\n"
"\n"
"# 路径信息\n"
"nav_msgs/Path path\n"
"\n"
"# 语义信息\n"
"string[] semantic_labels     # 路径上的语义标签\n"
"float32[] semantic_costs     # 对应的语义代价\n"
"float32[] safety_scores      # 安全性评分\n"
"\n"
"# 路径属性\n"
"float32 total_distance       # 总距离\n"
"float32 total_cost          # 总代价\n"
"float32 safety_rating       # 安全评级 (0-1)\n"
"bool contains_fire_risk     # 是否包含火灾风险\n"
"\n"
"# 应急信息\n"
"geometry_msgs/Point[] emergency_exits  # 应急出口位置\n"
"string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: nav_msgs/Path\n"
"#An array of poses that represents a Path for a robot to follow\n"
"Header header\n"
"geometry_msgs/PoseStamped[] poses\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/PoseStamped\n"
"# A Pose with reference coordinate frame and timestamp\n"
"Header header\n"
"Pose pose\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_navigation::GetSafePathResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.success);
      stream.next(m.message);
      stream.next(m.safe_path);
      stream.next(m.alternative_paths);
      stream.next(m.safety_score);
      stream.next(m.warnings);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetSafePathResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_navigation::GetSafePathResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_navigation::GetSafePathResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "message: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.message);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "safe_path: ";
    Printer< ::semantic_navigation::SemanticPath_<ContainerAllocator> >::stream(s, indent + "  ", v.safe_path);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "alternative_paths: ";
    if (v.alternative_paths.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.alternative_paths.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::semantic_navigation::SemanticPath_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.alternative_paths[i]);
    }
    if (v.alternative_paths.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "safety_score: ";
    Printer<float>::stream(s, indent + "  ", v.safety_score);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "warnings: ";
    if (v.warnings.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.warnings.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.warnings[i]);
    }
    if (v.warnings.empty() || true)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_GETSAFEPATHRESPONSE_H
