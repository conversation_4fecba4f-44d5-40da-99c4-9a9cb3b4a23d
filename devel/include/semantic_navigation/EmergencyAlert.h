// Generated by gencpp from file semantic_navigation/EmergencyAlert.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYALERT_H
#define SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYALERT_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/Point.h>

namespace semantic_navigation
{
template <class ContainerAllocator>
struct EmergencyAlert_
{
  typedef EmergencyAlert_<ContainerAllocator> Type;

  EmergencyAlert_()
    : header()
    , alert_type()
    , severity()
    , location()
    , affected_radius(0.0)
    , description()
    , recommended_action()
    , detection_time()
    , estimated_duration()
    , confidence(0.0)
    , related_objects()  {
    }
  EmergencyAlert_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , alert_type(_alloc)
    , severity(_alloc)
    , location(_alloc)
    , affected_radius(0.0)
    , description(_alloc)
    , recommended_action(_alloc)
    , detection_time()
    , estimated_duration()
    , confidence(0.0)
    , related_objects(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _alert_type_type;
  _alert_type_type alert_type;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _severity_type;
  _severity_type severity;

   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _location_type;
  _location_type location;

   typedef float _affected_radius_type;
  _affected_radius_type affected_radius;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _description_type;
  _description_type description;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _recommended_action_type;
  _recommended_action_type recommended_action;

   typedef ros::Time _detection_time_type;
  _detection_time_type detection_time;

   typedef ros::Duration _estimated_duration_type;
  _estimated_duration_type estimated_duration;

   typedef float _confidence_type;
  _confidence_type confidence;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _related_objects_type;
  _related_objects_type related_objects;





  typedef boost::shared_ptr< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> const> ConstPtr;

}; // struct EmergencyAlert_

typedef ::semantic_navigation::EmergencyAlert_<std::allocator<void> > EmergencyAlert;

typedef boost::shared_ptr< ::semantic_navigation::EmergencyAlert > EmergencyAlertPtr;
typedef boost::shared_ptr< ::semantic_navigation::EmergencyAlert const> EmergencyAlertConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_navigation::EmergencyAlert_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_navigation::EmergencyAlert_<ContainerAllocator1> & lhs, const ::semantic_navigation::EmergencyAlert_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.alert_type == rhs.alert_type &&
    lhs.severity == rhs.severity &&
    lhs.location == rhs.location &&
    lhs.affected_radius == rhs.affected_radius &&
    lhs.description == rhs.description &&
    lhs.recommended_action == rhs.recommended_action &&
    lhs.detection_time == rhs.detection_time &&
    lhs.estimated_duration == rhs.estimated_duration &&
    lhs.confidence == rhs.confidence &&
    lhs.related_objects == rhs.related_objects;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_navigation::EmergencyAlert_<ContainerAllocator1> & lhs, const ::semantic_navigation::EmergencyAlert_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_navigation

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> >
{
  static const char* value()
  {
    return "a8796edaa18ec76393edd2510a2b4bdd";
  }

  static const char* value(const ::semantic_navigation::EmergencyAlert_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xa8796edaa18ec763ULL;
  static const uint64_t static_value2 = 0x93edd2510a2b4bddULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_navigation/EmergencyAlert";
  }

  static const char* value(const ::semantic_navigation::EmergencyAlert_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 应急警报消息\n"
"Header header\n"
"\n"
"# 警报类型\n"
"string alert_type           # FIRE, SMOKE, OBSTACLE, SYSTEM_FAILURE\n"
"\n"
"# 警报等级\n"
"string severity             # LOW, MEDIUM, HIGH, CRITICAL\n"
"\n"
"# 位置信息\n"
"geometry_msgs/Point location\n"
"float32 affected_radius     # 影响半径 (米)\n"
"\n"
"# 描述信息\n"
"string description          # 警报描述\n"
"string recommended_action   # 建议行动\n"
"\n"
"# 时间信息\n"
"time detection_time         # 检测时间\n"
"duration estimated_duration # 预计持续时间\n"
"\n"
"# 相关数据\n"
"float32 confidence          # 置信度\n"
"string[] related_objects    # 相关物体\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::semantic_navigation::EmergencyAlert_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.alert_type);
      stream.next(m.severity);
      stream.next(m.location);
      stream.next(m.affected_radius);
      stream.next(m.description);
      stream.next(m.recommended_action);
      stream.next(m.detection_time);
      stream.next(m.estimated_duration);
      stream.next(m.confidence);
      stream.next(m.related_objects);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct EmergencyAlert_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_navigation::EmergencyAlert_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_navigation::EmergencyAlert_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "alert_type: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.alert_type);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "severity: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.severity);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "location: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.location);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "affected_radius: ";
    Printer<float>::stream(s, indent + "  ", v.affected_radius);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "description: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.description);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "recommended_action: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.recommended_action);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "detection_time: ";
    Printer<ros::Time>::stream(s, indent + "  ", v.detection_time);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "estimated_duration: ";
    Printer<ros::Duration>::stream(s, indent + "  ", v.estimated_duration);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence: ";
    Printer<float>::stream(s, indent + "  ", v.confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "related_objects: ";
    if (v.related_objects.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.related_objects.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.related_objects[i]);
    }
    if (v.related_objects.empty() || true)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_EMERGENCYALERT_H
