// Generated by gencpp from file semantic_navigation/GetSafePath.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_GETSAFEPATH_H
#define SEMANTIC_NAVIGATION_MESSAGE_GETSAFEPATH_H

#include <ros/service_traits.h>


#include <semantic_navigation/GetSafePathRequest.h>
#include <semantic_navigation/GetSafePathResponse.h>


namespace semantic_navigation
{

struct GetSafePath
{

typedef GetSafePathRequest Request;
typedef GetSafePathResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GetSafePath
} // namespace semantic_navigation


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::semantic_navigation::GetSafePath > {
  static const char* value()
  {
    return "677123b52cef0c55e41c66d038e4b3a2";
  }

  static const char* value(const ::semantic_navigation::GetSafePath&) { return value(); }
};

template<>
struct DataType< ::semantic_navigation::GetSafePath > {
  static const char* value()
  {
    return "semantic_navigation/GetSafePath";
  }

  static const char* value(const ::semantic_navigation::GetSafePath&) { return value(); }
};


// service_traits::MD5Sum< ::semantic_navigation::GetSafePathRequest> should match
// service_traits::MD5Sum< ::semantic_navigation::GetSafePath >
template<>
struct MD5Sum< ::semantic_navigation::GetSafePathRequest>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_navigation::GetSafePath >::value();
  }
  static const char* value(const ::semantic_navigation::GetSafePathRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_navigation::GetSafePathRequest> should match
// service_traits::DataType< ::semantic_navigation::GetSafePath >
template<>
struct DataType< ::semantic_navigation::GetSafePathRequest>
{
  static const char* value()
  {
    return DataType< ::semantic_navigation::GetSafePath >::value();
  }
  static const char* value(const ::semantic_navigation::GetSafePathRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::semantic_navigation::GetSafePathResponse> should match
// service_traits::MD5Sum< ::semantic_navigation::GetSafePath >
template<>
struct MD5Sum< ::semantic_navigation::GetSafePathResponse>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_navigation::GetSafePath >::value();
  }
  static const char* value(const ::semantic_navigation::GetSafePathResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_navigation::GetSafePathResponse> should match
// service_traits::DataType< ::semantic_navigation::GetSafePath >
template<>
struct DataType< ::semantic_navigation::GetSafePathResponse>
{
  static const char* value()
  {
    return DataType< ::semantic_navigation::GetSafePath >::value();
  }
  static const char* value(const ::semantic_navigation::GetSafePathResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_GETSAFEPATH_H
