// Generated by gencpp from file semantic_navigation/SetNavigationGoalRequest.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_SETNAVIGATIONGOALREQUEST_H
#define SEMANTIC_NAVIGATION_MESSAGE_SETNAVIGATIONGOALREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <semantic_navigation/SemanticNavigationGoal.h>

namespace semantic_navigation
{
template <class ContainerAllocator>
struct SetNavigationGoalRequest_
{
  typedef SetNavigationGoalRequest_<ContainerAllocator> Type;

  SetNavigationGoalRequest_()
    : goal()  {
    }
  SetNavigationGoalRequest_(const ContainerAllocator& _alloc)
    : goal(_alloc)  {
  (void)_alloc;
    }



   typedef  ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator>  _goal_type;
  _goal_type goal;





  typedef boost::shared_ptr< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> const> ConstPtr;

}; // struct SetNavigationGoalRequest_

typedef ::semantic_navigation::SetNavigationGoalRequest_<std::allocator<void> > SetNavigationGoalRequest;

typedef boost::shared_ptr< ::semantic_navigation::SetNavigationGoalRequest > SetNavigationGoalRequestPtr;
typedef boost::shared_ptr< ::semantic_navigation::SetNavigationGoalRequest const> SetNavigationGoalRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator1> & lhs, const ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator2> & rhs)
{
  return lhs.goal == rhs.goal;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator1> & lhs, const ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_navigation

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "3e127fafc98b335a09570dc426dce88b";
  }

  static const char* value(const ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x3e127fafc98b335aULL;
  static const uint64_t static_value2 = 0x09570dc426dce88bULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_navigation/SetNavigationGoalRequest";
  }

  static const char* value(const ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 设置导航目标服务\n"
"# 请求\n"
"SemanticNavigationGoal goal\n"
"\n"
"\n"
"================================================================================\n"
"MSG: semantic_navigation/SemanticNavigationGoal\n"
"# 语义导航目标消息\n"
"Header header\n"
"\n"
"# 目标位置\n"
"geometry_msgs/PoseStamped target_pose\n"
"\n"
"# 导航模式\n"
"string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE\n"
"\n"
"# 语义约束\n"
"string[] avoid_classes    # 需要避开的语义类别\n"
"string[] prefer_classes   # 优先通过的语义类别\n"
"\n"
"# 安全参数\n"
"float32 safety_distance   # 安全距离 (米)\n"
"float32 fire_avoidance_distance  # 火焰避让距离 (米)\n"
"\n"
"# 优先级设置\n"
"uint8 priority           # 导航优先级 (0-10, 10最高)\n"
"bool emergency_mode      # 是否为应急模式\n"
"\n"
"# 超时设置\n"
"duration timeout         # 导航超时时间\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/PoseStamped\n"
"# A Pose with reference coordinate frame and timestamp\n"
"Header header\n"
"Pose pose\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.goal);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SetNavigationGoalRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_navigation::SetNavigationGoalRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "goal: ";
    Printer< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> >::stream(s, indent + "  ", v.goal);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_SETNAVIGATIONGOALREQUEST_H
