// Generated by gencpp from file semantic_navigation/SemanticNavigationGoal.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_SEMANTICNAVIGATIONGOAL_H
#define SEMANTIC_NAVIGATION_MESSAGE_SEMANTICNAVIGATIONGOAL_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <geometry_msgs/PoseStamped.h>

namespace semantic_navigation
{
template <class ContainerAllocator>
struct SemanticNavigationGoal_
{
  typedef SemanticNavigationGoal_<ContainerAllocator> Type;

  SemanticNavigationGoal_()
    : header()
    , target_pose()
    , navigation_mode()
    , avoid_classes()
    , prefer_classes()
    , safety_distance(0.0)
    , fire_avoidance_distance(0.0)
    , priority(0)
    , emergency_mode(false)
    , timeout()  {
    }
  SemanticNavigationGoal_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , target_pose(_alloc)
    , navigation_mode(_alloc)
    , avoid_classes(_alloc)
    , prefer_classes(_alloc)
    , safety_distance(0.0)
    , fire_avoidance_distance(0.0)
    , priority(0)
    , emergency_mode(false)
    , timeout()  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef  ::geometry_msgs::PoseStamped_<ContainerAllocator>  _target_pose_type;
  _target_pose_type target_pose;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _navigation_mode_type;
  _navigation_mode_type navigation_mode;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _avoid_classes_type;
  _avoid_classes_type avoid_classes;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _prefer_classes_type;
  _prefer_classes_type prefer_classes;

   typedef float _safety_distance_type;
  _safety_distance_type safety_distance;

   typedef float _fire_avoidance_distance_type;
  _fire_avoidance_distance_type fire_avoidance_distance;

   typedef uint8_t _priority_type;
  _priority_type priority;

   typedef uint8_t _emergency_mode_type;
  _emergency_mode_type emergency_mode;

   typedef ros::Duration _timeout_type;
  _timeout_type timeout;





  typedef boost::shared_ptr< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> const> ConstPtr;

}; // struct SemanticNavigationGoal_

typedef ::semantic_navigation::SemanticNavigationGoal_<std::allocator<void> > SemanticNavigationGoal;

typedef boost::shared_ptr< ::semantic_navigation::SemanticNavigationGoal > SemanticNavigationGoalPtr;
typedef boost::shared_ptr< ::semantic_navigation::SemanticNavigationGoal const> SemanticNavigationGoalConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator1> & lhs, const ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.target_pose == rhs.target_pose &&
    lhs.navigation_mode == rhs.navigation_mode &&
    lhs.avoid_classes == rhs.avoid_classes &&
    lhs.prefer_classes == rhs.prefer_classes &&
    lhs.safety_distance == rhs.safety_distance &&
    lhs.fire_avoidance_distance == rhs.fire_avoidance_distance &&
    lhs.priority == rhs.priority &&
    lhs.emergency_mode == rhs.emergency_mode &&
    lhs.timeout == rhs.timeout;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator1> & lhs, const ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_navigation

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> >
{
  static const char* value()
  {
    return "f3344e67e6ced968d5c3cc9238c45392";
  }

  static const char* value(const ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xf3344e67e6ced968ULL;
  static const uint64_t static_value2 = 0xd5c3cc9238c45392ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_navigation/SemanticNavigationGoal";
  }

  static const char* value(const ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 语义导航目标消息\n"
"Header header\n"
"\n"
"# 目标位置\n"
"geometry_msgs/PoseStamped target_pose\n"
"\n"
"# 导航模式\n"
"string navigation_mode    # NORMAL, EMERGENCY, FIRE_ESCAPE\n"
"\n"
"# 语义约束\n"
"string[] avoid_classes    # 需要避开的语义类别\n"
"string[] prefer_classes   # 优先通过的语义类别\n"
"\n"
"# 安全参数\n"
"float32 safety_distance   # 安全距离 (米)\n"
"float32 fire_avoidance_distance  # 火焰避让距离 (米)\n"
"\n"
"# 优先级设置\n"
"uint8 priority           # 导航优先级 (0-10, 10最高)\n"
"bool emergency_mode      # 是否为应急模式\n"
"\n"
"# 超时设置\n"
"duration timeout         # 导航超时时间\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/PoseStamped\n"
"# A Pose with reference coordinate frame and timestamp\n"
"Header header\n"
"Pose pose\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.target_pose);
      stream.next(m.navigation_mode);
      stream.next(m.avoid_classes);
      stream.next(m.prefer_classes);
      stream.next(m.safety_distance);
      stream.next(m.fire_avoidance_distance);
      stream.next(m.priority);
      stream.next(m.emergency_mode);
      stream.next(m.timeout);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SemanticNavigationGoal_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_navigation::SemanticNavigationGoal_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "target_pose: ";
    Printer< ::geometry_msgs::PoseStamped_<ContainerAllocator> >::stream(s, indent + "  ", v.target_pose);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "navigation_mode: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.navigation_mode);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "avoid_classes: ";
    if (v.avoid_classes.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.avoid_classes.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.avoid_classes[i]);
    }
    if (v.avoid_classes.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "prefer_classes: ";
    if (v.prefer_classes.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.prefer_classes.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.prefer_classes[i]);
    }
    if (v.prefer_classes.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "safety_distance: ";
    Printer<float>::stream(s, indent + "  ", v.safety_distance);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "fire_avoidance_distance: ";
    Printer<float>::stream(s, indent + "  ", v.fire_avoidance_distance);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "priority: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.priority);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "emergency_mode: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.emergency_mode);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "timeout: ";
    Printer<ros::Duration>::stream(s, indent + "  ", v.timeout);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_SEMANTICNAVIGATIONGOAL_H
