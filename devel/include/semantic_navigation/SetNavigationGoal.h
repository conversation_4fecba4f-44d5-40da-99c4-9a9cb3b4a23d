// Generated by gencpp from file semantic_navigation/SetNavigationGoal.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_SETNAVIGATIONGOAL_H
#define SEMANTIC_NAVIGATION_MESSAGE_SETNAVIGATIONGOAL_H

#include <ros/service_traits.h>


#include <semantic_navigation/SetNavigationGoalRequest.h>
#include <semantic_navigation/SetNavigationGoalResponse.h>


namespace semantic_navigation
{

struct SetNavigationGoal
{

typedef SetNavigationGoalRequest Request;
typedef SetNavigationGoalResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct SetNavigationGoal
} // namespace semantic_navigation


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::semantic_navigation::SetNavigationGoal > {
  static const char* value()
  {
    return "9b33341aa6d4bf3615200fa760500108";
  }

  static const char* value(const ::semantic_navigation::SetNavigationGoal&) { return value(); }
};

template<>
struct DataType< ::semantic_navigation::SetNavigationGoal > {
  static const char* value()
  {
    return "semantic_navigation/SetNavigationGoal";
  }

  static const char* value(const ::semantic_navigation::SetNavigationGoal&) { return value(); }
};


// service_traits::MD5Sum< ::semantic_navigation::SetNavigationGoalRequest> should match
// service_traits::MD5Sum< ::semantic_navigation::SetNavigationGoal >
template<>
struct MD5Sum< ::semantic_navigation::SetNavigationGoalRequest>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_navigation::SetNavigationGoal >::value();
  }
  static const char* value(const ::semantic_navigation::SetNavigationGoalRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_navigation::SetNavigationGoalRequest> should match
// service_traits::DataType< ::semantic_navigation::SetNavigationGoal >
template<>
struct DataType< ::semantic_navigation::SetNavigationGoalRequest>
{
  static const char* value()
  {
    return DataType< ::semantic_navigation::SetNavigationGoal >::value();
  }
  static const char* value(const ::semantic_navigation::SetNavigationGoalRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::semantic_navigation::SetNavigationGoalResponse> should match
// service_traits::MD5Sum< ::semantic_navigation::SetNavigationGoal >
template<>
struct MD5Sum< ::semantic_navigation::SetNavigationGoalResponse>
{
  static const char* value()
  {
    return MD5Sum< ::semantic_navigation::SetNavigationGoal >::value();
  }
  static const char* value(const ::semantic_navigation::SetNavigationGoalResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::semantic_navigation::SetNavigationGoalResponse> should match
// service_traits::DataType< ::semantic_navigation::SetNavigationGoal >
template<>
struct DataType< ::semantic_navigation::SetNavigationGoalResponse>
{
  static const char* value()
  {
    return DataType< ::semantic_navigation::SetNavigationGoal >::value();
  }
  static const char* value(const ::semantic_navigation::SetNavigationGoalResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_SETNAVIGATIONGOAL_H
