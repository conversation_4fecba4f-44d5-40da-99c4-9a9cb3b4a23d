// Generated by gencpp from file semantic_navigation/SetNavigationGoalResponse.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_SETNAVIGATIONGOALRESPONSE_H
#define SEMANTIC_NAVIGATION_MESSAGE_SETNAVIGATIONGOALRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <semantic_navigation/SemanticPath.h>

namespace semantic_navigation
{
template <class ContainerAllocator>
struct SetNavigationGoalResponse_
{
  typedef SetNavigationGoalResponse_<ContainerAllocator> Type;

  SetNavigationGoalResponse_()
    : success(false)
    , message()
    , goal_id()
    , estimated_time(0.0)
    , planned_path()  {
    }
  SetNavigationGoalResponse_(const ContainerAllocator& _alloc)
    : success(false)
    , message(_alloc)
    , goal_id(_alloc)
    , estimated_time(0.0)
    , planned_path(_alloc)  {
  (void)_alloc;
    }



   typedef uint8_t _success_type;
  _success_type success;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _message_type;
  _message_type message;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _goal_id_type;
  _goal_id_type goal_id;

   typedef float _estimated_time_type;
  _estimated_time_type estimated_time;

   typedef  ::semantic_navigation::SemanticPath_<ContainerAllocator>  _planned_path_type;
  _planned_path_type planned_path;





  typedef boost::shared_ptr< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> const> ConstPtr;

}; // struct SetNavigationGoalResponse_

typedef ::semantic_navigation::SetNavigationGoalResponse_<std::allocator<void> > SetNavigationGoalResponse;

typedef boost::shared_ptr< ::semantic_navigation::SetNavigationGoalResponse > SetNavigationGoalResponsePtr;
typedef boost::shared_ptr< ::semantic_navigation::SetNavigationGoalResponse const> SetNavigationGoalResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator1> & lhs, const ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator2> & rhs)
{
  return lhs.success == rhs.success &&
    lhs.message == rhs.message &&
    lhs.goal_id == rhs.goal_id &&
    lhs.estimated_time == rhs.estimated_time &&
    lhs.planned_path == rhs.planned_path;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator1> & lhs, const ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_navigation

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "ab557ff716958fd7a5cc224181312313";
  }

  static const char* value(const ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xab557ff716958fd7ULL;
  static const uint64_t static_value2 = 0xa5cc224181312313ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_navigation/SetNavigationGoalResponse";
  }

  static const char* value(const ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
"# 响应\n"
"bool success\n"
"string message\n"
"string goal_id              # 目标ID，用于跟踪\n"
"float32 estimated_time      # 预计完成时间\n"
"SemanticPath planned_path   # 规划的路径\n"
"\n"
"\n"
"================================================================================\n"
"MSG: semantic_navigation/SemanticPath\n"
"# 语义路径消息\n"
"Header header\n"
"\n"
"# 路径信息\n"
"nav_msgs/Path path\n"
"\n"
"# 语义信息\n"
"string[] semantic_labels     # 路径上的语义标签\n"
"float32[] semantic_costs     # 对应的语义代价\n"
"float32[] safety_scores      # 安全性评分\n"
"\n"
"# 路径属性\n"
"float32 total_distance       # 总距离\n"
"float32 total_cost          # 总代价\n"
"float32 safety_rating       # 安全评级 (0-1)\n"
"bool contains_fire_risk     # 是否包含火灾风险\n"
"\n"
"# 应急信息\n"
"geometry_msgs/Point[] emergency_exits  # 应急出口位置\n"
"string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: nav_msgs/Path\n"
"#An array of poses that represents a Path for a robot to follow\n"
"Header header\n"
"geometry_msgs/PoseStamped[] poses\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/PoseStamped\n"
"# A Pose with reference coordinate frame and timestamp\n"
"Header header\n"
"Pose pose\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.success);
      stream.next(m.message);
      stream.next(m.goal_id);
      stream.next(m.estimated_time);
      stream.next(m.planned_path);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SetNavigationGoalResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_navigation::SetNavigationGoalResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "message: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.message);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "goal_id: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.goal_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "estimated_time: ";
    Printer<float>::stream(s, indent + "  ", v.estimated_time);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "planned_path: ";
    Printer< ::semantic_navigation::SemanticPath_<ContainerAllocator> >::stream(s, indent + "  ", v.planned_path);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_SETNAVIGATIONGOALRESPONSE_H
