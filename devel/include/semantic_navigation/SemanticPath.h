// Generated by gencpp from file semantic_navigation/SemanticPath.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_SEMANTICPATH_H
#define SEMANTIC_NAVIGATION_MESSAGE_SEMANTICPATH_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <nav_msgs/Path.h>
#include <geometry_msgs/Point.h>

namespace semantic_navigation
{
template <class ContainerAllocator>
struct SemanticPath_
{
  typedef SemanticPath_<ContainerAllocator> Type;

  SemanticPath_()
    : header()
    , path()
    , semantic_labels()
    , semantic_costs()
    , safety_scores()
    , total_distance(0.0)
    , total_cost(0.0)
    , safety_rating(0.0)
    , contains_fire_risk(false)
    , emergency_exits()
    , risk_level()  {
    }
  SemanticPath_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , path(_alloc)
    , semantic_labels(_alloc)
    , semantic_costs(_alloc)
    , safety_scores(_alloc)
    , total_distance(0.0)
    , total_cost(0.0)
    , safety_rating(0.0)
    , contains_fire_risk(false)
    , emergency_exits(_alloc)
    , risk_level(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef  ::nav_msgs::Path_<ContainerAllocator>  _path_type;
  _path_type path;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _semantic_labels_type;
  _semantic_labels_type semantic_labels;

   typedef std::vector<float, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<float>> _semantic_costs_type;
  _semantic_costs_type semantic_costs;

   typedef std::vector<float, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<float>> _safety_scores_type;
  _safety_scores_type safety_scores;

   typedef float _total_distance_type;
  _total_distance_type total_distance;

   typedef float _total_cost_type;
  _total_cost_type total_cost;

   typedef float _safety_rating_type;
  _safety_rating_type safety_rating;

   typedef uint8_t _contains_fire_risk_type;
  _contains_fire_risk_type contains_fire_risk;

   typedef std::vector< ::geometry_msgs::Point_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Point_<ContainerAllocator> >> _emergency_exits_type;
  _emergency_exits_type emergency_exits;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _risk_level_type;
  _risk_level_type risk_level;





  typedef boost::shared_ptr< ::semantic_navigation::SemanticPath_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_navigation::SemanticPath_<ContainerAllocator> const> ConstPtr;

}; // struct SemanticPath_

typedef ::semantic_navigation::SemanticPath_<std::allocator<void> > SemanticPath;

typedef boost::shared_ptr< ::semantic_navigation::SemanticPath > SemanticPathPtr;
typedef boost::shared_ptr< ::semantic_navigation::SemanticPath const> SemanticPathConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_navigation::SemanticPath_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_navigation::SemanticPath_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_navigation::SemanticPath_<ContainerAllocator1> & lhs, const ::semantic_navigation::SemanticPath_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.path == rhs.path &&
    lhs.semantic_labels == rhs.semantic_labels &&
    lhs.semantic_costs == rhs.semantic_costs &&
    lhs.safety_scores == rhs.safety_scores &&
    lhs.total_distance == rhs.total_distance &&
    lhs.total_cost == rhs.total_cost &&
    lhs.safety_rating == rhs.safety_rating &&
    lhs.contains_fire_risk == rhs.contains_fire_risk &&
    lhs.emergency_exits == rhs.emergency_exits &&
    lhs.risk_level == rhs.risk_level;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_navigation::SemanticPath_<ContainerAllocator1> & lhs, const ::semantic_navigation::SemanticPath_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_navigation

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::SemanticPath_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::SemanticPath_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::SemanticPath_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::SemanticPath_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::SemanticPath_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::SemanticPath_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_navigation::SemanticPath_<ContainerAllocator> >
{
  static const char* value()
  {
    return "0320dcd1e20996da4804a029f13c56f2";
  }

  static const char* value(const ::semantic_navigation::SemanticPath_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x0320dcd1e20996daULL;
  static const uint64_t static_value2 = 0x4804a029f13c56f2ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_navigation::SemanticPath_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_navigation/SemanticPath";
  }

  static const char* value(const ::semantic_navigation::SemanticPath_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_navigation::SemanticPath_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 语义路径消息\n"
"Header header\n"
"\n"
"# 路径信息\n"
"nav_msgs/Path path\n"
"\n"
"# 语义信息\n"
"string[] semantic_labels     # 路径上的语义标签\n"
"float32[] semantic_costs     # 对应的语义代价\n"
"float32[] safety_scores      # 安全性评分\n"
"\n"
"# 路径属性\n"
"float32 total_distance       # 总距离\n"
"float32 total_cost          # 总代价\n"
"float32 safety_rating       # 安全评级 (0-1)\n"
"bool contains_fire_risk     # 是否包含火灾风险\n"
"\n"
"# 应急信息\n"
"geometry_msgs/Point[] emergency_exits  # 应急出口位置\n"
"string risk_level           # 风险等级: LOW, MEDIUM, HIGH, CRITICAL\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: nav_msgs/Path\n"
"#An array of poses that represents a Path for a robot to follow\n"
"Header header\n"
"geometry_msgs/PoseStamped[] poses\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/PoseStamped\n"
"# A Pose with reference coordinate frame and timestamp\n"
"Header header\n"
"Pose pose\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_navigation::SemanticPath_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_navigation::SemanticPath_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.path);
      stream.next(m.semantic_labels);
      stream.next(m.semantic_costs);
      stream.next(m.safety_scores);
      stream.next(m.total_distance);
      stream.next(m.total_cost);
      stream.next(m.safety_rating);
      stream.next(m.contains_fire_risk);
      stream.next(m.emergency_exits);
      stream.next(m.risk_level);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SemanticPath_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_navigation::SemanticPath_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_navigation::SemanticPath_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "path: ";
    Printer< ::nav_msgs::Path_<ContainerAllocator> >::stream(s, indent + "  ", v.path);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "semantic_labels: ";
    if (v.semantic_labels.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.semantic_labels.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.semantic_labels[i]);
    }
    if (v.semantic_labels.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "semantic_costs: ";
    if (v.semantic_costs.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.semantic_costs.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<float>::stream(s, true ? std::string() : indent + "    ", v.semantic_costs[i]);
    }
    if (v.semantic_costs.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "safety_scores: ";
    if (v.safety_scores.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.safety_scores.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<float>::stream(s, true ? std::string() : indent + "    ", v.safety_scores[i]);
    }
    if (v.safety_scores.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "total_distance: ";
    Printer<float>::stream(s, indent + "  ", v.total_distance);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "total_cost: ";
    Printer<float>::stream(s, indent + "  ", v.total_cost);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "safety_rating: ";
    Printer<float>::stream(s, indent + "  ", v.safety_rating);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "contains_fire_risk: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.contains_fire_risk);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "emergency_exits: ";
    if (v.emergency_exits.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.emergency_exits.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.emergency_exits[i]);
    }
    if (v.emergency_exits.empty() || false)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "risk_level: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.risk_level);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_SEMANTICPATH_H
