// Generated by gencpp from file semantic_navigation/GetSafePathRequest.msg
// DO NOT EDIT!


#ifndef SEMANTIC_NAVIGATION_MESSAGE_GETSAFEPATHREQUEST_H
#define SEMANTIC_NAVIGATION_MESSAGE_GETSAFEPATHREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/PoseStamped.h>

namespace semantic_navigation
{
template <class ContainerAllocator>
struct GetSafePathRequest_
{
  typedef GetSafePathRequest_<ContainerAllocator> Type;

  GetSafePathRequest_()
    : start_pose()
    , goal_pose()
    , hazard_types()
    , safety_margin(0.0)
    , emergency_mode(false)  {
    }
  GetSafePathRequest_(const ContainerAllocator& _alloc)
    : start_pose(_alloc)
    , goal_pose(_alloc)
    , hazard_types(_alloc)
    , safety_margin(0.0)
    , emergency_mode(false)  {
  (void)_alloc;
    }



   typedef  ::geometry_msgs::PoseStamped_<ContainerAllocator>  _start_pose_type;
  _start_pose_type start_pose;

   typedef  ::geometry_msgs::PoseStamped_<ContainerAllocator>  _goal_pose_type;
  _goal_pose_type goal_pose;

   typedef std::vector<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>> _hazard_types_type;
  _hazard_types_type hazard_types;

   typedef float _safety_margin_type;
  _safety_margin_type safety_margin;

   typedef uint8_t _emergency_mode_type;
  _emergency_mode_type emergency_mode;





  typedef boost::shared_ptr< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> const> ConstPtr;

}; // struct GetSafePathRequest_

typedef ::semantic_navigation::GetSafePathRequest_<std::allocator<void> > GetSafePathRequest;

typedef boost::shared_ptr< ::semantic_navigation::GetSafePathRequest > GetSafePathRequestPtr;
typedef boost::shared_ptr< ::semantic_navigation::GetSafePathRequest const> GetSafePathRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::semantic_navigation::GetSafePathRequest_<ContainerAllocator1> & lhs, const ::semantic_navigation::GetSafePathRequest_<ContainerAllocator2> & rhs)
{
  return lhs.start_pose == rhs.start_pose &&
    lhs.goal_pose == rhs.goal_pose &&
    lhs.hazard_types == rhs.hazard_types &&
    lhs.safety_margin == rhs.safety_margin &&
    lhs.emergency_mode == rhs.emergency_mode;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::semantic_navigation::GetSafePathRequest_<ContainerAllocator1> & lhs, const ::semantic_navigation::GetSafePathRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace semantic_navigation

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "e74d023e9b083ae9fef6c469b92b6275";
  }

  static const char* value(const ::semantic_navigation::GetSafePathRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xe74d023e9b083ae9ULL;
  static const uint64_t static_value2 = 0xfef6c469b92b6275ULL;
};

template<class ContainerAllocator>
struct DataType< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "semantic_navigation/GetSafePathRequest";
  }

  static const char* value(const ::semantic_navigation::GetSafePathRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 获取安全路径服务\n"
"# 请求\n"
"geometry_msgs/PoseStamped start_pose\n"
"geometry_msgs/PoseStamped goal_pose\n"
"string[] hazard_types       # 需要避开的危险类型\n"
"float32 safety_margin       # 安全边距\n"
"bool emergency_mode         # 是否为应急模式\n"
"\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/PoseStamped\n"
"# A Pose with reference coordinate frame and timestamp\n"
"Header header\n"
"Pose pose\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose\n"
"# A representation of pose in free space, composed of position and orientation. \n"
"Point position\n"
"Quaternion orientation\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Quaternion\n"
"# This represents an orientation in free space in quaternion form.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 w\n"
;
  }

  static const char* value(const ::semantic_navigation::GetSafePathRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.start_pose);
      stream.next(m.goal_pose);
      stream.next(m.hazard_types);
      stream.next(m.safety_margin);
      stream.next(m.emergency_mode);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetSafePathRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::semantic_navigation::GetSafePathRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::semantic_navigation::GetSafePathRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "start_pose: ";
    Printer< ::geometry_msgs::PoseStamped_<ContainerAllocator> >::stream(s, indent + "  ", v.start_pose);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "goal_pose: ";
    Printer< ::geometry_msgs::PoseStamped_<ContainerAllocator> >::stream(s, indent + "  ", v.goal_pose);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "hazard_types: ";
    if (v.hazard_types.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.hazard_types.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, true ? std::string() : indent + "    ", v.hazard_types[i]);
    }
    if (v.hazard_types.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "safety_margin: ";
    Printer<float>::stream(s, indent + "  ", v.safety_margin);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "emergency_mode: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.emergency_mode);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SEMANTIC_NAVIGATION_MESSAGE_GETSAFEPATHREQUEST_H
