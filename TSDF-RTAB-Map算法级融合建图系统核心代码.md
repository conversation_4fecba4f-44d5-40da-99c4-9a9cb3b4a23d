# TSDF-RTAB-Map算法级融合建图系统核心代码

## 1. 位姿订阅中心模块 (pose_subscription_center.cpp)

```cpp
#include <ros/ros.h>
#include <nav_msgs/Odometry.h>
#include <geometry_msgs/TransformStamped.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_ros/buffer.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <tf2/LinearMath/Transform.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <mutex>
#include <Eigen/Dense>
#include <Eigen/Geometry>

class PoseSubscriptionCenter
{
public:
    PoseSubscriptionCenter() : 
        tf_buffer_(ros::Duration(10.0)),
        tf_listener_(tf_buffer_),
        last_pose_time_(0.0),
        pose_quality_threshold_(0.1)
    {
        ros::NodeHandle nh;
        ros::NodeHandle pnh("~");
        
        pnh.param<std::string>("source_frame", source_frame_, "map");
        pnh.param<std::string>("target_frame", target_frame_, "base_link");
        pnh.param<double>("publish_rate", publish_rate_, 30.0);
        pnh.param<double>("pose_quality_threshold", pose_quality_threshold_, 0.1);
        pnh.param<bool>("enable_pose_filtering", enable_pose_filtering_, true);
        pnh.param<bool>("use_odom_backup", use_odom_backup_, true);
        pnh.param<bool>("enable_coordinate_correction", enable_coordinate_correction_, true);
        pnh.param<std::string>("camera_frame", camera_frame_, "zed_left_camera_optical_frame");
        pnh.param<bool>("apply_optical_to_mechanical_transform", apply_optical_to_mechanical_transform_, true);

        odom_pub_ = nh.advertise<nav_msgs::Odometry>("/pose_center/odom", 10);

        if (use_odom_backup_) {
            rtabmap_odom_sub_ = nh.subscribe("/rtabmap/odom", 10,
                                           &PoseSubscriptionCenter::rtabmapOdomCallback, this);
        }
        
        timer_ = nh.createTimer(ros::Duration(1.0 / publish_rate_), 
                               &PoseSubscriptionCenter::timerCallback, this);

        initializeCoordinateTransforms();
    }

    void timerCallback(const ros::TimerEvent& event) {
        nav_msgs::Odometry odom_msg;
        odom_msg.header.stamp = ros::Time::now();
        odom_msg.header.frame_id = source_frame_;
        odom_msg.child_frame_id = target_frame_;

        bool pose_obtained = false;

        if (use_odom_backup_) {
            std::lock_guard<std::mutex> lock(odom_mutex_);
            if (latest_rtabmap_odom_) {
                odom_msg.pose = latest_rtabmap_odom_->pose;
                odom_msg.twist = latest_rtabmap_odom_->twist;
                pose_obtained = true;
                pose_source_ = "rtabmap_odom";
            }
        }

        if (!pose_obtained) {
            geometry_msgs::TransformStamped transform;
            try {
                transform = tf_buffer_.lookupTransform(source_frame_, target_frame_,
                                                      ros::Time(0), ros::Duration(2.0));
                transformToOdometry(transform, odom_msg);
                pose_obtained = true;
                pose_source_ = "tf";
            } catch (tf2::TransformException& ex) {
                return;
            }
        }

        if (enable_pose_filtering_ && !isPoseQualityGood(odom_msg)) {
            return;
        }

        if (enable_coordinate_correction_) {
            geometry_msgs::Transform corrected_transform;
            if (applyCoordinateCorrection(odom_msg.pose.pose, corrected_transform)) {
                odom_msg.pose.pose.position.x = corrected_transform.translation.x;
                odom_msg.pose.pose.position.y = corrected_transform.translation.y;
                odom_msg.pose.pose.position.z = corrected_transform.translation.z;
                odom_msg.pose.pose.orientation = corrected_transform.rotation;
            }
        }

        odom_pub_.publish(odom_msg);
        last_pose_time_ = odom_msg.header.stamp.toSec();
        pose_count_++;
    }

    void rtabmapOdomCallback(const nav_msgs::Odometry::ConstPtr& msg) {
        std::lock_guard<std::mutex> lock(odom_mutex_);
        latest_rtabmap_odom_ = msg;
    }

    void transformToOdometry(const geometry_msgs::TransformStamped& transform, 
                           nav_msgs::Odometry& odom) {
        odom.pose.pose.position.x = transform.transform.translation.x;
        odom.pose.pose.position.y = transform.transform.translation.y;
        odom.pose.pose.position.z = transform.transform.translation.z;
        odom.pose.pose.orientation = transform.transform.rotation;
    }

    bool isPoseQualityGood(const nav_msgs::Odometry& odom) {
        static geometry_msgs::Point last_position;
        static bool first_check = true;
        
        if (first_check) {
            last_position = odom.pose.pose.position;
            first_check = false;
            return true;
        }

        double distance = sqrt(
            pow(odom.pose.pose.position.x - last_position.x, 2) +
            pow(odom.pose.pose.position.y - last_position.y, 2) +
            pow(odom.pose.pose.position.z - last_position.z, 2)
        );

        if (distance > 5.0) {
            return false;
        }

        last_position = odom.pose.pose.position;
        return true;
    }

    void initializeCoordinateTransforms() {
        optical_to_mechanical_rotation_ << 
            0, 0, 1,
            -1, 0, 0,
            0, -1, 0;
        
        mechanical_to_optical_rotation_ = optical_to_mechanical_rotation_.transpose();

        try {
            base_to_camera_transform_ = tf_buffer_.lookupTransform(
                target_frame_, camera_frame_, ros::Time(0), ros::Duration(5.0));
            has_base_to_camera_transform_ = true;
        } catch (tf2::TransformException& ex) {
            has_base_to_camera_transform_ = false;
        }
    }

    bool applyCoordinateCorrection(const geometry_msgs::Pose& input_pose,
                                 geometry_msgs::Transform& corrected_transform) {
        tf2::fromMsg(input_pose, corrected_transform);

        if (has_base_to_camera_transform_) {
            tf2::Transform map_to_base;
            tf2::fromMsg(input_pose, map_to_base);

            tf2::Transform base_to_camera;
            tf2::fromMsg(base_to_camera_transform_.transform, base_to_camera);

            tf2::Transform map_to_camera = map_to_base * base_to_camera;
            corrected_transform = tf2::toMsg(map_to_camera);
        }

        if (apply_optical_to_mechanical_transform_) {
            Eigen::Vector3f position(corrected_transform.translation.x,
                                   corrected_transform.translation.y,
                                   corrected_transform.translation.z);

            Eigen::Quaternionf quaternion(corrected_transform.rotation.w,
                                        corrected_transform.rotation.x,
                                        corrected_transform.rotation.y,
                                        corrected_transform.rotation.z);

            Eigen::Matrix3f rotation_matrix = quaternion.toRotationMatrix();
            Eigen::Matrix3f corrected_rotation = mechanical_to_optical_rotation_ * rotation_matrix;
            Eigen::Quaternionf corrected_quaternion(corrected_rotation);

            corrected_transform.translation.x = position.x();
            corrected_transform.translation.y = position.y();
            corrected_transform.translation.z = position.z();
            corrected_transform.rotation.x = corrected_quaternion.x();
            corrected_transform.rotation.y = corrected_quaternion.y();
            corrected_transform.rotation.z = corrected_quaternion.z();
            corrected_transform.rotation.w = corrected_quaternion.w();
        }

        return true;
    }

private:
    tf2_ros::Buffer tf_buffer_;
    tf2_ros::TransformListener tf_listener_;
    ros::Publisher odom_pub_;
    ros::Subscriber rtabmap_odom_sub_;
    ros::Timer timer_;
    std::string source_frame_;
    std::string target_frame_;
    std::string camera_frame_;
    double publish_rate_;
    double pose_quality_threshold_;
    bool enable_pose_filtering_;
    bool use_odom_backup_;
    bool enable_coordinate_correction_;
    bool apply_optical_to_mechanical_transform_;
    bool has_base_to_camera_transform_;
    Eigen::Matrix3f optical_to_mechanical_rotation_;
    Eigen::Matrix3f mechanical_to_optical_rotation_;
    geometry_msgs::TransformStamped base_to_camera_transform_;
    nav_msgs::Odometry::ConstPtr latest_rtabmap_odom_;
    std::mutex odom_mutex_;
    std::string pose_source_;
    double last_pose_time_;
    int pose_count_ = 0;
};

int main(int argc, char** argv)
{
    ros::init(argc, argv, "pose_subscription_center");
    
    try {
        PoseSubscriptionCenter center;
        ros::spin();
    } catch (const std::exception& e) {
        return -1;
    }
    
    return 0;
}
```

## 2. TSDF融合算法核心模块 (tsdf_fusion.cpp)

```cpp
#include "tsdf_mapping/tsdf_fusion.h"
#include "tsdf_mapping/tsdf_cuda.h"
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include <cv_bridge/cv_bridge.h>
#include <image_geometry/pinhole_camera_model.h>
#include <tf2_eigen/tf2_eigen.h>
#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>

namespace tsdf_mapping {

TSDFFusion::TSDFFusion(ros::NodeHandle& nh) : nh_(nh) {
    nh_.param<double>("voxel_size", voxel_size_, 0.05);
    nh_.param<double>("truncation_distance", truncation_distance_, 0.3);
    nh_.param<double>("max_weight", max_weight_, 100.0);
    nh_.param<bool>("use_rtabmap_pose", use_rtabmap_pose_, true);
    nh_.param<bool>("enable_rtabmap_collaboration", enable_rtabmap_collaboration_, true);
    nh_.param<bool>("enable_gpu_acceleration", enable_gpu_acceleration_, true);

    pointcloud_pub_ = nh_.advertise<sensor_msgs::PointCloud2>("tsdf_pointcloud", 1);
    marker_pub_ = nh_.advertise<visualization_msgs::MarkerArray>("tsdf_markers", 1);

    if (enable_gpu_acceleration_) {
        gpu_tsdf_ = std::make_unique<TSDFCuda>();
    }
}

bool TSDFFusion::initialize() {
    initializeDepthPointcloudTransform();
    initializeQualityControl();
    validateCoordinateConfiguration();
    initializeRTABMapCollaboration();

    if (enable_gpu_acceleration_) {
        if (initializeGPUAcceleration()) {
            return true;
        } else {
            enable_gpu_acceleration_ = false;
        }
    }

    return true;
}

void TSDFFusion::processRGBD(const sensor_msgs::Image::ConstPtr& rgb_msg,
                            const sensor_msgs::Image::ConstPtr& depth_msg,
                            const sensor_msgs::CameraInfo::ConstPtr& camera_info) {
    
    cv_bridge::CvImagePtr rgb_ptr, depth_ptr;
    try {
        rgb_ptr = cv_bridge::toCvCopy(rgb_msg, sensor_msgs::image_encodings::BGR8);
        depth_ptr = cv_bridge::toCvCopy(depth_msg, sensor_msgs::image_encodings::TYPE_16UC1);
    } catch (cv_bridge::Exception& e) {
        return;
    }

    Eigen::Matrix4f camera_pose;
    if (!getCurrentCameraPose(camera_pose, rgb_msg->header.stamp)) {
        return;
    }

    updateDynamicVolumeOrigin(camera_pose);

    if (enable_gpu_acceleration_ && gpu_tsdf_) {
        bool gpu_success = processRGBDWithGPU(rgb_msg, depth_msg, camera_info, camera_pose);
        if (gpu_success) {
            transferGPUVoxelsToCPU();
        }
    } else {
        processRGBDWithCPU(rgb_ptr->image, depth_ptr->image, *camera_info, camera_pose);
    }
}

bool TSDFFusion::processRGBDWithGPU(const sensor_msgs::Image::ConstPtr& rgb_msg,
                                   const sensor_msgs::Image::ConstPtr& depth_msg,
                                   const sensor_msgs::CameraInfo::ConstPtr& camera_info,
                                   const Eigen::Matrix4f& camera_pose) {
    
    cv_bridge::CvImagePtr rgb_ptr, depth_ptr;
    try {
        rgb_ptr = cv_bridge::toCvCopy(rgb_msg, sensor_msgs::image_encodings::BGR8);
        depth_ptr = cv_bridge::toCvCopy(depth_msg, sensor_msgs::image_encodings::TYPE_16UC1);
    } catch (cv_bridge::Exception& e) {
        return false;
    }

    cv::Mat rgb_image = rgb_ptr->image;
    cv::Mat depth_image = depth_ptr->image;

    bool success = gpu_tsdf_->processRGBD(rgb_image, depth_image, camera_pose);
    
    if (success) {
        auto perf_stats = gpu_tsdf_->getPerformanceStats();
    }

    return success;
}

void TSDFFusion::processRGBDWithCPU(const cv::Mat& rgb_image, const cv::Mat& depth_image,
                                   const sensor_msgs::CameraInfo& camera_info,
                                   const Eigen::Matrix4f& camera_pose) {
    
    image_geometry::PinholeCameraModel camera_model;
    camera_model.fromCameraInfo(camera_info);

    for (int v = 0; v < depth_image.rows; v += 2) {
        for (int u = 0; u < depth_image.cols; u += 2) {
            uint16_t depth_raw = depth_image.at<uint16_t>(v, u);
            if (depth_raw == 0) continue;

            float depth = depth_raw * 0.001f;
            if (depth < 0.3f || depth > 10.0f) continue;

            cv::Point3d point_3d = camera_model.projectPixelTo3dRay(cv::Point2d(u, v));
            point_3d.x *= depth;
            point_3d.y *= depth;
            point_3d.z = depth;

            Eigen::Vector4f point_camera(point_3d.x, point_3d.y, point_3d.z, 1.0f);
            Eigen::Vector4f point_world = camera_pose * point_camera;
            Eigen::Vector3f surface_point = point_world.head<3>();

            cv::Vec3b color = rgb_image.at<cv::Vec3b>(v, u);
            Eigen::Vector3f surface_normal = computeSurfaceNormal(u, v, depth_image, camera_model, camera_pose);

            updateTSDFVoxelsAroundPoint(surface_point, depth, color, camera_pose, surface_normal);
        }
    }
}

void TSDFFusion::updateTSDFVoxelsAroundPoint(const Eigen::Vector3f& surface_point,
                                           float depth,
                                           const cv::Vec3b& color,
                                           const Eigen::Matrix4f& camera_pose,
                                           const Eigen::Vector3f& surface_normal) {
    
    Eigen::Vector3f camera_position = camera_pose.block<3, 1>(0, 3);
    VoxelIndex center_voxel = worldToVoxel(surface_point);
    int radius = static_cast<int>(std::ceil(truncation_distance_ / voxel_size_ * 0.5f)) + 1;

    for (int dx = -radius; dx <= radius; dx++) {
        for (int dy = -radius; dy <= radius; dy++) {
            for (int dz = -radius; dz <= radius; dz++) {
                VoxelIndex voxel_idx(center_voxel.x + dx, center_voxel.y + dy, center_voxel.z + dz);
                Eigen::Vector3f voxel_center = voxelToWorld(voxel_idx);

                float distance_to_surface = (voxel_center - surface_point).norm();

                if (distance_to_surface <= truncation_distance_) {
                    Eigen::Vector3f to_surface = surface_point - voxel_center;
                    float sdf = to_surface.dot(surface_normal);

                    sdf = std::max(-truncation_distance_, std::min(truncation_distance_, sdf));
                    sdf /= truncation_distance_;

                    float weight = calculateAdaptiveWeight(sdf, depth) * 1.5f;
                    updateTSDFVoxel(voxel_idx, sdf, weight, color);
                }
            }
        }
    }
}

void TSDFFusion::updateTSDFVoxel(const VoxelIndex& voxel_idx, float sdf_value, 
                                float weight, const cv::Vec3b& color) {
    auto it = tsdf_volume_.find(voxel_idx);
    if (it == tsdf_volume_.end()) {
        TSDFVoxel voxel;
        voxel.tsdf_value = sdf_value;
        voxel.weight = weight;
        voxel.r = color[2];
        voxel.g = color[1];
        voxel.b = color[0];
        tsdf_volume_[voxel_idx] = voxel;
    } else {
        TSDFVoxel& voxel = it->second;
        float weight_decay = 0.95f;
        voxel.weight *= weight_decay;

        float total_weight = voxel.weight + weight;
        if (total_weight > max_weight_) {
            float scale = max_weight_ / total_weight;
            voxel.weight *= scale;
            weight *= scale;
            total_weight = max_weight_;
        }

        voxel.tsdf_value = (voxel.tsdf_value * voxel.weight + sdf_value * weight) / total_weight;
        voxel.weight = total_weight;

        voxel.r = static_cast<uint8_t>((voxel.r * voxel.weight + color[2] * weight) / total_weight);
        voxel.g = static_cast<uint8_t>((voxel.g * voxel.weight + color[1] * weight) / total_weight);
        voxel.b = static_cast<uint8_t>((voxel.b * voxel.weight + color[0] * weight) / total_weight);
    }
}

VoxelIndex TSDFFusion::worldToVoxel(const Eigen::Vector3f& world_pos) const {
    if (volume_origin_initialized_) {
        Eigen::Vector3f relative_pos = world_pos - volume_origin_;
        return VoxelIndex(
            static_cast<int>(std::floor(relative_pos.x() / voxel_size_)),
            static_cast<int>(std::floor(relative_pos.y() / voxel_size_)),
            static_cast<int>(std::floor(relative_pos.z() / voxel_size_))
        );
    } else {
        return VoxelIndex(
            static_cast<int>(std::floor(world_pos.x() / voxel_size_)),
            static_cast<int>(std::floor(world_pos.y() / voxel_size_)),
            static_cast<int>(std::floor(world_pos.z() / voxel_size_))
        );
    }
}

Eigen::Vector3f TSDFFusion::voxelToWorld(const VoxelIndex& voxel_idx) const {
    if (volume_origin_initialized_) {
        Eigen::Vector3f relative_pos(
            (voxel_idx.x + 0.5f) * voxel_size_,
            (voxel_idx.y + 0.5f) * voxel_size_,
            (voxel_idx.z + 0.5f) * voxel_size_
        );
        Eigen::Vector3f world_pos = volume_origin_ + relative_pos;
        return world_pos;
    } else {
        return Eigen::Vector3f(
            (voxel_idx.x + 0.5f) * voxel_size_,
            (voxel_idx.y + 0.5f) * voxel_size_,
            (voxel_idx.z + 0.5f) * voxel_size_
        );
    }
}

bool TSDFFusion::getCurrentCameraPose(Eigen::Matrix4f& camera_pose, const ros::Time& timestamp) {
    if (use_rtabmap_pose_ && enable_rtabmap_collaboration_) {
        std::lock_guard<std::mutex> lock(pose_mutex_);
        if (latest_rtabmap_pose_) {
            geometry_msgs::Pose pose = latest_rtabmap_pose_->pose.pose;
            
            Eigen::Translation3f translation(pose.position.x, pose.position.y, pose.position.z);
            Eigen::Quaternionf rotation(pose.orientation.w, pose.orientation.x, 
                                      pose.orientation.y, pose.orientation.z);
            
            camera_pose = (translation * rotation).matrix();
            return true;
        }
    }

    try {
        geometry_msgs::TransformStamped transform = tf_buffer_.lookupTransform(
            "map", "base_link", timestamp, ros::Duration(0.1));
        
        Eigen::Translation3f translation(transform.transform.translation.x,
                                       transform.transform.translation.y,
                                       transform.transform.translation.z);
        Eigen::Quaternionf rotation(transform.transform.rotation.w,
                                  transform.transform.rotation.x,
                                  transform.transform.rotation.y,
                                  transform.transform.rotation.z);
        
        camera_pose = (translation * rotation).matrix();
        return true;
    } catch (tf2::TransformException& ex) {
        return false;
    }
}

void TSDFFusion::initializeRTABMapCollaboration() {
    if (use_rtabmap_pose_ && enable_rtabmap_collaboration_) {
        std::string selected_topic = "/pose_center/odom";
        rtabmap_odom_sub_ = nh_.subscribe(selected_topic, 10,
                                         &TSDFFusion::rtabmapOdomCallback, this);
    }
}

void TSDFFusion::rtabmapOdomCallback(const nav_msgs::Odometry::ConstPtr& msg) {
    std::lock_guard<std::mutex> lock(pose_mutex_);
    latest_rtabmap_pose_ = msg;
}

bool TSDFFusion::initializeGPUAcceleration() {
    CudaTSDFParams gpu_params;
    gpu_params.voxel_size = voxel_size_;
    gpu_params.truncation_distance = truncation_distance_;
    gpu_params.max_weight = max_weight_;

    float volume_extent = 10.0f;
    gpu_params.volume_size_x = static_cast<int>(volume_extent / voxel_size_);
    gpu_params.volume_size_y = static_cast<int>(volume_extent / voxel_size_);
    gpu_params.volume_size_z = static_cast<int>(volume_extent / voxel_size_);

    gpu_params.volume_origin_x = -volume_extent / 2.0f;
    gpu_params.volume_origin_y = -volume_extent / 2.0f;
    gpu_params.volume_origin_z = -volume_extent / 2.0f;

    CudaCameraParams camera_params;
    camera_params.fx = 525.0f;
    camera_params.fy = 525.0f;
    camera_params.cx = 320.0f;
    camera_params.cy = 240.0f;
    camera_params.width = 640;
    camera_params.height = 480;

    if (gpu_tsdf_->initialize(gpu_params, camera_params)) {
        gpu_initialized_ = true;
        return true;
    } else {
        return false;
    }
}

void TSDFFusion::transferGPUVoxelsToCPU() {
    if (!gpu_tsdf_ || !gpu_initialized_) return;

    auto gpu_voxels = gpu_tsdf_->getVoxelData();
    
    for (const auto& gpu_voxel : gpu_voxels) {
        VoxelIndex idx(gpu_voxel.x, gpu_voxel.y, gpu_voxel.z);
        
        TSDFVoxel cpu_voxel;
        cpu_voxel.tsdf_value = gpu_voxel.tsdf_value;
        cpu_voxel.weight = gpu_voxel.weight;
        cpu_voxel.r = gpu_voxel.r;
        cpu_voxel.g = gpu_voxel.g;
        cpu_voxel.b = gpu_voxel.b;
        
        tsdf_volume_[idx] = cpu_voxel;
    }
}

void TSDFFusion::publishVisualization() {
    publishPointCloud();
    publishVoxelMarkers();
}

void TSDFFusion::publishPointCloud() {
    pcl::PointCloud<pcl::PointXYZRGB> cloud;
    
    for (const auto& pair : tsdf_volume_) {
        const VoxelIndex& idx = pair.first;
        const TSDFVoxel& voxel = pair.second;
        
        if (voxel.weight > 1.0f && std::abs(voxel.tsdf_value) < 0.1f) {
            Eigen::Vector3f world_pos = voxelToWorld(idx);
            
            pcl::PointXYZRGB point;
            point.x = world_pos.x();
            point.y = world_pos.y();
            point.z = world_pos.z();
            point.r = voxel.r;
            point.g = voxel.g;
            point.b = voxel.b;
            
            cloud.points.push_back(point);
        }
    }
    
    cloud.width = cloud.points.size();
    cloud.height = 1;
    cloud.is_dense = false;
    
    sensor_msgs::PointCloud2 cloud_msg;
    pcl::toROSMsg(cloud, cloud_msg);
    cloud_msg.header.frame_id = "map";
    cloud_msg.header.stamp = ros::Time::now();
    
    pointcloud_pub_.publish(cloud_msg);
}

} // namespace tsdf_mapping
```

## 3. TSDF融合节点模块 (tsdf_fusion_node.cpp)

```cpp
#include "tsdf_mapping/tsdf_fusion.h"
#include <message_filters/subscriber.h>
#include <message_filters/time_synchronizer.h>
#include <message_filters/sync_policies/approximate_time.h>
#include <message_filters/sync_policies/exact_time.h>
#include <boost/thread/mutex.hpp>
#include <rosgraph_msgs/Clock.h>
#include <ros/topic.h>

class TSDFFusionNode {
public:
    TSDFFusionNode() : nh_("~"), use_manual_sync_(false), sync_timeout_(0.1) {
        tsdf_fusion_ = std::make_unique<tsdf_mapping::TSDFFusion>(nh_);

        if (!tsdf_fusion_->initialize()) {
            return;
        }

        checkSimTimeStatus();
        setupSubscribers();
    }

private:
    ros::NodeHandle nh_;
    std::unique_ptr<tsdf_mapping::TSDFFusion> tsdf_fusion_;

    std::unique_ptr<message_filters::Subscriber<sensor_msgs::Image>> depth_sub_;
    std::unique_ptr<message_filters::Subscriber<sensor_msgs::Image>> rgb_sub_;
    std::unique_ptr<message_filters::Subscriber<sensor_msgs::CameraInfo>> camera_info_sub_filter_;

    typedef message_filters::sync_policies::ApproximateTime<sensor_msgs::Image, sensor_msgs::Image, sensor_msgs::CameraInfo> ApproxSyncPolicy;
    std::unique_ptr<message_filters::Synchronizer<ApproxSyncPolicy>> approx_sync_;

    typedef message_filters::sync_policies::ExactTime<sensor_msgs::Image, sensor_msgs::Image, sensor_msgs::CameraInfo> ExactSyncPolicy;
    std::unique_ptr<message_filters::Synchronizer<ExactSyncPolicy>> exact_sync_;

    ros::Subscriber manual_depth_sub_;
    ros::Subscriber manual_rgb_sub_;
    ros::Subscriber manual_camera_info_sub_;

    sensor_msgs::Image::ConstPtr latest_depth_;
    sensor_msgs::Image::ConstPtr latest_rgb_;
    sensor_msgs::CameraInfo::ConstPtr latest_camera_info_;
    boost::mutex data_mutex_;

    bool use_manual_sync_;
    double sync_timeout_;
    ros::Timer publish_timer_;

    int callback_count_ = 0;
    double total_process_time_ = 0.0;
    int process_count_ = 0;
    double current_publish_rate_ = 10.0;

    void checkSimTimeStatus() {
        bool use_sim_time = false;
        nh_.param("/use_sim_time", use_sim_time, false);

        if (use_sim_time) {
            use_manual_sync_ = true;
        }
    }

    void setupSubscribers() {
        std::string rgb_topic, depth_topic, camera_info_topic;
        nh_.param<std::string>("rgb_topic", rgb_topic, "/camera/color/image_raw");
        nh_.param<std::string>("depth_topic", depth_topic, "/camera/aligned_depth_to_color/image_raw");
        nh_.param<std::string>("camera_info_topic", camera_info_topic, "/camera/color/camera_info");

        nh_.param<bool>("use_manual_sync", use_manual_sync_, true);
        nh_.param<double>("sync_timeout", sync_timeout_, 0.1);
        use_manual_sync_ = true;

        if (use_manual_sync_) {
            setupManualSync(rgb_topic, depth_topic, camera_info_topic);
        } else {
            setupMessageFilters(rgb_topic, depth_topic, camera_info_topic);
        }

        publish_timer_ = nh_.createTimer(ros::Duration(1.0/current_publish_rate_),
                                       &TSDFFusionNode::publishCallback, this);
    }

    void setupManualSync(const std::string& rgb_topic, const std::string& depth_topic,
                        const std::string& camera_info_topic) {
        manual_rgb_sub_ = nh_.subscribe(rgb_topic, 1, &TSDFFusionNode::manualRgbCallback, this);
        manual_depth_sub_ = nh_.subscribe(depth_topic, 1, &TSDFFusionNode::manualDepthCallback, this);
        manual_camera_info_sub_ = nh_.subscribe(camera_info_topic, 1, &TSDFFusionNode::manualCameraInfoCallback, this);
    }

    void setupMessageFilters(const std::string& rgb_topic, const std::string& depth_topic,
                           const std::string& camera_info_topic) {
        rgb_sub_ = std::make_unique<message_filters::Subscriber<sensor_msgs::Image>>(nh_, rgb_topic, 1);
        depth_sub_ = std::make_unique<message_filters::Subscriber<sensor_msgs::Image>>(nh_, depth_topic, 1);
        camera_info_sub_filter_ = std::make_unique<message_filters::Subscriber<sensor_msgs::CameraInfo>>(nh_, camera_info_topic, 1);

        bool use_exact_sync = false;
        nh_.param<bool>("use_exact_sync", use_exact_sync, false);

        if (use_exact_sync) {
            exact_sync_ = std::make_unique<message_filters::Synchronizer<ExactSyncPolicy>>(
                ExactSyncPolicy(10), *rgb_sub_, *depth_sub_, *camera_info_sub_filter_);
            exact_sync_->registerCallback(boost::bind(&TSDFFusionNode::syncCallback, this, _1, _2, _3));
        } else {
            approx_sync_ = std::make_unique<message_filters::Synchronizer<ApproxSyncPolicy>>(
                ApproxSyncPolicy(10), *rgb_sub_, *depth_sub_, *camera_info_sub_filter_);
            approx_sync_->registerCallback(boost::bind(&TSDFFusionNode::syncCallback, this, _1, _2, _3));
        }
    }

    void manualRgbCallback(const sensor_msgs::Image::ConstPtr& msg) {
        boost::lock_guard<boost::mutex> lock(data_mutex_);
        latest_rgb_ = msg;
    }

    void manualDepthCallback(const sensor_msgs::Image::ConstPtr& msg) {
        boost::lock_guard<boost::mutex> lock(data_mutex_);
        latest_depth_ = msg;
    }

    void manualCameraInfoCallback(const sensor_msgs::CameraInfo::ConstPtr& msg) {
        boost::lock_guard<boost::mutex> lock(data_mutex_);
        latest_camera_info_ = msg;
    }

    void publishCallback(const ros::TimerEvent& event) {
        if (!use_manual_sync_) return;

        sensor_msgs::Image::ConstPtr rgb_msg, depth_msg;
        sensor_msgs::CameraInfo::ConstPtr camera_info_msg;

        {
            boost::lock_guard<boost::mutex> lock(data_mutex_);
            if (!latest_rgb_ || !latest_depth_ || !latest_camera_info_) {
                return;
            }

            ros::Time now = ros::Time::now();
            if ((now - latest_rgb_->header.stamp).toSec() > sync_timeout_ ||
                (now - latest_depth_->header.stamp).toSec() > sync_timeout_) {
                return;
            }

            rgb_msg = latest_rgb_;
            depth_msg = latest_depth_;
            camera_info_msg = latest_camera_info_;
        }

        processData(rgb_msg, depth_msg, camera_info_msg);
    }

    void syncCallback(const sensor_msgs::Image::ConstPtr& rgb_msg,
                     const sensor_msgs::Image::ConstPtr& depth_msg,
                     const sensor_msgs::CameraInfo::ConstPtr& camera_info_msg) {
        processData(rgb_msg, depth_msg, camera_info_msg);
    }

    void processData(const sensor_msgs::Image::ConstPtr& rgb_msg,
                    const sensor_msgs::Image::ConstPtr& depth_msg,
                    const sensor_msgs::CameraInfo::ConstPtr& camera_info_msg) {

        ros::Time process_start = ros::Time::now();
        callback_count_++;

        tsdf_fusion_->processRGBD(rgb_msg, depth_msg, camera_info_msg);
        tsdf_fusion_->publishVisualization();

        double process_time = (ros::Time::now() - process_start).toSec();
        updatePerformanceMetrics(process_time);

        if (callback_count_ % 10 == 0) {
            double avg_process_time = total_process_time_ / process_count_;
        }
    }

    void updatePerformanceMetrics(double process_time) {
        total_process_time_ += process_time;
        process_count_++;

        if (process_count_ >= 10) {
            double avg_process_time = total_process_time_ / process_count_;

            double target_rate;
            if (avg_process_time < 0.05) {
                target_rate = 20.0;
            } else if (avg_process_time < 0.1) {
                target_rate = 10.0;
            } else if (avg_process_time < 0.2) {
                target_rate = 5.0;
            } else {
                target_rate = 2.0;
            }

            if (std::abs(target_rate - current_publish_rate_) > 0.5) {
                current_publish_rate_ = target_rate;
                publish_timer_.stop();
                publish_timer_ = nh_.createTimer(ros::Duration(1.0/current_publish_rate_),
                                               &TSDFFusionNode::publishCallback, this);
            }

            total_process_time_ = 0.0;
            process_count_ = 0;
        }
    }
};

int main(int argc, char** argv) {
    ros::init(argc, argv, "tsdf_fusion_node");

    TSDFFusionNode node;

    ros::spin();

    return 0;
}
```

## 4. GPU加速TSDF模块 (tsdf_cuda.cu)

```cpp
#include "tsdf_mapping/tsdf_cuda.h"
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <thrust/device_vector.h>
#include <thrust/host_vector.h>
#include <thrust/copy.h>
#include <iostream>
#include <chrono>

struct CudaVoxel {
    float tsdf_value;
    float weight;
    uint8_t r, g, b;
    int x, y, z;
};

__global__ void updateTSDFKernel(
    CudaVoxel* voxels,
    int* voxel_indices,
    int num_voxels,
    float* depth_image,
    uint8_t* rgb_image,
    int width, int height,
    float fx, float fy, float cx, float cy,
    float* camera_pose,
    float voxel_size,
    float truncation_distance,
    float max_weight,
    float volume_origin_x,
    float volume_origin_y,
    float volume_origin_z
) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx >= num_voxels) return;

    CudaVoxel& voxel = voxels[idx];

    float world_x = volume_origin_x + (voxel.x + 0.5f) * voxel_size;
    float world_y = volume_origin_y + (voxel.y + 0.5f) * voxel_size;
    float world_z = volume_origin_z + (voxel.z + 0.5f) * voxel_size;

    float camera_x = camera_pose[0] * world_x + camera_pose[1] * world_y + camera_pose[2] * world_z + camera_pose[3];
    float camera_y = camera_pose[4] * world_x + camera_pose[5] * world_y + camera_pose[6] * world_z + camera_pose[7];
    float camera_z = camera_pose[8] * world_x + camera_pose[9] * world_y + camera_pose[10] * world_z + camera_pose[11];

    if (camera_z <= 0.1f) return;

    int pixel_u = __float2int_rn(fx * camera_x / camera_z + cx);
    int pixel_v = __float2int_rn(fy * camera_y / camera_z + cy);

    if (pixel_u < 0 || pixel_u >= width || pixel_v < 0 || pixel_v >= height) return;

    float measured_depth = depth_image[pixel_v * width + pixel_u];
    if (measured_depth <= 0.0f) return;

    float sdf = measured_depth - camera_z;
    if (sdf < -truncation_distance) return;

    sdf = fminf(truncation_distance, sdf);
    sdf /= truncation_distance;

    float weight = 1.0f;
    if (fabsf(sdf) < 0.1f) {
        weight = 2.0f;
    }

    float old_weight = voxel.weight;
    float new_weight = fminf(old_weight + weight, max_weight);

    if (new_weight > 0.0f) {
        voxel.tsdf_value = (voxel.tsdf_value * old_weight + sdf * weight) / new_weight;
        voxel.weight = new_weight;

        int rgb_idx = (pixel_v * width + pixel_u) * 3;
        uint8_t r = rgb_image[rgb_idx + 2];
        uint8_t g = rgb_image[rgb_idx + 1];
        uint8_t b = rgb_image[rgb_idx + 0];

        voxel.r = (uint8_t)((voxel.r * old_weight + r * weight) / new_weight);
        voxel.g = (uint8_t)((voxel.g * old_weight + g * weight) / new_weight);
        voxel.b = (uint8_t)((voxel.b * old_weight + b * weight) / new_weight);
    }
}

TSDFCuda::TSDFCuda() : initialized_(false) {
    cudaEventCreate(&start_event_);
    cudaEventCreate(&stop_event_);
    cudaStreamCreate(&compute_stream_);
    cudaStreamCreate(&memory_stream_);
}

TSDFCuda::~TSDFCuda() {
    if (initialized_) {
        cleanup();
    }

    cudaEventDestroy(start_event_);
    cudaEventDestroy(stop_event_);
    cudaStreamDestroy(compute_stream_);
    cudaStreamDestroy(memory_stream_);
}

bool TSDFCuda::initialize(const CudaTSDFParams& params, const CudaCameraParams& camera_params) {
    params_ = params;
    camera_params_ = camera_params;

    int total_voxels = params_.volume_size_x * params_.volume_size_y * params_.volume_size_z;

    try {
        d_voxels_.resize(total_voxels);
        d_depth_image_.resize(camera_params_.width * camera_params_.height);
        d_rgb_image_.resize(camera_params_.width * camera_params_.height * 3);
        d_camera_pose_.resize(16);

        thrust::fill(d_voxels_.begin(), d_voxels_.end(), CudaVoxel{0.0f, 0.0f, 0, 0, 0, 0, 0, 0});

        initialized_ = true;
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

bool TSDFCuda::processRGBD(const cv::Mat& rgb_image,
                          const cv::Mat& depth_image,
                          const Eigen::Matrix4f& camera_pose) {
    if (!initialized_) {
        return false;
    }

    if (!checkGPUHealth()) {
        if (!attemptGPURecovery()) {
            return false;
        }
    }

    cudaEventRecord(start_event_, compute_stream_);

    float pose_data[16];
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 4; j++) {
            pose_data[i * 4 + j] = camera_pose(i, j);
        }
    }

    cv::Mat depth_float;
    depth_image.convertTo(depth_float, CV_32F, 0.001f);

    thrust::copy(depth_float.ptr<float>(),
                depth_float.ptr<float>() + depth_float.total(),
                d_depth_image_.begin());

    thrust::copy(rgb_image.ptr<uint8_t>(),
                rgb_image.ptr<uint8_t>() + rgb_image.total(),
                d_rgb_image_.begin());

    thrust::copy(pose_data, pose_data + 16, d_camera_pose_.begin());

    int total_voxels = params_.volume_size_x * params_.volume_size_y * params_.volume_size_z;
    int threads_per_block = 256;
    int blocks = (total_voxels + threads_per_block - 1) / threads_per_block;

    updateTSDFKernel<<<blocks, threads_per_block, 0, compute_stream_>>>(
        thrust::raw_pointer_cast(d_voxels_.data()),
        nullptr,
        total_voxels,
        thrust::raw_pointer_cast(d_depth_image_.data()),
        thrust::raw_pointer_cast(d_rgb_image_.data()),
        camera_params_.width, camera_params_.height,
        camera_params_.fx, camera_params_.fy,
        camera_params_.cx, camera_params_.cy,
        thrust::raw_pointer_cast(d_camera_pose_.data()),
        params_.voxel_size,
        params_.truncation_distance,
        params_.max_weight,
        params_.volume_origin_x,
        params_.volume_origin_y,
        params_.volume_origin_z
    );

    cudaEventRecord(stop_event_, compute_stream_);
    cudaEventSynchronize(stop_event_);

    float elapsed_time;
    cudaEventElapsedTime(&elapsed_time, start_event_, stop_event_);

    perf_stats_.gpu_process_time_ms = elapsed_time;
    perf_stats_.processed_frames++;

    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time_).count();
    if (duration > 0) {
        perf_stats_.average_fps = (perf_stats_.processed_frames * 1000.0f) / duration;
    }

    return true;
}

std::vector<CudaVoxel> TSDFCuda::getVoxelData() const {
    if (!initialized_) {
        return {};
    }

    thrust::host_vector<CudaVoxel> h_voxels = d_voxels_;

    std::vector<CudaVoxel> result;
    result.reserve(h_voxels.size());

    for (size_t i = 0; i < h_voxels.size(); ++i) {
        const CudaVoxel& voxel = h_voxels[i];
        if (voxel.weight > 1.0f) {
            CudaVoxel output_voxel = voxel;

            int z = i / (params_.volume_size_x * params_.volume_size_y);
            int y = (i % (params_.volume_size_x * params_.volume_size_y)) / params_.volume_size_x;
            int x = i % params_.volume_size_x;

            output_voxel.x = x;
            output_voxel.y = y;
            output_voxel.z = z;

            result.push_back(output_voxel);
        }
    }

    return result;
}

bool TSDFCuda::checkGPUHealth() const {
    cudaError_t error = cudaGetLastError();
    if (error != cudaSuccess) {
        return false;
    }

    size_t free_mem, total_mem;
    cudaMemGetInfo(&free_mem, &total_mem);

    float memory_usage = 1.0f - (float)free_mem / (float)total_mem;
    if (memory_usage > 0.95f) {
        return false;
    }

    return true;
}

bool TSDFCuda::attemptGPURecovery() {
    cudaDeviceReset();

    cudaEventCreate(&start_event_);
    cudaEventCreate(&stop_event_);
    cudaStreamCreate(&compute_stream_);
    cudaStreamCreate(&memory_stream_);

    return initialize(params_, camera_params_);
}

void TSDFCuda::getMemoryUsage(size_t& used_mem, size_t& total_mem) const {
    size_t free_mem;
    cudaMemGetInfo(&free_mem, &total_mem);
    used_mem = total_mem - free_mem;
}

CudaPerformanceStats TSDFCuda::getPerformanceStats() const {
    return perf_stats_;
}

void TSDFCuda::cleanup() {
    d_voxels_.clear();
    d_depth_image_.clear();
    d_rgb_image_.clear();
    d_camera_pose_.clear();

    initialized_ = false;
}
```

## 5. TSDF融合头文件 (tsdf_fusion.h)

```cpp
#ifndef TSDF_MAPPING_TSDF_FUSION_H
#define TSDF_MAPPING_TSDF_FUSION_H

#include <ros/ros.h>
#include <sensor_msgs/Image.h>
#include <sensor_msgs/PointCloud2.h>
#include <sensor_msgs/CameraInfo.h>
#include <nav_msgs/Odometry.h>
#include <geometry_msgs/TransformStamped.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_ros/buffer.h>
#include <cv_bridge/cv_bridge.h>
#include <opencv2/opencv.hpp>
#include <Eigen/Dense>
#include <unordered_map>
#include <memory>
#include <mutex>

namespace tsdf_mapping {

struct VoxelIndex {
    int x, y, z;

    VoxelIndex() : x(0), y(0), z(0) {}
    VoxelIndex(int x_, int y_, int z_) : x(x_), y(y_), z(z_) {}

    bool operator==(const VoxelIndex& other) const {
        return x == other.x && y == other.y && z == other.z;
    }
};

struct VoxelIndexHash {
    std::size_t operator()(const VoxelIndex& idx) const {
        return std::hash<int>()(idx.x) ^
               (std::hash<int>()(idx.y) << 1) ^
               (std::hash<int>()(idx.z) << 2);
    }
};

struct TSDFVoxel {
    float tsdf_value;
    float weight;
    uint8_t r, g, b;

    TSDFVoxel() : tsdf_value(0.0f), weight(0.0f), r(0), g(0), b(0) {}
};

class TSDFCuda;

class TSDFFusion {
public:
    explicit TSDFFusion(ros::NodeHandle& nh);
    ~TSDFFusion();

    bool initialize();

    void processRGBD(const sensor_msgs::Image::ConstPtr& rgb_msg,
                     const sensor_msgs::Image::ConstPtr& depth_msg,
                     const sensor_msgs::CameraInfo::ConstPtr& camera_info);

    void publishVisualization();

private:
    ros::NodeHandle& nh_;

    ros::Publisher pointcloud_pub_;
    ros::Publisher marker_pub_;
    ros::Publisher performance_pub_;

    ros::Subscriber rtabmap_odom_sub_;

    tf2_ros::Buffer tf_buffer_;
    tf2_ros::TransformListener tf_listener_;

    std::unordered_map<VoxelIndex, TSDFVoxel, VoxelIndexHash> tsdf_volume_;

    double voxel_size_;
    double truncation_distance_;
    double max_weight_;
    bool use_rtabmap_pose_;
    bool enable_rtabmap_collaboration_;

    nav_msgs::Odometry::ConstPtr latest_rtabmap_pose_;
    std::mutex pose_mutex_;

    mutable cv::Mat depth_gradient_x_;
    mutable cv::Mat depth_gradient_y_;
    mutable cv::Mat depth_gradient_magnitude_;

    std::unique_ptr<TSDFCuda> gpu_tsdf_;
    bool enable_gpu_acceleration_;
    bool gpu_initialized_;

    Eigen::Vector3f volume_origin_;
    bool volume_origin_initialized_;
    float volume_update_threshold_;
    float migration_distance_threshold_;
    Eigen::Vector3f last_robot_position_;

    void processRGBDWithCPU(const cv::Mat& rgb_image, const cv::Mat& depth_image,
                           const sensor_msgs::CameraInfo& camera_info,
                           const Eigen::Matrix4f& camera_pose);

    bool processRGBDWithGPU(const sensor_msgs::Image::ConstPtr& rgb_msg,
                           const sensor_msgs::Image::ConstPtr& depth_msg,
                           const sensor_msgs::CameraInfo::ConstPtr& camera_info,
                           const Eigen::Matrix4f& camera_pose);

    void updateTSDFVoxelsAroundPoint(const Eigen::Vector3f& surface_point,
                                   float depth,
                                   const cv::Vec3b& color,
                                   const Eigen::Matrix4f& camera_pose,
                                   const Eigen::Vector3f& surface_normal);

    void updateTSDFVoxel(const VoxelIndex& voxel_idx, float sdf_value,
                        float weight, const cv::Vec3b& color);

    VoxelIndex worldToVoxel(const Eigen::Vector3f& world_pos) const;
    Eigen::Vector3f voxelToWorld(const VoxelIndex& voxel_idx) const;

    bool getCurrentCameraPose(Eigen::Matrix4f& camera_pose, const ros::Time& timestamp);

    Eigen::Vector3f computeSurfaceNormal(int u, int v, const cv::Mat& depth_image,
                                       const image_geometry::PinholeCameraModel& camera_model,
                                       const Eigen::Matrix4f& camera_pose) const;

    float calculateAdaptiveWeight(float sdf_value, float depth) const;

    void initializeDepthPointcloudTransform();
    void initializeQualityControl();
    void validateCoordinateConfiguration();
    void initializeRTABMapCollaboration();

    bool initializeGPUAcceleration();
    void transferGPUVoxelsToCPU();
    void cleanupGPUResources();

    void updateDynamicVolumeOrigin(const Eigen::Matrix4f& camera_pose);

    void rtabmapOdomCallback(const nav_msgs::Odometry::ConstPtr& msg);

    void publishPointCloud();
    void publishVoxelMarkers();
    void publishPerformanceStats();

    void buildTopologicalConnections();
    bool validateSurfaceContinuity(const VoxelIndex& voxel_idx) const;
    void repairSurfaceGap(const VoxelIndex& voxel_idx);

    int classifyCurvatureLevel(float curvature) const;
    void updateSingleScaleVoxels(const Eigen::Vector3f& surface_point, float depth,
                               const cv::Vec3b& color, const Eigen::Matrix4f& camera_pose,
                               const Eigen::Vector3f& surface_normal);
    void updateMultiScaleVoxels(const Eigen::Vector3f& surface_point, float depth,
                              const cv::Vec3b& color, const Eigen::Matrix4f& camera_pose,
                              const Eigen::Vector3f& surface_normal);
};

} // namespace tsdf_mapping

#endif // TSDF_MAPPING_TSDF_FUSION_H
```

## 6. GPU TSDF头文件 (tsdf_cuda.h)

```cpp
#ifndef TSDF_MAPPING_TSDF_CUDA_H
#define TSDF_MAPPING_TSDF_CUDA_H

#include <opencv2/opencv.hpp>
#include <Eigen/Dense>
#include <vector>
#include <memory>
#include <chrono>
#include <cuda_runtime.h>
#include <thrust/device_vector.h>

struct CudaTSDFParams {
    float voxel_size;
    float truncation_distance;
    float max_weight;
    int volume_size_x;
    int volume_size_y;
    int volume_size_z;
    float volume_origin_x;
    float volume_origin_y;
    float volume_origin_z;

    CudaTSDFParams() :
        voxel_size(0.05f),
        truncation_distance(0.3f),
        max_weight(100.0f),
        volume_size_x(200),
        volume_size_y(200),
        volume_size_z(200),
        volume_origin_x(-5.0f),
        volume_origin_y(-5.0f),
        volume_origin_z(-5.0f) {}
};

struct CudaCameraParams {
    float fx, fy, cx, cy;
    int width, height;

    CudaCameraParams() :
        fx(525.0f), fy(525.0f),
        cx(320.0f), cy(240.0f),
        width(640), height(480) {}
};

struct CudaPerformanceStats {
    float gpu_process_time_ms;
    int processed_frames;
    float average_fps;

    CudaPerformanceStats() :
        gpu_process_time_ms(0.0f),
        processed_frames(0),
        average_fps(0.0f) {}
};

struct CudaVoxel;

class TSDFCuda {
public:
    TSDFCuda();
    ~TSDFCuda();

    bool initialize(const CudaTSDFParams& params, const CudaCameraParams& camera_params);

    bool processRGBD(const cv::Mat& rgb_image,
                     const cv::Mat& depth_image,
                     const Eigen::Matrix4f& camera_pose);

    std::vector<CudaVoxel> getVoxelData() const;

    void setMigrationDistanceThreshold(float threshold) {
        migration_distance_threshold_ = threshold;
    }

    bool checkGPUHealth() const;
    bool attemptGPURecovery();

    void getMemoryUsage(size_t& used_mem, size_t& total_mem) const;
    CudaPerformanceStats getPerformanceStats() const;

private:
    bool initialized_;
    CudaTSDFParams params_;
    CudaCameraParams camera_params_;

    thrust::device_vector<CudaVoxel> d_voxels_;
    thrust::device_vector<float> d_depth_image_;
    thrust::device_vector<uint8_t> d_rgb_image_;
    thrust::device_vector<float> d_camera_pose_;

    cudaEvent_t start_event_;
    cudaEvent_t stop_event_;
    cudaStream_t compute_stream_;
    cudaStream_t memory_stream_;

    float migration_distance_threshold_;
    CudaPerformanceStats perf_stats_;
    std::chrono::steady_clock::time_point start_time_;

    void cleanup();
};

#endif // TSDF_MAPPING_TSDF_CUDA_H
```

## 7. CMakeLists.txt配置文件

```cmake
cmake_minimum_required(VERSION 3.0.2)
project(tsdf_mapping)

enable_language(CUDA)
set(CMAKE_CUDA_STANDARD 14)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

add_compile_options(-std=c++14)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  sensor_msgs
  geometry_msgs
  visualization_msgs
  tf2
  tf2_ros
  pcl_ros
  pcl_conversions
  cv_bridge
  image_transport
  image_geometry
  grid_map_ros
  grid_map_core
  grid_map_msgs
  octomap_ros
  octomap_msgs
  dynamic_reconfigure
)

find_package(OpenCV REQUIRED)
find_package(PCL REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(Boost REQUIRED COMPONENTS system thread)

find_package(CUDA REQUIRED)
if(CUDA_FOUND)
    enable_language(CUDA)
    set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -arch=sm_86")
    add_definitions(-DWITH_CUDA)
endif()

catkin_package(
  INCLUDE_DIRS include
  LIBRARIES ${PROJECT_NAME}
  CATKIN_DEPENDS
    roscpp rospy std_msgs sensor_msgs geometry_msgs visualization_msgs
    tf2 tf2_ros pcl_ros pcl_conversions cv_bridge image_transport
    image_geometry grid_map_ros grid_map_core grid_map_msgs
    octomap_ros octomap_msgs dynamic_reconfigure
  DEPENDS OpenCV PCL Eigen3 Boost
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
  ${OpenCV_INCLUDE_DIRS}
  ${PCL_INCLUDE_DIRS}
  ${Eigen3_INCLUDE_DIRS}
  ${Boost_INCLUDE_DIRS}
  ${CUDA_INCLUDE_DIRS}
)

add_library(${PROJECT_NAME}
  src/tsdf_fusion.cpp
)

if(CUDA_FOUND)
    add_library(tsdf_cuda
        src/tsdf_cuda.cu
    )
    set_target_properties(tsdf_cuda PROPERTIES
        CUDA_SEPARABLE_COMPILATION ON
        CUDA_RESOLVE_DEVICE_SYMBOLS ON
    )
    target_link_libraries(tsdf_cuda ${CUDA_LIBRARIES})
endif()

target_link_libraries(${PROJECT_NAME}
  ${catkin_LIBRARIES}
  ${OpenCV_LIBRARIES}
  ${PCL_LIBRARIES}
  ${Boost_LIBRARIES}
)

if(CUDA_FOUND)
    target_link_libraries(${PROJECT_NAME} tsdf_cuda)
endif()

add_executable(tsdf_fusion_node src/tsdf_fusion_node.cpp)
target_link_libraries(tsdf_fusion_node ${PROJECT_NAME})

add_executable(pose_subscription_center src/pose_subscription_center.cpp)
target_link_libraries(pose_subscription_center ${catkin_LIBRARIES})

add_executable(gpu_monitor_node src/gpu_monitor_node.cpp)
target_link_libraries(gpu_monitor_node ${catkin_LIBRARIES})

install(TARGETS ${PROJECT_NAME} tsdf_fusion_node pose_subscription_center
  ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

install(DIRECTORY include/${PROJECT_NAME}/
  DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
  FILES_MATCHING PATTERN "*.h"
)

install(DIRECTORY launch/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
  FILES_MATCHING PATTERN "*.launch"
)

install(DIRECTORY config/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/config
  FILES_MATCHING PATTERN "*.rviz" PATTERN "*.yaml"
)
```

## 8. Package.xml配置文件

```xml
<?xml version="1.0"?>
<package format="2">
  <name>tsdf_mapping</name>
  <version>1.0.0</version>
  <description>TSDF-RTAB-Map算法级融合建图系统</description>

  <maintainer email="<EMAIL>">开发团队</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>

  <depend>roscpp</depend>
  <depend>rospy</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>visualization_msgs</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>pcl_ros</depend>
  <depend>pcl_conversions</depend>
  <depend>cv_bridge</depend>
  <depend>image_transport</depend>
  <depend>image_geometry</depend>
  <depend>grid_map_ros</depend>
  <depend>grid_map_core</depend>
  <depend>grid_map_msgs</depend>
  <depend>octomap_ros</depend>
  <depend>octomap_msgs</depend>
  <depend>dynamic_reconfigure</depend>

  <build_depend>libopencv-dev</build_depend>
  <build_depend>libpcl-all-dev</build_depend>
  <build_depend>libeigen3-dev</build_depend>
  <build_depend>libboost-all-dev</build_depend>

  <exec_depend>libopencv-dev</exec_depend>
  <exec_depend>libpcl-all-dev</exec_depend>
  <exec_depend>libeigen3-dev</exec_depend>
  <exec_depend>libboost-all-dev</exec_depend>

  <export>
  </export>
</package>
```

## 9. Launch文件配置 (tsdf_mapping.launch)

```xml
<?xml version="1.0"?>
<launch>
  <arg name="use_sim_time" default="true"/>
  <arg name="enable_gpu_acceleration" default="true"/>
  <arg name="voxel_size" default="0.05"/>
  <arg name="truncation_distance" default="0.3"/>
  <arg name="max_weight" default="100.0"/>
  <arg name="use_rtabmap_pose" default="true"/>
  <arg name="enable_rtabmap_collaboration" default="true"/>
  <arg name="rgb_topic" default="/camera/color/image_raw"/>
  <arg name="depth_topic" default="/camera/aligned_depth_to_color/image_raw"/>
  <arg name="camera_info_topic" default="/camera/color/camera_info"/>
  <arg name="source_frame" default="map"/>
  <arg name="target_frame" default="base_link"/>
  <arg name="camera_frame" default="zed_left_camera_optical_frame"/>
  <arg name="publish_rate" default="30.0"/>
  <arg name="enable_coordinate_correction" default="true"/>
  <arg name="apply_optical_to_mechanical_transform" default="true"/>
  <arg name="use_odom_backup" default="true"/>
  <arg name="enable_pose_filtering" default="false"/>
  <arg name="pose_quality_threshold" default="0.1"/>

  <param name="use_sim_time" value="$(arg use_sim_time)"/>

  <node name="pose_subscription_center" pkg="tsdf_mapping" type="pose_subscription_center" output="screen">
    <param name="source_frame" value="$(arg source_frame)"/>
    <param name="target_frame" value="$(arg target_frame)"/>
    <param name="camera_frame" value="$(arg camera_frame)"/>
    <param name="publish_rate" value="$(arg publish_rate)"/>
    <param name="enable_coordinate_correction" value="$(arg enable_coordinate_correction)"/>
    <param name="apply_optical_to_mechanical_transform" value="$(arg apply_optical_to_mechanical_transform)"/>
    <param name="use_odom_backup" value="$(arg use_odom_backup)"/>
    <param name="enable_pose_filtering" value="$(arg enable_pose_filtering)"/>
    <param name="pose_quality_threshold" value="$(arg pose_quality_threshold)"/>
  </node>

  <node name="tsdf_fusion_node" pkg="tsdf_mapping" type="tsdf_fusion_node" output="screen">
    <param name="voxel_size" value="$(arg voxel_size)"/>
    <param name="truncation_distance" value="$(arg truncation_distance)"/>
    <param name="max_weight" value="$(arg max_weight)"/>
    <param name="use_rtabmap_pose" value="$(arg use_rtabmap_pose)"/>
    <param name="enable_rtabmap_collaboration" value="$(arg enable_rtabmap_collaboration)"/>
    <param name="enable_gpu_acceleration" value="$(arg enable_gpu_acceleration)"/>
    <param name="rgb_topic" value="$(arg rgb_topic)"/>
    <param name="depth_topic" value="$(arg depth_topic)"/>
    <param name="camera_info_topic" value="$(arg camera_info_topic)"/>
    <param name="use_manual_sync" value="true"/>
    <param name="sync_timeout" value="0.1"/>
    <param name="use_exact_sync" value="false"/>
  </node>

  <node name="gpu_monitor_node" pkg="tsdf_mapping" type="gpu_monitor_node" output="screen">
    <param name="monitor_interval" value="1.0"/>
    <param name="memory_threshold" value="0.9"/>
    <param name="temperature_threshold" value="80"/>
  </node>

</launch>
```

## 10. 多尺度TSDF实现 (multi_scale_tsdf.cpp)

```cpp
#include "tsdf_mapping/multi_scale_tsdf.h"
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include <cv_bridge/cv_bridge.h>
#include <image_geometry/pinhole_camera_model.h>

namespace tsdf_mapping {

MultiScaleTSDF::MultiScaleTSDF(ros::NodeHandle& nh) : nh_(nh) {
    nh_.param<double>("base_voxel_size", base_voxel_size_, 0.05);
    nh_.param<int>("num_scales", num_scales_, 3);
    nh_.param<double>("scale_factor", scale_factor_, 2.0);
    nh_.param<double>("truncation_distance", truncation_distance_, 0.3);
    nh_.param<double>("max_weight", max_weight_, 100.0);

    initializeScales();

    pointcloud_pub_ = nh_.advertise<sensor_msgs::PointCloud2>("multi_scale_pointcloud", 1);
    marker_pub_ = nh_.advertise<visualization_msgs::MarkerArray>("multi_scale_markers", 1);
}

void MultiScaleTSDF::initializeScales() {
    scales_.clear();

    for (int i = 0; i < num_scales_; ++i) {
        TSDFScale scale;
        scale.voxel_size = base_voxel_size_ * std::pow(scale_factor_, i);
        scale.truncation_distance = truncation_distance_ * std::pow(scale_factor_, i);
        scale.level = i;

        scales_.push_back(scale);
    }
}

void MultiScaleTSDF::processRGBD(const cv::Mat& rgb_image, const cv::Mat& depth_image,
                                const sensor_msgs::CameraInfo& camera_info,
                                const Eigen::Matrix4f& camera_pose) {

    image_geometry::PinholeCameraModel camera_model;
    camera_model.fromCameraInfo(camera_info);

    for (auto& scale : scales_) {
        processScaleLevel(rgb_image, depth_image, camera_model, camera_pose, scale);
    }

    fuseMultiScaleResults();
}

void MultiScaleTSDF::processScaleLevel(const cv::Mat& rgb_image, const cv::Mat& depth_image,
                                     const image_geometry::PinholeCameraModel& camera_model,
                                     const Eigen::Matrix4f& camera_pose,
                                     TSDFScale& scale) {

    int step_size = static_cast<int>(scale.voxel_size / base_voxel_size_);
    step_size = std::max(1, step_size);

    for (int v = 0; v < depth_image.rows; v += step_size) {
        for (int u = 0; u < depth_image.cols; u += step_size) {
            uint16_t depth_raw = depth_image.at<uint16_t>(v, u);
            if (depth_raw == 0) continue;

            float depth = depth_raw * 0.001f;
            if (depth < 0.3f || depth > 10.0f) continue;

            cv::Point3d point_3d = camera_model.projectPixelTo3dRay(cv::Point2d(u, v));
            point_3d.x *= depth;
            point_3d.y *= depth;
            point_3d.z = depth;

            Eigen::Vector4f point_camera(point_3d.x, point_3d.y, point_3d.z, 1.0f);
            Eigen::Vector4f point_world = camera_pose * point_camera;
            Eigen::Vector3f surface_point = point_world.head<3>();

            cv::Vec3b color = rgb_image.at<cv::Vec3b>(v, u);

            updateTSDFVoxelsAtScale(surface_point, depth, color, camera_pose, scale);
        }
    }
}

void MultiScaleTSDF::updateTSDFVoxelsAtScale(const Eigen::Vector3f& surface_point,
                                           float depth,
                                           const cv::Vec3b& color,
                                           const Eigen::Matrix4f& camera_pose,
                                           TSDFScale& scale) {

    Eigen::Vector3f camera_position = camera_pose.block<3, 1>(0, 3);
    VoxelIndex center_voxel = worldToVoxel(surface_point, scale.voxel_size);

    int radius = static_cast<int>(std::ceil(scale.truncation_distance / scale.voxel_size)) + 1;

    for (int dx = -radius; dx <= radius; dx++) {
        for (int dy = -radius; dy <= radius; dy++) {
            for (int dz = -radius; dz <= radius; dz++) {
                VoxelIndex voxel_idx(center_voxel.x + dx, center_voxel.y + dy, center_voxel.z + dz);
                Eigen::Vector3f voxel_center = voxelToWorld(voxel_idx, scale.voxel_size);

                Eigen::Vector3f ray_direction = (surface_point - camera_position).normalized();
                float distance_along_ray = (voxel_center - camera_position).dot(ray_direction);
                float sdf = distance_along_ray - depth;

                if (std::abs(sdf) <= scale.truncation_distance) {
                    sdf = std::max(-scale.truncation_distance, std::min(scale.truncation_distance, sdf));
                    sdf /= scale.truncation_distance;

                    float weight = calculateScaleWeight(sdf, depth, scale.level);
                    updateVoxelAtScale(voxel_idx, sdf, weight, color, scale);
                }
            }
        }
    }
}

void MultiScaleTSDF::updateVoxelAtScale(const VoxelIndex& voxel_idx, float sdf_value,
                                      float weight, const cv::Vec3b& color,
                                      TSDFScale& scale) {

    auto& volume = scale.tsdf_volume;
    auto it = volume.find(voxel_idx);

    if (it == volume.end()) {
        TSDFVoxel voxel;
        voxel.tsdf_value = sdf_value;
        voxel.weight = weight;
        voxel.r = color[2];
        voxel.g = color[1];
        voxel.b = color[0];
        volume[voxel_idx] = voxel;
    } else {
        TSDFVoxel& voxel = it->second;

        float scale_weight_decay = 0.95f - 0.05f * scale.level;
        voxel.weight *= scale_weight_decay;

        float total_weight = voxel.weight + weight;
        if (total_weight > max_weight_) {
            float scale_factor = max_weight_ / total_weight;
            voxel.weight *= scale_factor;
            weight *= scale_factor;
            total_weight = max_weight_;
        }

        voxel.tsdf_value = (voxel.tsdf_value * voxel.weight + sdf_value * weight) / total_weight;
        voxel.weight = total_weight;

        voxel.r = static_cast<uint8_t>((voxel.r * voxel.weight + color[2] * weight) / total_weight);
        voxel.g = static_cast<uint8_t>((voxel.g * voxel.weight + color[1] * weight) / total_weight);
        voxel.b = static_cast<uint8_t>((voxel.b * voxel.weight + color[0] * weight) / total_weight);
    }
}

float MultiScaleTSDF::calculateScaleWeight(float sdf_value, float depth, int scale_level) const {
    float base_weight = 1.0f;

    if (std::abs(sdf_value) < 0.1f) {
        base_weight = 2.0f;
    }

    float depth_weight = 1.0f / (1.0f + depth * 0.1f);

    float scale_weight = 1.0f / (1.0f + scale_level * 0.2f);

    return base_weight * depth_weight * scale_weight;
}

void MultiScaleTSDF::fuseMultiScaleResults() {
    fused_volume_.clear();

    for (const auto& scale : scales_) {
        for (const auto& pair : scale.tsdf_volume) {
            const VoxelIndex& idx = pair.first;
            const TSDFVoxel& voxel = pair.second;

            VoxelIndex base_idx = scaleVoxelIndex(idx, scale.voxel_size, base_voxel_size_);

            auto it = fused_volume_.find(base_idx);
            if (it == fused_volume_.end()) {
                fused_volume_[base_idx] = voxel;
            } else {
                TSDFVoxel& existing_voxel = it->second;

                float total_weight = existing_voxel.weight + voxel.weight;
                if (total_weight > 0) {
                    existing_voxel.tsdf_value = (existing_voxel.tsdf_value * existing_voxel.weight +
                                               voxel.tsdf_value * voxel.weight) / total_weight;

                    existing_voxel.r = static_cast<uint8_t>((existing_voxel.r * existing_voxel.weight +
                                                           voxel.r * voxel.weight) / total_weight);
                    existing_voxel.g = static_cast<uint8_t>((existing_voxel.g * existing_voxel.weight +
                                                           voxel.g * voxel.weight) / total_weight);
                    existing_voxel.b = static_cast<uint8_t>((existing_voxel.b * existing_voxel.weight +
                                                           voxel.b * voxel.weight) / total_weight);

                    existing_voxel.weight = std::min(total_weight, max_weight_);
                }
            }
        }
    }
}

VoxelIndex MultiScaleTSDF::scaleVoxelIndex(const VoxelIndex& idx, float from_voxel_size, float to_voxel_size) const {
    float scale_ratio = from_voxel_size / to_voxel_size;

    return VoxelIndex(
        static_cast<int>(idx.x * scale_ratio),
        static_cast<int>(idx.y * scale_ratio),
        static_cast<int>(idx.z * scale_ratio)
    );
}

VoxelIndex MultiScaleTSDF::worldToVoxel(const Eigen::Vector3f& world_pos, float voxel_size) const {
    return VoxelIndex(
        static_cast<int>(std::floor(world_pos.x() / voxel_size)),
        static_cast<int>(std::floor(world_pos.y() / voxel_size)),
        static_cast<int>(std::floor(world_pos.z() / voxel_size))
    );
}

Eigen::Vector3f MultiScaleTSDF::voxelToWorld(const VoxelIndex& voxel_idx, float voxel_size) const {
    return Eigen::Vector3f(
        (voxel_idx.x + 0.5f) * voxel_size,
        (voxel_idx.y + 0.5f) * voxel_size,
        (voxel_idx.z + 0.5f) * voxel_size
    );
}

void MultiScaleTSDF::publishVisualization() {
    publishMultiScalePointCloud();
    publishScaleMarkers();
}

void MultiScaleTSDF::publishMultiScalePointCloud() {
    pcl::PointCloud<pcl::PointXYZRGB> cloud;

    for (const auto& pair : fused_volume_) {
        const VoxelIndex& idx = pair.first;
        const TSDFVoxel& voxel = pair.second;

        if (voxel.weight > 1.0f && std::abs(voxel.tsdf_value) < 0.1f) {
            Eigen::Vector3f world_pos = voxelToWorld(idx, base_voxel_size_);

            pcl::PointXYZRGB point;
            point.x = world_pos.x();
            point.y = world_pos.y();
            point.z = world_pos.z();
            point.r = voxel.r;
            point.g = voxel.g;
            point.b = voxel.b;

            cloud.points.push_back(point);
        }
    }

    cloud.width = cloud.points.size();
    cloud.height = 1;
    cloud.is_dense = false;

    sensor_msgs::PointCloud2 cloud_msg;
    pcl::toROSMsg(cloud, cloud_msg);
    cloud_msg.header.frame_id = "map";
    cloud_msg.header.stamp = ros::Time::now();

    pointcloud_pub_.publish(cloud_msg);
}

void MultiScaleTSDF::publishScaleMarkers() {
    visualization_msgs::MarkerArray marker_array;

    for (size_t i = 0; i < scales_.size(); ++i) {
        const auto& scale = scales_[i];

        visualization_msgs::Marker marker;
        marker.header.frame_id = "map";
        marker.header.stamp = ros::Time::now();
        marker.ns = "tsdf_scales";
        marker.id = i;
        marker.type = visualization_msgs::Marker::TEXT_VIEW_FACING;
        marker.action = visualization_msgs::Marker::ADD;

        marker.pose.position.x = i * 2.0;
        marker.pose.position.y = 0.0;
        marker.pose.position.z = 2.0;
        marker.pose.orientation.w = 1.0;

        marker.scale.z = 0.2;

        marker.color.r = 1.0;
        marker.color.g = 1.0;
        marker.color.b = 1.0;
        marker.color.a = 1.0;

        std::stringstream ss;
        ss << "Scale " << i << "\nVoxel: " << scale.voxel_size << "m\nVoxels: " << scale.tsdf_volume.size();
        marker.text = ss.str();

        marker_array.markers.push_back(marker);
    }

    marker_pub_.publish(marker_array);
}

std::vector<TSDFVoxel> MultiScaleTSDF::extractSurfaceVoxels(float iso_value) const {
    std::vector<TSDFVoxel> surface_voxels;

    for (const auto& pair : fused_volume_) {
        const TSDFVoxel& voxel = pair.second;

        if (voxel.weight > 1.0f && std::abs(voxel.tsdf_value - iso_value) < 0.05f) {
            surface_voxels.push_back(voxel);
        }
    }

    return surface_voxels;
}

void MultiScaleTSDF::clearScale(int scale_level) {
    if (scale_level >= 0 && scale_level < scales_.size()) {
        scales_[scale_level].tsdf_volume.clear();
    }
}

void MultiScaleTSDF::clearAllScales() {
    for (auto& scale : scales_) {
        scale.tsdf_volume.clear();
    }
    fused_volume_.clear();
}

size_t MultiScaleTSDF::getTotalVoxelCount() const {
    size_t total = 0;
    for (const auto& scale : scales_) {
        total += scale.tsdf_volume.size();
    }
    return total;
}

float MultiScaleTSDF::getMemoryUsage() const {
    size_t total_voxels = getTotalVoxelCount() + fused_volume_.size();
    return total_voxels * sizeof(TSDFVoxel) / (1024.0f * 1024.0f);
}

} // namespace tsdf_mapping
```

## 11. 体素网格管理器 (voxel_grid_manager.cpp)

```cpp
#include "tsdf_mapping/voxel_grid_manager.h"
#include <algorithm>
#include <cmath>
#include <fstream>
#include <boost/archive/binary_oarchive.hpp>
#include <boost/archive/binary_iarchive.hpp>
#include <boost/serialization/unordered_map.hpp>

namespace tsdf_mapping {

VoxelGridManager::VoxelGridManager(float voxel_size, float max_distance)
    : voxel_size_(voxel_size), max_distance_(max_distance),
      grid_size_(static_cast<int>(2 * max_distance / voxel_size)) {

    origin_ = Eigen::Vector3f(-max_distance, -max_distance, -max_distance);
    voxel_grids_.resize(grid_size_ * grid_size_ * grid_size_);

    for (int i = 0; i < voxel_grids_.size(); ++i) {
        voxel_grids_[i] = std::make_shared<VoxelGrid>();
    }
}

VoxelGridManager::~VoxelGridManager() {
    voxel_grids_.clear();
}

bool VoxelGridManager::insertVoxel(const Eigen::Vector3f& position, const TSDFVoxel& voxel) {
    GridIndex grid_idx = worldToGridIndex(position);
    if (!isValidGridIndex(grid_idx)) {
        return false;
    }

    int linear_idx = gridIndexToLinear(grid_idx);
    auto& grid = voxel_grids_[linear_idx];

    VoxelIndex voxel_idx = worldToVoxelIndex(position);
    grid->voxels[voxel_idx] = voxel;

    return true;
}

bool VoxelGridManager::getVoxel(const Eigen::Vector3f& position, TSDFVoxel& voxel) const {
    GridIndex grid_idx = worldToGridIndex(position);
    if (!isValidGridIndex(grid_idx)) {
        return false;
    }

    int linear_idx = gridIndexToLinear(grid_idx);
    const auto& grid = voxel_grids_[linear_idx];

    VoxelIndex voxel_idx = worldToVoxelIndex(position);
    auto it = grid->voxels.find(voxel_idx);

    if (it != grid->voxels.end()) {
        voxel = it->second;
        return true;
    }

    return false;
}

bool VoxelGridManager::removeVoxel(const Eigen::Vector3f& position) {
    GridIndex grid_idx = worldToGridIndex(position);
    if (!isValidGridIndex(grid_idx)) {
        return false;
    }

    int linear_idx = gridIndexToLinear(grid_idx);
    auto& grid = voxel_grids_[linear_idx];

    VoxelIndex voxel_idx = worldToVoxelIndex(position);
    auto it = grid->voxels.find(voxel_idx);

    if (it != grid->voxels.end()) {
        grid->voxels.erase(it);
        return true;
    }

    return false;
}

std::vector<TSDFVoxel> VoxelGridManager::getVoxelsInRadius(const Eigen::Vector3f& center, float radius) const {
    std::vector<TSDFVoxel> result;

    int grid_radius = static_cast<int>(std::ceil(radius / (voxel_size_ * GRID_SIZE)));
    GridIndex center_grid = worldToGridIndex(center);

    for (int dx = -grid_radius; dx <= grid_radius; ++dx) {
        for (int dy = -grid_radius; dy <= grid_radius; ++dy) {
            for (int dz = -grid_radius; dz <= grid_radius; ++dz) {
                GridIndex grid_idx(center_grid.x + dx, center_grid.y + dy, center_grid.z + dz);

                if (isValidGridIndex(grid_idx)) {
                    int linear_idx = gridIndexToLinear(grid_idx);
                    const auto& grid = voxel_grids_[linear_idx];

                    for (const auto& pair : grid->voxels) {
                        Eigen::Vector3f voxel_pos = voxelIndexToWorld(pair.first);
                        float distance = (voxel_pos - center).norm();

                        if (distance <= radius) {
                            result.push_back(pair.second);
                        }
                    }
                }
            }
        }
    }

    return result;
}

void VoxelGridManager::updateVoxelGrid(const Eigen::Vector3f& camera_position) {
    current_camera_position_ = camera_position;

    GridIndex camera_grid = worldToGridIndex(camera_position);

    for (int x = 0; x < grid_size_; ++x) {
        for (int y = 0; y < grid_size_; ++y) {
            for (int z = 0; z < grid_size_; ++z) {
                GridIndex grid_idx(x, y, z);
                float distance = gridDistance(camera_grid, grid_idx);

                int linear_idx = gridIndexToLinear(grid_idx);
                auto& grid = voxel_grids_[linear_idx];

                if (distance > MAX_GRID_DISTANCE) {
                    if (grid->is_active) {
                        saveGridToDisk(grid_idx, grid);
                        grid->voxels.clear();
                        grid->is_active = false;
                    }
                } else {
                    if (!grid->is_active) {
                        loadGridFromDisk(grid_idx, grid);
                        grid->is_active = true;
                    }
                }
            }
        }
    }
}

void VoxelGridManager::saveGridToDisk(const GridIndex& grid_idx, std::shared_ptr<VoxelGrid> grid) {
    if (grid->voxels.empty()) {
        return;
    }

    std::string filename = generateGridFilename(grid_idx);
    std::ofstream ofs(filename, std::ios::binary);

    if (ofs.is_open()) {
        boost::archive::binary_oarchive oa(ofs);
        oa << grid->voxels;
        ofs.close();

        grid->is_saved_to_disk = true;
        grid->disk_filename = filename;
    }
}

void VoxelGridManager::loadGridFromDisk(const GridIndex& grid_idx, std::shared_ptr<VoxelGrid> grid) {
    std::string filename = generateGridFilename(grid_idx);
    std::ifstream ifs(filename, std::ios::binary);

    if (ifs.is_open()) {
        boost::archive::binary_iarchive ia(ifs);
        ia >> grid->voxels;
        ifs.close();

        grid->is_saved_to_disk = false;
    }
}

std::string VoxelGridManager::generateGridFilename(const GridIndex& grid_idx) const {
    std::stringstream ss;
    ss << "grid_" << grid_idx.x << "_" << grid_idx.y << "_" << grid_idx.z << ".bin";
    return ss.str();
}

GridIndex VoxelGridManager::worldToGridIndex(const Eigen::Vector3f& position) const {
    Eigen::Vector3f relative_pos = position - origin_;

    return GridIndex(
        static_cast<int>(std::floor(relative_pos.x() / (voxel_size_ * GRID_SIZE))),
        static_cast<int>(std::floor(relative_pos.y() / (voxel_size_ * GRID_SIZE))),
        static_cast<int>(std::floor(relative_pos.z() / (voxel_size_ * GRID_SIZE)))
    );
}

VoxelIndex VoxelGridManager::worldToVoxelIndex(const Eigen::Vector3f& position) const {
    Eigen::Vector3f relative_pos = position - origin_;

    return VoxelIndex(
        static_cast<int>(std::floor(relative_pos.x() / voxel_size_)),
        static_cast<int>(std::floor(relative_pos.y() / voxel_size_)),
        static_cast<int>(std::floor(relative_pos.z() / voxel_size_))
    );
}

Eigen::Vector3f VoxelGridManager::voxelIndexToWorld(const VoxelIndex& voxel_idx) const {
    return origin_ + Eigen::Vector3f(
        (voxel_idx.x + 0.5f) * voxel_size_,
        (voxel_idx.y + 0.5f) * voxel_size_,
        (voxel_idx.z + 0.5f) * voxel_size_
    );
}

bool VoxelGridManager::isValidGridIndex(const GridIndex& grid_idx) const {
    return grid_idx.x >= 0 && grid_idx.x < grid_size_ &&
           grid_idx.y >= 0 && grid_idx.y < grid_size_ &&
           grid_idx.z >= 0 && grid_idx.z < grid_size_;
}

int VoxelGridManager::gridIndexToLinear(const GridIndex& grid_idx) const {
    return grid_idx.x * grid_size_ * grid_size_ + grid_idx.y * grid_size_ + grid_idx.z;
}

GridIndex VoxelGridManager::linearToGridIndex(int linear_idx) const {
    int z = linear_idx % grid_size_;
    int y = (linear_idx / grid_size_) % grid_size_;
    int x = linear_idx / (grid_size_ * grid_size_);

    return GridIndex(x, y, z);
}

float VoxelGridManager::gridDistance(const GridIndex& a, const GridIndex& b) const {
    return std::sqrt(
        std::pow(a.x - b.x, 2) +
        std::pow(a.y - b.y, 2) +
        std::pow(a.z - b.z, 2)
    );
}

size_t VoxelGridManager::getTotalVoxelCount() const {
    size_t total = 0;

    for (const auto& grid : voxel_grids_) {
        if (grid->is_active) {
            total += grid->voxels.size();
        }
    }

    return total;
}

size_t VoxelGridManager::getActiveGridCount() const {
    size_t count = 0;

    for (const auto& grid : voxel_grids_) {
        if (grid->is_active) {
            count++;
        }
    }

    return count;
}

float VoxelGridManager::getMemoryUsage() const {
    size_t total_voxels = getTotalVoxelCount();
    size_t grid_overhead = voxel_grids_.size() * sizeof(VoxelGrid);

    return (total_voxels * sizeof(TSDFVoxel) + grid_overhead) / (1024.0f * 1024.0f);
}

void VoxelGridManager::optimizeMemory() {
    for (auto& grid : voxel_grids_) {
        if (grid->is_active && !grid->voxels.empty()) {
            auto it = grid->voxels.begin();
            while (it != grid->voxels.end()) {
                if (it->second.weight < 0.1f) {
                    it = grid->voxels.erase(it);
                } else {
                    ++it;
                }
            }
        }
    }
}

void VoxelGridManager::clearAll() {
    for (auto& grid : voxel_grids_) {
        grid->voxels.clear();
        grid->is_active = false;
        grid->is_saved_to_disk = false;
        grid->disk_filename.clear();
    }
}

std::vector<Eigen::Vector3f> VoxelGridManager::extractSurfacePoints(float iso_value) const {
    std::vector<Eigen::Vector3f> surface_points;

    for (const auto& grid : voxel_grids_) {
        if (grid->is_active) {
            for (const auto& pair : grid->voxels) {
                const TSDFVoxel& voxel = pair.second;

                if (voxel.weight > 1.0f && std::abs(voxel.tsdf_value - iso_value) < 0.05f) {
                    Eigen::Vector3f world_pos = voxelIndexToWorld(pair.first);
                    surface_points.push_back(world_pos);
                }
            }
        }
    }

    return surface_points;
}

bool VoxelGridManager::saveToFile(const std::string& filename) const {
    std::ofstream ofs(filename, std::ios::binary);
    if (!ofs.is_open()) {
        return false;
    }

    boost::archive::binary_oarchive oa(ofs);

    oa << voxel_size_;
    oa << max_distance_;
    oa << grid_size_;
    oa << origin_;

    size_t active_grid_count = getActiveGridCount();
    oa << active_grid_count;

    for (int i = 0; i < voxel_grids_.size(); ++i) {
        const auto& grid = voxel_grids_[i];
        if (grid->is_active && !grid->voxels.empty()) {
            GridIndex grid_idx = linearToGridIndex(i);
            oa << grid_idx;
            oa << grid->voxels;
        }
    }

    ofs.close();
    return true;
}

bool VoxelGridManager::loadFromFile(const std::string& filename) {
    std::ifstream ifs(filename, std::ios::binary);
    if (!ifs.is_open()) {
        return false;
    }

    boost::archive::binary_iarchive ia(ifs);

    ia >> voxel_size_;
    ia >> max_distance_;
    ia >> grid_size_;
    ia >> origin_;

    voxel_grids_.clear();
    voxel_grids_.resize(grid_size_ * grid_size_ * grid_size_);

    for (int i = 0; i < voxel_grids_.size(); ++i) {
        voxel_grids_[i] = std::make_shared<VoxelGrid>();
    }

    size_t active_grid_count;
    ia >> active_grid_count;

    for (size_t i = 0; i < active_grid_count; ++i) {
        GridIndex grid_idx;
        ia >> grid_idx;

        int linear_idx = gridIndexToLinear(grid_idx);
        auto& grid = voxel_grids_[linear_idx];

        ia >> grid->voxels;
        grid->is_active = true;
    }

    ifs.close();
    return true;
}

} // namespace tsdf_mapping
```

## 12. 表面重建算法 (surface_reconstruction.cpp)

```cpp
#include "tsdf_mapping/surface_reconstruction.h"
#include <pcl/surface/marching_cubes_hoppe.h>
#include <pcl/surface/marching_cubes_rbf.h>
#include <pcl/surface/poisson.h>
#include <pcl/surface/gp3.h>
#include <pcl/features/normal_3d.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/filters/statistical_outlier_removal.h>

namespace tsdf_mapping {

SurfaceReconstruction::SurfaceReconstruction() {
    reconstruction_method_ = ReconstructionMethod::MARCHING_CUBES;
    iso_value_ = 0.0f;
    leaf_size_ = 0.01f;
    search_radius_ = 0.05f;
}

SurfaceReconstruction::~SurfaceReconstruction() {}

bool SurfaceReconstruction::reconstructSurface(const std::vector<TSDFVoxel>& voxels,
                                              const std::vector<Eigen::Vector3f>& positions,
                                              pcl::PolygonMesh& mesh) {

    if (voxels.size() != positions.size() || voxels.empty()) {
        return false;
    }

    pcl::PointCloud<pcl::PointXYZRGBNormal>::Ptr cloud = createPointCloudFromVoxels(voxels, positions);

    if (!cloud || cloud->empty()) {
        return false;
    }

    preprocessPointCloud(cloud);

    switch (reconstruction_method_) {
        case ReconstructionMethod::MARCHING_CUBES:
            return reconstructWithMarchingCubes(cloud, mesh);
        case ReconstructionMethod::POISSON:
            return reconstructWithPoisson(cloud, mesh);
        case ReconstructionMethod::GREEDY_PROJECTION:
            return reconstructWithGreedyProjection(cloud, mesh);
        default:
            return reconstructWithMarchingCubes(cloud, mesh);
    }
}

pcl::PointCloud<pcl::PointXYZRGBNormal>::Ptr SurfaceReconstruction::createPointCloudFromVoxels(
    const std::vector<TSDFVoxel>& voxels, const std::vector<Eigen::Vector3f>& positions) {

    pcl::PointCloud<pcl::PointXYZRGBNormal>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZRGBNormal>);

    for (size_t i = 0; i < voxels.size(); ++i) {
        const TSDFVoxel& voxel = voxels[i];
        const Eigen::Vector3f& pos = positions[i];

        if (voxel.weight > 1.0f && std::abs(voxel.tsdf_value) < 0.2f) {
            pcl::PointXYZRGBNormal point;
            point.x = pos.x();
            point.y = pos.y();
            point.z = pos.z();
            point.r = voxel.r;
            point.g = voxel.g;
            point.b = voxel.b;

            cloud->points.push_back(point);
        }
    }

    cloud->width = cloud->points.size();
    cloud->height = 1;
    cloud->is_dense = false;

    return cloud;
}

void SurfaceReconstruction::preprocessPointCloud(pcl::PointCloud<pcl::PointXYZRGBNormal>::Ptr cloud) {
    if (cloud->empty()) return;

    pcl::VoxelGrid<pcl::PointXYZRGBNormal> voxel_filter;
    voxel_filter.setInputCloud(cloud);
    voxel_filter.setLeafSize(leaf_size_, leaf_size_, leaf_size_);
    voxel_filter.filter(*cloud);

    pcl::StatisticalOutlierRemoval<pcl::PointXYZRGBNormal> outlier_filter;
    outlier_filter.setInputCloud(cloud);
    outlier_filter.setMeanK(50);
    outlier_filter.setStddevMulThresh(1.0);
    outlier_filter.filter(*cloud);

    estimateNormals(cloud);
}

void SurfaceReconstruction::estimateNormals(pcl::PointCloud<pcl::PointXYZRGBNormal>::Ptr cloud) {
    pcl::NormalEstimation<pcl::PointXYZRGBNormal, pcl::PointXYZRGBNormal> normal_estimation;
    pcl::search::KdTree<pcl::PointXYZRGBNormal>::Ptr tree(new pcl::search::KdTree<pcl::PointXYZRGBNormal>);

    normal_estimation.setInputCloud(cloud);
    normal_estimation.setSearchMethod(tree);
    normal_estimation.setRadiusSearch(search_radius_);
    normal_estimation.compute(*cloud);

    for (auto& point : cloud->points) {
        if (!std::isfinite(point.normal_x) || !std::isfinite(point.normal_y) || !std::isfinite(point.normal_z)) {
            point.normal_x = 0.0f;
            point.normal_y = 0.0f;
            point.normal_z = 1.0f;
        }
    }
}

bool SurfaceReconstruction::reconstructWithMarchingCubes(pcl::PointCloud<pcl::PointXYZRGBNormal>::Ptr cloud,
                                                        pcl::PolygonMesh& mesh) {

    pcl::MarchingCubesHoppe<pcl::PointXYZRGBNormal> marching_cubes;
    marching_cubes.setInputCloud(cloud);
    marching_cubes.setIsoLevel(iso_value_);
    marching_cubes.setGridResolution(50, 50, 50);
    marching_cubes.setPercentageExtendGrid(0.1f);

    try {
        marching_cubes.reconstruct(mesh);
        return !mesh.polygons.empty();
    } catch (const std::exception& e) {
        return false;
    }
}

bool SurfaceReconstruction::reconstructWithPoisson(pcl::PointCloud<pcl::PointXYZRGBNormal>::Ptr cloud,
                                                   pcl::PolygonMesh& mesh) {

    pcl::Poisson<pcl::PointXYZRGBNormal> poisson;
    poisson.setInputCloud(cloud);
    poisson.setDepth(9);
    poisson.setPointWeight(4.0f);
    poisson.setSamplesPerNode(3.0f);
    poisson.setScale(1.1f);
    poisson.setIsoDivide(8);
    poisson.setConfidence(false);
    poisson.setOutputPolygons(false);

    try {
        poisson.reconstruct(mesh);
        return !mesh.polygons.empty();
    } catch (const std::exception& e) {
        return false;
    }
}

bool SurfaceReconstruction::reconstructWithGreedyProjection(pcl::PointCloud<pcl::PointXYZRGBNormal>::Ptr cloud,
                                                           pcl::PolygonMesh& mesh) {

    pcl::search::KdTree<pcl::PointXYZRGBNormal>::Ptr tree(new pcl::search::KdTree<pcl::PointXYZRGBNormal>);
    tree->setInputCloud(cloud);

    pcl::GreedyProjectionTriangulation<pcl::PointXYZRGBNormal> gp3;
    gp3.setSearchRadius(search_radius_ * 2.0f);
    gp3.setMu(2.5);
    gp3.setMaximumNearestNeighbors(100);
    gp3.setMaximumSurfaceAngle(M_PI/4);
    gp3.setMinimumAngle(M_PI/18);
    gp3.setMaximumAngle(2*M_PI/3);
    gp3.setNormalConsistency(false);

    gp3.setInputCloud(cloud);
    gp3.setSearchMethod(tree);

    try {
        gp3.reconstruct(mesh);
        return !mesh.polygons.empty();
    } catch (const std::exception& e) {
        return false;
    }
}

bool SurfaceReconstruction::extractIsosurface(const std::unordered_map<VoxelIndex, TSDFVoxel, VoxelIndexHash>& tsdf_volume,
                                             float voxel_size, float iso_value,
                                             std::vector<Eigen::Vector3f>& vertices,
                                             std::vector<Eigen::Vector3i>& triangles) {

    vertices.clear();
    triangles.clear();

    std::unordered_map<VoxelIndex, int, VoxelIndexHash> vertex_map;
    int vertex_count = 0;

    for (const auto& pair : tsdf_volume) {
        const VoxelIndex& idx = pair.first;
        const TSDFVoxel& voxel = pair.second;

        if (voxel.weight > 1.0f) {
            extractVoxelIsosurface(idx, voxel, tsdf_volume, voxel_size, iso_value,
                                 vertices, triangles, vertex_map, vertex_count);
        }
    }

    return !vertices.empty() && !triangles.empty();
}

void SurfaceReconstruction::extractVoxelIsosurface(const VoxelIndex& voxel_idx, const TSDFVoxel& voxel,
                                                  const std::unordered_map<VoxelIndex, TSDFVoxel, VoxelIndexHash>& tsdf_volume,
                                                  float voxel_size, float iso_value,
                                                  std::vector<Eigen::Vector3f>& vertices,
                                                  std::vector<Eigen::Vector3i>& triangles,
                                                  std::unordered_map<VoxelIndex, int, VoxelIndexHash>& vertex_map,
                                                  int& vertex_count) {

    float cube_values[8];
    Eigen::Vector3f cube_positions[8];

    for (int i = 0; i < 8; ++i) {
        VoxelIndex corner_idx = voxel_idx;
        corner_idx.x += (i & 1) ? 1 : 0;
        corner_idx.y += (i & 2) ? 1 : 0;
        corner_idx.z += (i & 4) ? 1 : 0;

        auto it = tsdf_volume.find(corner_idx);
        if (it != tsdf_volume.end() && it->second.weight > 1.0f) {
            cube_values[i] = it->second.tsdf_value;
        } else {
            cube_values[i] = 1.0f;
        }

        cube_positions[i] = Eigen::Vector3f(
            corner_idx.x * voxel_size,
            corner_idx.y * voxel_size,
            corner_idx.z * voxel_size
        );
    }

    int cube_index = 0;
    for (int i = 0; i < 8; ++i) {
        if (cube_values[i] < iso_value) {
            cube_index |= (1 << i);
        }
    }

    if (cube_index == 0 || cube_index == 255) {
        return;
    }

    Eigen::Vector3f edge_vertices[12];
    bool edge_valid[12];

    for (int i = 0; i < 12; ++i) {
        edge_valid[i] = false;

        int edge_table_value = EDGE_TABLE[cube_index];
        if (edge_table_value & (1 << i)) {
            int v1 = EDGE_VERTICES[i][0];
            int v2 = EDGE_VERTICES[i][1];

            float t = (iso_value - cube_values[v1]) / (cube_values[v2] - cube_values[v1]);
            t = std::max(0.0f, std::min(1.0f, t));

            edge_vertices[i] = cube_positions[v1] + t * (cube_positions[v2] - cube_positions[v1]);
            edge_valid[i] = true;
        }
    }

    for (int i = 0; TRIANGLE_TABLE[cube_index][i] != -1; i += 3) {
        Eigen::Vector3i triangle;
        bool triangle_valid = true;

        for (int j = 0; j < 3; ++j) {
            int edge_idx = TRIANGLE_TABLE[cube_index][i + j];

            if (!edge_valid[edge_idx]) {
                triangle_valid = false;
                break;
            }

            Eigen::Vector3f vertex = edge_vertices[edge_idx];
            VoxelIndex vertex_voxel_idx(
                static_cast<int>(std::round(vertex.x() / voxel_size)),
                static_cast<int>(std::round(vertex.y() / voxel_size)),
                static_cast<int>(std::round(vertex.z() / voxel_size))
            );

            auto vertex_it = vertex_map.find(vertex_voxel_idx);
            if (vertex_it == vertex_map.end()) {
                vertices.push_back(vertex);
                vertex_map[vertex_voxel_idx] = vertex_count;
                triangle[j] = vertex_count;
                vertex_count++;
            } else {
                triangle[j] = vertex_it->second;
            }
        }

        if (triangle_valid) {
            triangles.push_back(triangle);
        }
    }
}

void SurfaceReconstruction::smoothMesh(pcl::PolygonMesh& mesh, int iterations) {
    if (mesh.polygons.empty() || iterations <= 0) {
        return;
    }

    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
    pcl::fromPCLPointCloud2(mesh.cloud, *cloud);

    for (int iter = 0; iter < iterations; ++iter) {
        std::vector<Eigen::Vector3f> new_positions(cloud->points.size(), Eigen::Vector3f::Zero());
        std::vector<int> neighbor_counts(cloud->points.size(), 0);

        for (const auto& polygon : mesh.polygons) {
            if (polygon.vertices.size() == 3) {
                for (int i = 0; i < 3; ++i) {
                    int v1 = polygon.vertices[i];
                    int v2 = polygon.vertices[(i + 1) % 3];

                    new_positions[v1] += Eigen::Vector3f(cloud->points[v2].x, cloud->points[v2].y, cloud->points[v2].z);
                    neighbor_counts[v1]++;
                }
            }
        }

        for (size_t i = 0; i < cloud->points.size(); ++i) {
            if (neighbor_counts[i] > 0) {
                new_positions[i] /= neighbor_counts[i];

                Eigen::Vector3f original(cloud->points[i].x, cloud->points[i].y, cloud->points[i].z);
                Eigen::Vector3f smoothed = 0.5f * original + 0.5f * new_positions[i];

                cloud->points[i].x = smoothed.x();
                cloud->points[i].y = smoothed.y();
                cloud->points[i].z = smoothed.z();
            }
        }
    }

    pcl::toPCLPointCloud2(*cloud, mesh.cloud);
}

void SurfaceReconstruction::decimateMesh(pcl::PolygonMesh& mesh, float reduction_factor) {
    if (mesh.polygons.empty() || reduction_factor <= 0.0f || reduction_factor >= 1.0f) {
        return;
    }

    size_t target_triangles = static_cast<size_t>(mesh.polygons.size() * (1.0f - reduction_factor));

    while (mesh.polygons.size() > target_triangles) {
        float min_edge_length = std::numeric_limits<float>::max();
        int min_edge_triangle = -1;

        pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
        pcl::fromPCLPointCloud2(mesh.cloud, *cloud);

        for (size_t i = 0; i < mesh.polygons.size(); ++i) {
            const auto& polygon = mesh.polygons[i];
            if (polygon.vertices.size() == 3) {
                for (int j = 0; j < 3; ++j) {
                    int v1 = polygon.vertices[j];
                    int v2 = polygon.vertices[(j + 1) % 3];

                    float edge_length = std::sqrt(
                        std::pow(cloud->points[v1].x - cloud->points[v2].x, 2) +
                        std::pow(cloud->points[v1].y - cloud->points[v2].y, 2) +
                        std::pow(cloud->points[v1].z - cloud->points[v2].z, 2)
                    );

                    if (edge_length < min_edge_length) {
                        min_edge_length = edge_length;
                        min_edge_triangle = i;
                    }
                }
            }
        }

        if (min_edge_triangle >= 0) {
            mesh.polygons.erase(mesh.polygons.begin() + min_edge_triangle);
        } else {
            break;
        }
    }
}

} // namespace tsdf_mapping
```
