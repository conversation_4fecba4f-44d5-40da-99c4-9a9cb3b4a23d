#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语义导航测试脚本
功能：测试语义导航系统的各项功能
作者：语义SLAM增强项目 - 阶段2扩展
版本：2.1
"""

import rospy
import time
import sys
from geometry_msgs.msg import PoseStamped, Point, Quaternion
from std_msgs.msg import Header
from semantic_navigation.srv import SetNavigationGoal, SetNavigationGoalRequest
from semantic_navigation.srv import EmergencyStop, EmergencyStopRequest

class SemanticNavigationTester:
    """语义导航测试器"""
    
    def __init__(self):
        rospy.init_node('semantic_navigation_tester', anonymous=True)
        
        # 等待服务
        self.wait_for_services()
        
        # 创建服务客户端
        self.set_goal_client = rospy.ServiceProxy('set_navigation_goal', SetNavigationGoal)
        self.emergency_stop_client = rospy.ServiceProxy('emergency_stop_service', EmergencyStop)
        
        print("🧪 语义导航测试器初始化完成")
    
    def wait_for_services(self):
        """等待服务可用"""
        print("⏳ 等待导航服务...")
        
        try:
            rospy.wait_for_service('set_navigation_goal', timeout=30)
            rospy.wait_for_service('emergency_stop_service', timeout=30)
            print("✅ 所有服务已就绪")
        except rospy.ROSException:
            print("❌ 服务等待超时，请确保导航系统正在运行")
            sys.exit(1)
    
    def create_goal_pose(self, x, y, z=0.0, yaw=0.0):
        """创建目标位姿"""
        goal = PoseStamped()
        goal.header.frame_id = "map"
        goal.header.stamp = rospy.Time.now()
        
        goal.pose.position.x = x
        goal.pose.position.y = y
        goal.pose.position.z = z
        
        # 简单的yaw角度转四元数
        goal.pose.orientation.z = yaw
        goal.pose.orientation.w = 1.0
        
        return goal
    
    def test_set_navigation_goal(self, x, y, description=""):
        """测试设置导航目标"""
        print(f"\n🎯 测试设置导航目标: ({x}, {y}) {description}")
        
        try:
            # 创建请求
            request = SetNavigationGoalRequest()
            request.goal = self.create_goal_pose(x, y)
            request.emergency_mode = False
            request.timeout = 60.0
            
            # 调用服务
            response = self.set_goal_client(request)
            
            if response.success:
                print(f"✅ 导航目标设置成功: {response.message}")
                return True
            else:
                print(f"❌ 导航目标设置失败: {response.message}")
                return False
                
        except rospy.ServiceException as e:
            print(f"❌ 服务调用失败: {e}")
            return False
    
    def test_emergency_stop(self, stop=True, reason="测试"):
        """测试应急停止"""
        action = "激活" if stop else "解除"
        print(f"\n🚨 测试应急停止{action}: {reason}")
        
        try:
            # 创建请求
            request = EmergencyStopRequest()
            request.stop = stop
            request.reason = reason
            
            # 调用服务
            response = self.emergency_stop_client(request)
            
            if response.success:
                print(f"✅ 应急停止{action}成功: {response.message}")
                return True
            else:
                print(f"❌ 应急停止{action}失败: {response.message}")
                return False
                
        except rospy.ServiceException as e:
            print(f"❌ 服务调用失败: {e}")
            return False
    
    def run_basic_tests(self):
        """运行基础测试"""
        print("\n" + "="*50)
        print("🧪 开始基础功能测试")
        print("="*50)
        
        # 测试1: 设置简单目标
        success1 = self.test_set_navigation_goal(2.0, 1.0, "- 简单目标")
        time.sleep(2)
        
        # 测试2: 应急停止
        success2 = self.test_emergency_stop(True, "基础测试应急停止")
        time.sleep(2)
        
        # 测试3: 恢复导航
        success3 = self.test_emergency_stop(False, "基础测试恢复导航")
        time.sleep(2)
        
        # 测试4: 设置远距离目标
        success4 = self.test_set_navigation_goal(5.0, 3.0, "- 远距离目标")
        
        # 总结
        print("\n" + "="*50)
        print("📊 基础测试结果总结")
        print("="*50)
        tests = [
            ("设置简单目标", success1),
            ("应急停止", success2),
            ("恢复导航", success3),
            ("设置远距离目标", success4)
        ]
        
        passed = 0
        for test_name, result in tests:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{len(tests)} 测试通过")
        return passed == len(tests)
    
    def run_interactive_test(self):
        """运行交互式测试"""
        print("\n" + "="*50)
        print("🎮 交互式测试模式")
        print("="*50)
        print("输入命令进行测试:")
        print("  goal <x> <y>  - 设置导航目标")
        print("  stop          - 应急停止")
        print("  resume        - 恢复导航")
        print("  quit          - 退出")
        print("="*50)
        
        while not rospy.is_shutdown():
            try:
                command = input("\n🎮 请输入命令: ").strip().lower()
                
                if command == "quit" or command == "q":
                    break
                elif command == "stop":
                    self.test_emergency_stop(True, "用户手动停止")
                elif command == "resume":
                    self.test_emergency_stop(False, "用户手动恢复")
                elif command.startswith("goal"):
                    parts = command.split()
                    if len(parts) >= 3:
                        try:
                            x = float(parts[1])
                            y = float(parts[2])
                            self.test_set_navigation_goal(x, y, "- 用户设置")
                        except ValueError:
                            print("❌ 坐标格式错误，请输入数字")
                    else:
                        print("❌ 格式错误，请使用: goal <x> <y>")
                else:
                    print("❌ 未知命令，请重新输入")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break
        
        print("\n👋 交互式测试结束")
    
    def run_predefined_scenarios(self):
        """运行预定义场景测试"""
        print("\n" + "="*50)
        print("🎬 预定义场景测试")
        print("="*50)
        
        scenarios = [
            {
                'name': '场景1: 办公室导航',
                'goals': [(1.0, 1.0), (3.0, 2.0), (2.0, 4.0)],
                'description': '模拟在办公室环境中的多点导航'
            },
            {
                'name': '场景2: 应急疏散',
                'goals': [(0.0, 0.0), (-2.0, -1.0)],
                'description': '模拟应急情况下的疏散路径'
            },
            {
                'name': '场景3: 巡逻路径',
                'goals': [(2.0, 0.0), (2.0, 2.0), (0.0, 2.0), (0.0, 0.0)],
                'description': '模拟安全巡逻的循环路径'
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n🎬 执行{scenario['name']}")
            print(f"📝 描述: {scenario['description']}")
            
            for j, (x, y) in enumerate(scenario['goals'], 1):
                print(f"\n  📍 目标点 {j}/{len(scenario['goals'])}: ({x}, {y})")
                success = self.test_set_navigation_goal(x, y, f"- {scenario['name']} 目标{j}")
                
                if success:
                    print(f"  ⏳ 等待导航完成...")
                    time.sleep(5)  # 等待导航
                else:
                    print(f"  ❌ 目标设置失败，跳过此场景")
                    break
            
            print(f"✅ {scenario['name']} 完成")
            
            # 场景间暂停
            if i < len(scenarios):
                input(f"\n⏸️  按Enter继续下一个场景...")


def main():
    """主函数"""
    print("🚀 语义导航系统测试器")
    print("版本: 2.1")
    print("="*50)
    
    try:
        tester = SemanticNavigationTester()
        
        while True:
            print("\n📋 请选择测试模式:")
            print("  1. 基础功能测试")
            print("  2. 交互式测试")
            print("  3. 预定义场景测试")
            print("  4. 退出")
            
            try:
                choice = input("\n请输入选择 (1-4): ").strip()
                
                if choice == "1":
                    tester.run_basic_tests()
                elif choice == "2":
                    tester.run_interactive_test()
                elif choice == "3":
                    tester.run_predefined_scenarios()
                elif choice == "4":
                    break
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break
        
        print("\n👋 测试器退出")
        
    except rospy.ROSInterruptException:
        print("\n🛑 ROS中断，测试器退出")
    except Exception as e:
        print(f"\n❌ 测试器异常: {e}")


if __name__ == '__main__':
    main()
