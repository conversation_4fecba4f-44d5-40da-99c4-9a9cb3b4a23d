#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
语义导航系统监控工具
功能：实时监控语义导航系统状态
作者：语义SLAM增强项目 - 阶段2扩展
版本：2.1
"""

import rospy
import time
import threading
from datetime import datetime
from collections import deque

from std_msgs.msg import String, Bool
from geometry_msgs.msg import PoseStamped, Twist
from nav_msgs.msg import Path
from visualization_msgs.msg import MarkerArray
from semantic_navigation.msg import NavigationStatus, EmergencyAlert

class NavigationMonitor:
    """导航系统监控器"""
    
    def __init__(self):
        rospy.init_node('navigation_monitor', anonymous=True)
        
        # 状态数据
        self.navigation_state = "UNKNOWN"
        self.current_pose = None
        self.current_goal = None
        self.planned_path = None
        self.cmd_vel = None
        self.fire_locations = []
        self.navigation_status = None
        self.emergency_alerts = deque(maxlen=10)
        self.emergency_stop_active = False
        
        # 统计数据
        self.start_time = time.time()
        self.goal_count = 0
        self.emergency_count = 0
        self.distance_traveled = 0.0
        self.last_pose = None
        
        # 线程锁
        self.data_lock = threading.Lock()
        
        # 设置订阅器
        self.setup_subscribers()
        
        print("📊 语义导航系统监控器启动")
        print("="*50)
    
    def setup_subscribers(self):
        """设置订阅器"""
        # 导航状态
        self.state_sub = rospy.Subscriber(
            '/navigation_state', String, self.state_callback, queue_size=1
        )
        
        # 机器人位姿
        self.pose_sub = rospy.Subscriber(
            '/pose_center/odom', PoseStamped, self.pose_callback, queue_size=1
        )
        
        # 规划路径
        self.path_sub = rospy.Subscriber(
            '/planned_path', Path, self.path_callback, queue_size=1
        )
        
        # 速度命令
        self.cmd_vel_sub = rospy.Subscriber(
            '/cmd_vel', Twist, self.cmd_vel_callback, queue_size=1
        )
        
        # 火焰位置
        self.fire_sub = rospy.Subscriber(
            '/semantic_perception/fire_locations_3d', MarkerArray, 
            self.fire_callback, queue_size=10
        )
        
        # 导航状态详情
        self.nav_status_sub = rospy.Subscriber(
            '/navigation_status', NavigationStatus, 
            self.nav_status_callback, queue_size=1
        )
        
        # 应急警报
        self.alert_sub = rospy.Subscriber(
            '/emergency_alert', EmergencyAlert, 
            self.alert_callback, queue_size=10
        )
        
        # 应急停止状态
        self.emergency_stop_sub = rospy.Subscriber(
            '/emergency_stop', Bool, self.emergency_stop_callback, queue_size=1
        )
    
    def state_callback(self, msg):
        """导航状态回调"""
        with self.data_lock:
            old_state = self.navigation_state
            self.navigation_state = msg.data
            
            if old_state != self.navigation_state:
                if self.navigation_state == "GOAL_REACHED":
                    self.goal_count += 1
    
    def pose_callback(self, msg):
        """位姿回调"""
        with self.data_lock:
            if self.last_pose:
                # 计算移动距离
                dx = msg.pose.position.x - self.last_pose.pose.position.x
                dy = msg.pose.position.y - self.last_pose.pose.position.y
                distance = (dx**2 + dy**2)**0.5
                self.distance_traveled += distance
            
            self.last_pose = self.current_pose
            self.current_pose = msg
    
    def path_callback(self, msg):
        """路径回调"""
        with self.data_lock:
            self.planned_path = msg
    
    def cmd_vel_callback(self, msg):
        """速度命令回调"""
        with self.data_lock:
            self.cmd_vel = msg
    
    def fire_callback(self, msg):
        """火焰位置回调"""
        with self.data_lock:
            fire_locations = []
            for marker in msg.markers:
                if marker.ns == "fire_locations_3d":
                    fire_locations.append({
                        'x': marker.pose.position.x,
                        'y': marker.pose.position.y,
                        'confidence': marker.color.a
                    })
            self.fire_locations = fire_locations
    
    def nav_status_callback(self, msg):
        """导航状态详情回调"""
        with self.data_lock:
            self.navigation_status = msg
    
    def alert_callback(self, msg):
        """应急警报回调"""
        with self.data_lock:
            alert_info = {
                'time': datetime.now().strftime("%H:%M:%S"),
                'type': msg.alert_type,
                'severity': msg.severity,
                'message': msg.message,
                'immediate_action': msg.requires_immediate_action
            }
            self.emergency_alerts.append(alert_info)
            
            if msg.severity in ["HIGH", "CRITICAL"]:
                self.emergency_count += 1
    
    def emergency_stop_callback(self, msg):
        """应急停止回调"""
        with self.data_lock:
            self.emergency_stop_active = msg.data
    
    def get_system_status(self):
        """获取系统状态"""
        with self.data_lock:
            status = {
                'navigation_state': self.navigation_state,
                'emergency_stop': self.emergency_stop_active,
                'fire_count': len(self.fire_locations),
                'has_path': self.planned_path is not None,
                'path_length': len(self.planned_path.poses) if self.planned_path else 0,
                'current_speed': 0.0,
                'current_angular_speed': 0.0,
                'distance_to_goal': 0.0,
                'fire_threat': False,
                'obstacle_count': 0
            }
            
            if self.cmd_vel:
                status['current_speed'] = self.cmd_vel.linear.x
                status['current_angular_speed'] = self.cmd_vel.angular.z
            
            if self.navigation_status:
                status['distance_to_goal'] = self.navigation_status.distance_to_goal
                status['fire_threat'] = self.navigation_status.fire_threat_detected
                status['obstacle_count'] = self.navigation_status.obstacle_count
            
            return status
    
    def get_statistics(self):
        """获取统计信息"""
        runtime = time.time() - self.start_time
        
        stats = {
            'runtime': runtime,
            'runtime_str': f"{int(runtime//3600):02d}:{int((runtime%3600)//60):02d}:{int(runtime%60):02d}",
            'goals_reached': self.goal_count,
            'emergency_events': self.emergency_count,
            'distance_traveled': self.distance_traveled,
            'avg_speed': self.distance_traveled / runtime if runtime > 0 else 0.0
        }
        
        return stats
    
    def print_status_header(self):
        """打印状态表头"""
        print("\n" + "="*80)
        print("📊 语义导航系统实时监控")
        print("="*80)
        print(f"{'时间':<10} {'状态':<12} {'速度':<8} {'火焰':<6} {'路径':<8} {'距离':<8} {'威胁':<6}")
        print("-"*80)
    
    def print_status_line(self):
        """打印状态行"""
        status = self.get_system_status()
        current_time = datetime.now().strftime("%H:%M:%S")
        
        # 状态颜色编码
        state_color = {
            'IDLE': '⚪',
            'PLANNING': '🟡',
            'NAVIGATING': '🟢',
            'AVOIDING': '🟠',
            'EMERGENCY_STOP': '🔴',
            'GOAL_REACHED': '✅',
            'FAILED': '❌'
        }
        
        state_icon = state_color.get(status['navigation_state'], '❓')
        
        # 格式化输出
        speed_str = f"{status['current_speed']:.2f}"
        fire_str = f"{status['fire_count']}"
        path_str = f"{status['path_length']}"
        dist_str = f"{status['distance_to_goal']:.1f}m"
        threat_str = "🔥" if status['fire_threat'] else "✅"
        
        print(f"{current_time:<10} {state_icon}{status['navigation_state']:<11} {speed_str:<8} {fire_str:<6} {path_str:<8} {dist_str:<8} {threat_str:<6}")
    
    def print_detailed_status(self):
        """打印详细状态"""
        status = self.get_system_status()
        stats = self.get_statistics()
        
        print("\n" + "="*60)
        print("📋 详细系统状态")
        print("="*60)
        
        # 基本状态
        print(f"🎮 导航状态: {status['navigation_state']}")
        print(f"🚨 应急停止: {'激活' if status['emergency_stop'] else '正常'}")
        print(f"🔥 火焰检测: {status['fire_count']} 个位置")
        print(f"🛡️ 火焰威胁: {'是' if status['fire_threat'] else '否'}")
        print(f"🚧 障碍物数: {status['obstacle_count']}")
        
        # 运动状态
        print(f"\n🚗 运动状态:")
        print(f"  线速度: {status['current_speed']:.3f} m/s")
        print(f"  角速度: {status['current_angular_speed']:.3f} rad/s")
        print(f"  到目标距离: {status['distance_to_goal']:.2f} m")
        
        # 路径信息
        print(f"\n🗺️ 路径信息:")
        print(f"  有规划路径: {'是' if status['has_path'] else '否'}")
        print(f"  路径点数: {status['path_length']}")
        
        # 统计信息
        print(f"\n📊 统计信息:")
        print(f"  运行时间: {stats['runtime_str']}")
        print(f"  到达目标: {stats['goals_reached']} 次")
        print(f"  应急事件: {stats['emergency_events']} 次")
        print(f"  行驶距离: {stats['distance_traveled']:.2f} m")
        print(f"  平均速度: {stats['avg_speed']:.3f} m/s")
        
        # 最近警报
        if self.emergency_alerts:
            print(f"\n🚨 最近警报:")
            for alert in list(self.emergency_alerts)[-3:]:
                severity_icon = {"LOW": "🟢", "MEDIUM": "🟡", "HIGH": "🟠", "CRITICAL": "🔴"}
                icon = severity_icon.get(alert['severity'], "❓")
                print(f"  {alert['time']} {icon} {alert['message']}")
        
        # 火焰位置
        if self.fire_locations:
            print(f"\n🔥 火焰位置:")
            for i, fire in enumerate(self.fire_locations[:3]):
                print(f"  {i+1}. ({fire['x']:.1f}, {fire['y']:.1f}) 置信度: {fire['confidence']:.2f}")
    
    def run_continuous_monitor(self):
        """运行连续监控"""
        self.print_status_header()
        
        rate = rospy.Rate(2)  # 2Hz更新
        line_count = 0
        
        try:
            while not rospy.is_shutdown():
                self.print_status_line()
                line_count += 1
                
                # 每20行重新打印表头
                if line_count % 20 == 0:
                    self.print_status_header()
                
                rate.sleep()
                
        except KeyboardInterrupt:
            print("\n👋 监控结束")
    
    def run_interactive_monitor(self):
        """运行交互式监控"""
        print("🎮 交互式监控模式")
        print("命令: status(状态), stats(统计), alerts(警报), quit(退出)")
        
        while not rospy.is_shutdown():
            try:
                command = input("\n监控> ").strip().lower()
                
                if command in ['quit', 'q', 'exit']:
                    break
                elif command in ['status', 's']:
                    self.print_detailed_status()
                elif command in ['stats', 'statistics']:
                    stats = self.get_statistics()
                    print(f"\n📊 统计摘要:")
                    print(f"运行时间: {stats['runtime_str']}")
                    print(f"目标完成: {stats['goals_reached']}")
                    print(f"应急事件: {stats['emergency_events']}")
                    print(f"行驶距离: {stats['distance_traveled']:.2f}m")
                elif command in ['alerts', 'a']:
                    if self.emergency_alerts:
                        print(f"\n🚨 警报历史:")
                        for alert in self.emergency_alerts:
                            print(f"{alert['time']} [{alert['severity']}] {alert['message']}")
                    else:
                        print("📋 无警报记录")
                elif command in ['help', 'h']:
                    print("📋 可用命令:")
                    print("  status/s  - 显示详细状态")
                    print("  stats     - 显示统计信息")
                    print("  alerts/a  - 显示警报历史")
                    print("  quit/q    - 退出监控")
                else:
                    print("❓ 未知命令，输入 'help' 查看帮助")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break
        
        print("\n👋 交互式监控结束")


def main():
    """主函数"""
    import sys
    
    print("📊 语义导航系统监控工具")
    print("版本: 2.1")
    
    try:
        monitor = NavigationMonitor()
        
        # 等待数据
        print("⏳ 等待系统数据...")
        rospy.sleep(2)
        
        if len(sys.argv) > 1 and sys.argv[1] == 'interactive':
            monitor.run_interactive_monitor()
        else:
            print("💡 提示: 使用 'python3 monitor_navigation.py interactive' 启动交互模式")
            monitor.run_continuous_monitor()
            
    except rospy.ROSInterruptException:
        print("🛑 ROS中断，监控器退出")
    except Exception as e:
        print(f"❌ 监控器异常: {e}")

if __name__ == '__main__':
    main()
