# 几何-语义融合建图系统核心代码

## 1. 语义分割网络模块 (semantic_segmentation_node.py)

```python
#!/usr/bin/env python3
import rospy
import torch
import torch.nn.functional as F
import torchvision.transforms as transforms
import torchvision.models.segmentation as models
import cv2
import numpy as np
import time
from std_msgs.msg import Header
from sensor_msgs.msg import Image, CameraInfo
from cv_bridge import CvBridge
from semantic_perception.msg import SemanticSegmentation

class SemanticSegmentationNode:
    def __init__(self):
        rospy.init_node('semantic_segmentation_node', anonymous=True)
        
        self.bridge = CvBridge()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.model_name = rospy.get_param('~model_name', 'deeplabv3_resnet50')
        self.num_classes = rospy.get_param('~num_classes', 21)
        self.input_size = rospy.get_param('~input_size', [512, 512])
        self.confidence_threshold = rospy.get_param('~confidence_threshold', 0.5)
        
        self.class_names = [
            'background', 'aeroplane', 'bicycle', 'bird', 'boat',
            'bottle', 'bus', 'car', 'cat', 'chair', 'cow',
            'diningtable', 'dog', 'horse', 'motorbike', 'person',
            'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor'
        ]
        
        self.class_colors = self._generate_colors(self.num_classes)
        
        self.model = None
        self._load_model()
        
        self.seg_pub = rospy.Publisher('semantic_segmentation', SemanticSegmentation, queue_size=1)
        self.vis_pub = rospy.Publisher('segmentation_visualization', Image, queue_size=1)
        
        self.image_sub = rospy.Subscriber('image_raw', Image, self.image_callback, queue_size=1)
        self.camera_info_sub = rospy.Subscriber('camera_info', CameraInfo, self.camera_info_callback, queue_size=1)
        
        self.camera_info = None

    def _load_model(self):
        try:
            if self.model_name == 'deeplabv3_resnet50':
                self.model = models.deeplabv3_resnet50(pretrained=True, num_classes=self.num_classes)
            elif self.model_name == 'deeplabv3_resnet101':
                self.model = models.deeplabv3_resnet101(pretrained=True, num_classes=self.num_classes)
            elif self.model_name == 'fcn_resnet50':
                self.model = models.fcn_resnet50(pretrained=True, num_classes=self.num_classes)
            else:
                self.model = models.deeplabv3_resnet50(pretrained=True, num_classes=self.num_classes)
            
            self.model.to(self.device)
            self.model.eval()
            
            self.transform = transforms.Compose([
                transforms.ToPILImage(),
                transforms.Resize(self.input_size),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
            
        except Exception as e:
            self.model = None

    def camera_info_callback(self, msg):
        self.camera_info = msg

    def image_callback(self, msg):
        try:
            start_time = time.time()
            
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            
            if self.model is not None:
                segmentation_mask, confidence_map = self._segment_image(cv_image)
            else:
                segmentation_mask, confidence_map = self._generate_dummy_segmentation(cv_image)
            
            seg_msg = self._create_segmentation_message(
                msg.header, cv_image, segmentation_mask, confidence_map
            )
            
            self.seg_pub.publish(seg_msg)
            
            vis_image = self._create_visualization(cv_image, segmentation_mask)
            vis_msg = self.bridge.cv2_to_imgmsg(vis_image, "bgr8")
            vis_msg.header = msg.header
            self.vis_pub.publish(vis_msg)
            
            processing_time = time.time() - start_time
            
        except Exception as e:
            pass

    def _segment_image(self, image):
        input_tensor = self._preprocess_image(image)
        
        with torch.no_grad():
            output = self.model(input_tensor)
            segmentation_mask, confidence_map = self._postprocess_output(output, image.shape[:2])
            
        return segmentation_mask, confidence_map

    def _preprocess_image(self, image):
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        input_tensor = self.transform(image_rgb).unsqueeze(0).to(self.device)
        return input_tensor

    def _postprocess_output(self, output, original_size):
        if isinstance(output, dict):
            output = output['out']
        
        output = F.softmax(output, dim=1)
        output = F.interpolate(output, size=original_size, mode='bilinear', align_corners=False)
        
        confidence_map = torch.max(output, dim=1)[0].cpu().numpy().squeeze()
        segmentation_mask = torch.argmax(output, dim=1).cpu().numpy().squeeze().astype(np.uint8)
        
        return segmentation_mask, confidence_map

    def _generate_dummy_segmentation(self, image):
        h, w = image.shape[:2]
        segmentation_mask = np.random.randint(0, self.num_classes, (h, w), dtype=np.uint8)
        confidence_map = np.random.rand(h, w).astype(np.float32)
        return segmentation_mask, confidence_map

    def _create_segmentation_message(self, header, image, segmentation_mask, confidence_map):
        msg = SemanticSegmentation()
        msg.header = header
        msg.height = image.shape[0]
        msg.width = image.shape[1]
        
        msg.class_ids = segmentation_mask.flatten().tolist()
        msg.confidence = confidence_map.flatten().tolist()
        msg.class_names = self.class_names
        
        unique_classes = np.unique(segmentation_mask)
        msg.num_classes = len(unique_classes)
        msg.mean_confidence = float(np.mean(confidence_map))
        
        if self.camera_info:
            msg.camera_info = self.camera_info
            
        return msg

    def _create_visualization(self, image, segmentation_mask):
        colored_mask = np.zeros_like(image)
        for class_id in range(self.num_classes):
            mask = segmentation_mask == class_id
            colored_mask[mask] = self.class_colors[class_id]
        
        alpha = 0.6
        visualization = cv2.addWeighted(image, 1-alpha, colored_mask, alpha, 0)
        
        return visualization

    def _generate_colors(self, num_classes):
        colors = []
        for i in range(num_classes):
            hue = int(180 * i / num_classes)
            color = cv2.cvtColor(np.uint8([[[hue, 255, 255]]]), cv2.COLOR_HSV2BGR)[0][0]
            colors.append([int(color[0]), int(color[1]), int(color[2])])
        return colors

if __name__ == '__main__':
    try:
        node = SemanticSegmentationNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```

## 2. 几何-语义融合核心模块 (geometry_semantic_fusion_node.py)

```python
#!/usr/bin/env python3
import rospy
import numpy as np
import cv2
import threading
from collections import deque
import tf2_ros
import tf2_geometry_msgs
from std_msgs.msg import Header
from sensor_msgs.msg import PointCloud2, CameraInfo
from geometry_msgs.msg import Point
from cv_bridge import CvBridge
from semantic_perception.msg import SemanticSegmentation
import sensor_msgs.point_cloud2 as pc2

class GeometrySemanticFusionNode:
    def __init__(self):
        rospy.init_node('geometry_semantic_fusion_node', anonymous=True)
        
        self.enable_temporal_smoothing = rospy.get_param('~enable_temporal_smoothing', True)
        self.geometric_constraint_weight = rospy.get_param('~geometric_constraint_weight', 0.3)
        self.temporal_smoothing_weight = rospy.get_param('~temporal_smoothing_weight', 0.2)
        self.confidence_boost_factor = rospy.get_param('~confidence_boost_factor', 1.2)
        
        self.bridge = CvBridge()
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)
        
        self.tsdf_pointcloud = None
        self.semantic_segmentation = None
        self.camera_info = None
        self.last_fusion_result = None
        
        self.data_lock = threading.Lock()
        
        self.semantic_history = deque(maxlen=5)
        
        self.tsdf_sub = rospy.Subscriber('/tsdf_mapping/pointcloud', PointCloud2, 
                                        self.tsdf_callback, queue_size=1)
        self.semantic_sub = rospy.Subscriber('/semantic_perception/segmentation', SemanticSegmentation, 
                                           self.semantic_callback, queue_size=1)
        self.camera_info_sub = rospy.Subscriber('/stereo_camera/left/camera_info', CameraInfo, 
                                               self.camera_info_callback, queue_size=1)
        
        self.fused_semantic_pub = rospy.Publisher('fused_semantic_segmentation', SemanticSegmentation, queue_size=1)
        self.enhanced_pointcloud_pub = rospy.Publisher('enhanced_semantic_pointcloud', PointCloud2, queue_size=1)
        
        self.fusion_thread = threading.Thread(target=self.fusion_loop)
        self.fusion_thread.daemon = True
        self.fusion_thread.start()

    def tsdf_callback(self, msg):
        with self.data_lock:
            self.tsdf_pointcloud = msg

    def semantic_callback(self, msg):
        with self.data_lock:
            self.semantic_segmentation = msg
            
            if self.enable_temporal_smoothing:
                self.semantic_history.append(msg)

    def camera_info_callback(self, msg):
        self.camera_info = msg

    def fusion_loop(self):
        rate = rospy.Rate(10.0)
        
        while not rospy.is_shutdown():
            try:
                with self.data_lock:
                    if (self.tsdf_pointcloud is None or 
                        self.semantic_segmentation is None or 
                        self.camera_info is None):
                        rate.sleep()
                        continue
                    
                    tsdf_data = self.tsdf_pointcloud
                    semantic_data = self.semantic_segmentation
                    camera_info = self.camera_info
                
                fusion_result = self.perform_fusion(tsdf_data, semantic_data, camera_info)
                
                if fusion_result is not None:
                    self.publish_fusion_results(fusion_result)
                    self.last_fusion_result = fusion_result
                
                rate.sleep()
                
            except Exception as e:
                rate.sleep()

    def perform_fusion(self, tsdf_pointcloud, semantic_segmentation, camera_info):
        try:
            tsdf_points = self.extract_pointcloud_data(tsdf_pointcloud)
            if tsdf_points is None or len(tsdf_points) == 0:
                return None
            
            semantic_data = self.reconstruct_semantic_data(semantic_segmentation)
            if semantic_data is None:
                return None
            
            geometric_features = self.compute_geometric_features(tsdf_points)
            
            projected_points = self.project_points_to_image(tsdf_points, camera_info)
            
            enhanced_semantic = self.apply_geometric_constraints(
                semantic_data, projected_points, geometric_features)
            
            if self.enable_temporal_smoothing:
                enhanced_semantic = self.apply_temporal_smoothing(enhanced_semantic)
            
            return enhanced_semantic
            
        except Exception as e:
            return None

    def extract_pointcloud_data(self, pointcloud_msg):
        try:
            points = []
            for point in pc2.read_points(pointcloud_msg, skip_nans=True):
                points.append([point[0], point[1], point[2]])
            
            if len(points) == 0:
                return None
                
            return np.array(points)
            
        except Exception as e:
            return None

    def reconstruct_semantic_data(self, semantic_msg):
        try:
            height = semantic_msg.height
            width = semantic_msg.width
            
            class_ids = np.array(semantic_msg.class_ids).reshape(height, width)
            confidence = np.array(semantic_msg.confidence).reshape(height, width)
            
            return {
                'class_ids': class_ids,
                'confidence': confidence,
                'class_names': semantic_msg.class_names,
                'height': height,
                'width': width
            }
            
        except Exception as e:
            return None

    def compute_geometric_features(self, points):
        try:
            normals = self.compute_surface_normals(points)
            curvatures = self.compute_curvatures(points, normals)
            
            return {
                'normals': normals,
                'curvatures': curvatures
            }
            
        except Exception as e:
            return {'normals': np.zeros((len(points), 3)), 'curvatures': np.zeros(len(points))}

    def compute_surface_normals(self, points):
        normals = np.zeros_like(points)
        
        for i in range(len(points)):
            neighbors = self.find_k_nearest_neighbors(points, i, k=10)
            if len(neighbors) >= 3:
                neighbor_points = points[neighbors]
                centroid = np.mean(neighbor_points, axis=0)
                centered_points = neighbor_points - centroid
                
                try:
                    _, _, V = np.linalg.svd(centered_points)
                    normal = V[-1]
                    normals[i] = normal / np.linalg.norm(normal)
                except:
                    normals[i] = [0, 0, 1]
            else:
                normals[i] = [0, 0, 1]
        
        return normals

    def compute_curvatures(self, points, normals):
        curvatures = np.zeros(len(points))
        
        for i in range(len(points)):
            neighbors = self.find_k_nearest_neighbors(points, i, k=10)
            if len(neighbors) >= 3:
                neighbor_normals = normals[neighbors]
                normal_variations = np.linalg.norm(neighbor_normals - normals[i], axis=1)
                curvatures[i] = np.mean(normal_variations)
        
        return curvatures

    def find_k_nearest_neighbors(self, points, index, k=10):
        distances = np.linalg.norm(points - points[index], axis=1)
        return np.argsort(distances)[1:k+1]

    def project_points_to_image(self, points, camera_info):
        try:
            fx = camera_info.K[0]
            fy = camera_info.K[4]
            cx = camera_info.K[2]
            cy = camera_info.K[5]
            
            projected_points = []
            valid_indices = []
            
            for i, point in enumerate(points):
                x, y, z = point
                if z > 0.1:
                    u = int(fx * x / z + cx)
                    v = int(fy * y / z + cy)
                    
                    if 0 <= u < camera_info.width and 0 <= v < camera_info.height:
                        projected_points.append([u, v, z])
                        valid_indices.append(i)
            
            return np.array(projected_points), np.array(valid_indices)
            
        except Exception as e:
            return np.array([]), np.array([])

    def apply_geometric_constraints(self, semantic_data, projected_points, geometric_features):
        try:
            class_ids = semantic_data['class_ids'].copy()
            confidence = semantic_data['confidence'].copy()
            
            if len(projected_points) == 0:
                return semantic_data
            
            proj_points, valid_indices = projected_points
            
            normals = geometric_features['normals'][valid_indices]
            curvatures = geometric_features['curvatures'][valid_indices]
            
            for i, (u, v, depth) in enumerate(proj_points):
                u, v = int(u), int(v)
                
                neighborhood = self.get_image_neighborhood(u, v, class_ids.shape, radius=3)
                
                depth_consistent = True
                for nu, nv in neighborhood:
                    pass
                
                if depth_consistent:
                    if curvatures[i] < 0.1:
                        confidence[v, u] *= self.confidence_boost_factor
                    
                    if curvatures[i] > 0.5:
                        confidence[v, u] *= 0.8
            
            return {
                'class_ids': class_ids,
                'confidence': confidence,
                'class_names': semantic_data['class_names'],
                'height': semantic_data['height'],
                'width': semantic_data['width']
            }
            
        except Exception as e:
            return semantic_data

    def get_image_neighborhood(self, u, v, shape, radius=3):
        h, w = shape
        neighborhood = []
        
        for dv in range(-radius, radius+1):
            for du in range(-radius, radius+1):
                nu, nv = u + du, v + dv
                if 0 <= nu < w and 0 <= nv < h:
                    neighborhood.append((nu, nv))
        
        return neighborhood

    def apply_temporal_smoothing(self, enhanced_semantic):
        try:
            if len(self.semantic_history) < 2:
                return enhanced_semantic
            
            current_confidence = enhanced_semantic['confidence']
            current_class_ids = enhanced_semantic['class_ids']
            
            weights = np.linspace(0.1, 0.9, len(self.semantic_history))
            weights = weights / weights.sum()
            
            smoothed_confidence = current_confidence * weights[-1]
            
            for i, hist_msg in enumerate(self.semantic_history[:-1]):
                try:
                    hist_data = self.reconstruct_semantic_data(hist_msg)
                    if hist_data is not None:
                        hist_confidence = hist_data['confidence']
                        hist_class_ids = hist_data['class_ids']
                        
                        same_class_mask = (hist_class_ids == current_class_ids)
                        smoothed_confidence[same_class_mask] += (
                            hist_confidence[same_class_mask] * weights[i])
                
                except Exception as e:
                    continue
            
            enhanced_semantic['confidence'] = smoothed_confidence
            return enhanced_semantic
            
        except Exception as e:
            return enhanced_semantic

    def publish_fusion_results(self, fusion_result):
        try:
            msg = SemanticSegmentation()
            msg.header.stamp = rospy.Time.now()
            msg.header.frame_id = "camera_link"
            msg.height = fusion_result['height']
            msg.width = fusion_result['width']
            
            msg.class_ids = fusion_result['class_ids'].flatten().tolist()
            msg.confidence = fusion_result['confidence'].flatten().tolist()
            msg.class_names = fusion_result['class_names']
            
            unique_classes = np.unique(fusion_result['class_ids'])
            msg.num_classes = len(unique_classes)
            msg.mean_confidence = float(np.mean(fusion_result['confidence']))
            
            self.fused_semantic_pub.publish(msg)
            
        except Exception as e:
            pass

if __name__ == '__main__':
    try:
        node = GeometrySemanticFusionNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```

## 3. 语义地图集成模块 (semantic_map_integration_node.py)

```python
#!/usr/bin/env python3
import rospy
import numpy as np
import tf2_ros
import tf2_geometry_msgs
from std_msgs.msg import Header
from sensor_msgs.msg import PointCloud2, Image
from geometry_msgs.msg import Point, Pose, PoseStamped
from visualization_msgs.msg import Marker, MarkerArray
from darknet_ros_msgs.msg import BoundingBoxes
from semantic_perception.msg import SemanticSegmentation, SemanticObject
import sensor_msgs.point_cloud2 as pc2

class SemanticMapIntegrationNode:
    def __init__(self):
        rospy.init_node('semantic_map_integration_node', anonymous=True)

        self.map_resolution = rospy.get_param('~map_resolution', 0.05)
        self.map_size_x = rospy.get_param('~map_size_x', 20.0)
        self.map_size_y = rospy.get_param('~map_size_y', 20.0)
        self.map_size_z = rospy.get_param('~map_size_z', 5.0)
        self.enable_semantic_mapping = rospy.get_param('~enable_semantic_mapping', True)
        self.enable_object_tracking = rospy.get_param('~enable_object_tracking', True)
        self.semantic_confidence_threshold = rospy.get_param('~semantic_confidence_threshold', 0.6)

        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)

        self.semantic_map = {}
        self.semantic_objects = {}
        self.fire_locations = []

        self.last_rtabmap_cloud = None
        self.last_semantic_data = None
        self.last_tsdf_cloud = None

        self.rtabmap_cloud_sub = rospy.Subscriber('rtabmap/cloud_map', PointCloud2, self.rtabmap_cloud_callback)
        self.semantic_segmentation_sub = rospy.Subscriber('semantic_segmentation', SemanticSegmentation, self.semantic_segmentation_callback)
        self.object_detection_sub = rospy.Subscriber('darknet_ros/bounding_boxes', BoundingBoxes, self.object_detection_callback)

        self.tsdf_cloud_sub = rospy.Subscriber('tsdf_pointcloud', PointCloud2, self.tsdf_cloud_callback)
        self.fused_semantic_sub = rospy.Subscriber('fused_semantic_segmentation', SemanticSegmentation, self.fused_semantic_callback)
        self.fire_detection_sub = rospy.Subscriber('fire_detections', SemanticObject, self.fire_detection_callback)
        self.enhanced_pointcloud_sub = rospy.Subscriber('enhanced_semantic_pointcloud', PointCloud2, self.enhanced_pointcloud_callback)

        self.semantic_map_pub = rospy.Publisher('semantic_map', PointCloud2, queue_size=1)
        self.semantic_markers_pub = rospy.Publisher('semantic_markers', MarkerArray, queue_size=1)
        self.fire_markers_pub = rospy.Publisher('fire_markers', MarkerArray, queue_size=1)

        self.integration_timer = rospy.Timer(rospy.Duration(1.0), self.integration_callback)
        self.publish_timer = rospy.Timer(rospy.Duration(0.5), self.publish_callback)

    def rtabmap_cloud_callback(self, msg):
        try:
            self.last_rtabmap_cloud = msg
        except Exception as e:
            pass

    def semantic_segmentation_callback(self, msg):
        try:
            self.last_semantic_data = msg
        except Exception as e:
            pass

    def tsdf_cloud_callback(self, msg):
        try:
            self.last_tsdf_cloud = msg
        except Exception as e:
            pass

    def fused_semantic_callback(self, msg):
        try:
            self.last_semantic_data = msg
        except Exception as e:
            pass

    def fire_detection_callback(self, msg):
        try:
            current_time = rospy.Time.now()

            fire_location = {
                'position': msg.position,
                'confidence': msg.confidence,
                'detected_time': current_time,
                'class_name': msg.class_name
            }

            self.fire_locations.append(fire_location)

        except Exception as e:
            pass

    def enhanced_pointcloud_callback(self, msg):
        try:
            pass
        except Exception as e:
            pass

    def object_detection_callback(self, msg):
        try:
            if not self.enable_object_tracking:
                return

            current_time = rospy.Time.now()

            for bbox in msg.bounding_boxes:
                if bbox.probability > self.semantic_confidence_threshold:
                    obj_id = f"{bbox.Class}_{bbox.id if hasattr(bbox, 'id') else len(self.semantic_objects)}"

                    self.semantic_objects[obj_id] = {
                        'class': bbox.Class,
                        'probability': bbox.probability,
                        'bbox': bbox,
                        'last_seen': current_time,
                        'position': self.estimate_object_position(bbox)
                    }

        except Exception as e:
            pass

    def estimate_object_position(self, bbox):
        try:
            center_x = (bbox.xmin + bbox.xmax) / 2.0
            center_y = (bbox.ymin + bbox.ymax) / 2.0

            estimated_depth = 2.0

            position = Point()
            position.x = center_x * estimated_depth / 1000.0
            position.y = center_y * estimated_depth / 1000.0
            position.z = estimated_depth

            return position

        except Exception as e:
            return Point()

    def integration_callback(self, event):
        try:
            if not self.enable_semantic_mapping:
                return

            current_time = rospy.Time.now()
            expired_objects = []

            for obj_id, obj_data in self.semantic_objects.items():
                if (current_time - obj_data['last_seen']).to_sec() > 10.0:
                    expired_objects.append(obj_id)

            for obj_id in expired_objects:
                del self.semantic_objects[obj_id]

            self.fire_locations = [
                fire for fire in self.fire_locations
                if (current_time - fire['detected_time']).to_sec() < 30.0
            ]

            self.process_fire_detections()
            self.integrate_enhanced_semantic_data()
            self.generate_emergency_zones()

        except Exception as e:
            pass

    def process_fire_detections(self):
        try:
            if not self.fire_locations:
                return

            for fire_data in self.fire_locations:
                grid_x, grid_y, grid_z = self.world_to_grid(
                    fire_data['position'].x,
                    fire_data['position'].y,
                    fire_data['position'].z
                )

                if self.is_valid_grid_position(grid_x, grid_y, grid_z):
                    grid_key = (grid_x, grid_y, grid_z)

                    if grid_key not in self.semantic_map:
                        self.semantic_map[grid_key] = {
                            'class_id': 255,
                            'class_name': 'fire',
                            'confidence': fire_data['confidence'],
                            'last_updated': rospy.Time.now()
                        }
                    else:
                        existing_confidence = self.semantic_map[grid_key]['confidence']
                        new_confidence = max(existing_confidence, fire_data['confidence'])
                        self.semantic_map[grid_key]['confidence'] = new_confidence
                        self.semantic_map[grid_key]['last_updated'] = rospy.Time.now()

        except Exception as e:
            pass

    def integrate_enhanced_semantic_data(self):
        try:
            if self.last_semantic_data is None:
                return

            semantic_data = self.last_semantic_data

            height = semantic_data.height
            width = semantic_data.width

            class_ids = np.array(semantic_data.class_ids).reshape(height, width)
            confidence = np.array(semantic_data.confidence).reshape(height, width)

            for v in range(0, height, 5):
                for u in range(0, width, 5):
                    if confidence[v, u] > self.semantic_confidence_threshold:
                        class_id = class_ids[v, u]
                        class_name = semantic_data.class_names[class_id] if class_id < len(semantic_data.class_names) else 'unknown'

                        world_point = self.pixel_to_world(u, v, 2.0)
                        if world_point is not None:
                            grid_x, grid_y, grid_z = self.world_to_grid(
                                world_point.x, world_point.y, world_point.z
                            )

                            if self.is_valid_grid_position(grid_x, grid_y, grid_z):
                                grid_key = (grid_x, grid_y, grid_z)

                                if grid_key not in self.semantic_map:
                                    self.semantic_map[grid_key] = {
                                        'class_id': class_id,
                                        'class_name': class_name,
                                        'confidence': confidence[v, u],
                                        'last_updated': rospy.Time.now()
                                    }
                                else:
                                    existing_confidence = self.semantic_map[grid_key]['confidence']
                                    if confidence[v, u] > existing_confidence:
                                        self.semantic_map[grid_key]['class_id'] = class_id
                                        self.semantic_map[grid_key]['class_name'] = class_name
                                        self.semantic_map[grid_key]['confidence'] = confidence[v, u]
                                        self.semantic_map[grid_key]['last_updated'] = rospy.Time.now()

        except Exception as e:
            pass

    def world_to_grid(self, x, y, z):
        grid_x = int((x + self.map_size_x / 2.0) / self.map_resolution)
        grid_y = int((y + self.map_size_y / 2.0) / self.map_resolution)
        grid_z = int(z / self.map_resolution)
        return grid_x, grid_y, grid_z

    def grid_to_world(self, grid_x, grid_y, grid_z):
        x = grid_x * self.map_resolution - self.map_size_x / 2.0
        y = grid_y * self.map_resolution - self.map_size_y / 2.0
        z = grid_z * self.map_resolution
        return x, y, z

    def is_valid_grid_position(self, grid_x, grid_y, grid_z):
        max_grid_x = int(self.map_size_x / self.map_resolution)
        max_grid_y = int(self.map_size_y / self.map_resolution)
        max_grid_z = int(self.map_size_z / self.map_resolution)

        return (0 <= grid_x < max_grid_x and
                0 <= grid_y < max_grid_y and
                0 <= grid_z < max_grid_z)

    def pixel_to_world(self, u, v, depth):
        try:
            fx = 525.0
            fy = 525.0
            cx = 320.0
            cy = 240.0

            x = (u - cx) * depth / fx
            y = (v - cy) * depth / fy
            z = depth

            point = Point()
            point.x = x
            point.y = y
            point.z = z

            return point

        except Exception as e:
            return None

    def publish_callback(self, event):
        try:
            self.publish_semantic_map()
            self.publish_semantic_markers()
            self.publish_fire_markers()
        except Exception as e:
            pass

    def publish_semantic_map(self):
        try:
            if not self.semantic_map:
                return

            points = []
            for (grid_x, grid_y, grid_z), semantic_data in self.semantic_map.items():
                x, y, z = self.grid_to_world(grid_x, grid_y, grid_z)

                class_id = semantic_data['class_id']
                confidence = semantic_data['confidence']

                point = [x, y, z, class_id, confidence]
                points.append(point)

            if points:
                header = Header()
                header.stamp = rospy.Time.now()
                header.frame_id = "map"

                fields = [
                    pc2.PointField('x', 0, pc2.PointField.FLOAT32, 1),
                    pc2.PointField('y', 4, pc2.PointField.FLOAT32, 1),
                    pc2.PointField('z', 8, pc2.PointField.FLOAT32, 1),
                    pc2.PointField('class_id', 12, pc2.PointField.UINT32, 1),
                    pc2.PointField('confidence', 16, pc2.PointField.FLOAT32, 1),
                ]

                cloud_msg = pc2.create_cloud(header, fields, points)
                self.semantic_map_pub.publish(cloud_msg)

        except Exception as e:
            pass

if __name__ == '__main__':
    try:
        node = SemanticMapIntegrationNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```
