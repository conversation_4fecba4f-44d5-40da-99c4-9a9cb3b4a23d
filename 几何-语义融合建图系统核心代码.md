# 几何-语义融合建图系统核心代码

## 1. 语义分割网络模块 (semantic_segmentation_node.py)

```python
#!/usr/bin/env python3
import rospy
import torch
import torch.nn.functional as F
import torchvision.transforms as transforms
import torchvision.models.segmentation as models
import cv2
import numpy as np
import time
from std_msgs.msg import Header
from sensor_msgs.msg import Image, CameraInfo
from cv_bridge import CvBridge
from semantic_perception.msg import SemanticSegmentation

class SemanticSegmentationNode:
    def __init__(self):
        rospy.init_node('semantic_segmentation_node', anonymous=True)
        
        self.bridge = CvBridge()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.model_name = rospy.get_param('~model_name', 'deeplabv3_resnet50')
        self.num_classes = rospy.get_param('~num_classes', 21)
        self.input_size = rospy.get_param('~input_size', [512, 512])
        self.confidence_threshold = rospy.get_param('~confidence_threshold', 0.5)
        
        self.class_names = [
            'background', 'aeroplane', 'bicycle', 'bird', 'boat',
            'bottle', 'bus', 'car', 'cat', 'chair', 'cow',
            'diningtable', 'dog', 'horse', 'motorbike', 'person',
            'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor'
        ]
        
        self.class_colors = self._generate_colors(self.num_classes)
        
        self.model = None
        self._load_model()
        
        self.seg_pub = rospy.Publisher('semantic_segmentation', SemanticSegmentation, queue_size=1)
        self.vis_pub = rospy.Publisher('segmentation_visualization', Image, queue_size=1)
        
        self.image_sub = rospy.Subscriber('image_raw', Image, self.image_callback, queue_size=1)
        self.camera_info_sub = rospy.Subscriber('camera_info', CameraInfo, self.camera_info_callback, queue_size=1)
        
        self.camera_info = None

    def _load_model(self):
        try:
            if self.model_name == 'deeplabv3_resnet50':
                self.model = models.deeplabv3_resnet50(pretrained=True, num_classes=self.num_classes)
            elif self.model_name == 'deeplabv3_resnet101':
                self.model = models.deeplabv3_resnet101(pretrained=True, num_classes=self.num_classes)
            elif self.model_name == 'fcn_resnet50':
                self.model = models.fcn_resnet50(pretrained=True, num_classes=self.num_classes)
            else:
                self.model = models.deeplabv3_resnet50(pretrained=True, num_classes=self.num_classes)
            
            self.model.to(self.device)
            self.model.eval()
            
            self.transform = transforms.Compose([
                transforms.ToPILImage(),
                transforms.Resize(self.input_size),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
            
        except Exception as e:
            self.model = None

    def camera_info_callback(self, msg):
        self.camera_info = msg

    def image_callback(self, msg):
        try:
            start_time = time.time()
            
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            
            if self.model is not None:
                segmentation_mask, confidence_map = self._segment_image(cv_image)
            else:
                segmentation_mask, confidence_map = self._generate_dummy_segmentation(cv_image)
            
            seg_msg = self._create_segmentation_message(
                msg.header, cv_image, segmentation_mask, confidence_map
            )
            
            self.seg_pub.publish(seg_msg)
            
            vis_image = self._create_visualization(cv_image, segmentation_mask)
            vis_msg = self.bridge.cv2_to_imgmsg(vis_image, "bgr8")
            vis_msg.header = msg.header
            self.vis_pub.publish(vis_msg)
            
            processing_time = time.time() - start_time
            
        except Exception as e:
            pass

    def _segment_image(self, image):
        input_tensor = self._preprocess_image(image)
        
        with torch.no_grad():
            output = self.model(input_tensor)
            segmentation_mask, confidence_map = self._postprocess_output(output, image.shape[:2])
            
        return segmentation_mask, confidence_map

    def _preprocess_image(self, image):
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        input_tensor = self.transform(image_rgb).unsqueeze(0).to(self.device)
        return input_tensor

    def _postprocess_output(self, output, original_size):
        if isinstance(output, dict):
            output = output['out']
        
        output = F.softmax(output, dim=1)
        output = F.interpolate(output, size=original_size, mode='bilinear', align_corners=False)
        
        confidence_map = torch.max(output, dim=1)[0].cpu().numpy().squeeze()
        segmentation_mask = torch.argmax(output, dim=1).cpu().numpy().squeeze().astype(np.uint8)
        
        return segmentation_mask, confidence_map

    def _generate_dummy_segmentation(self, image):
        h, w = image.shape[:2]
        segmentation_mask = np.random.randint(0, self.num_classes, (h, w), dtype=np.uint8)
        confidence_map = np.random.rand(h, w).astype(np.float32)
        return segmentation_mask, confidence_map

    def _create_segmentation_message(self, header, image, segmentation_mask, confidence_map):
        msg = SemanticSegmentation()
        msg.header = header
        msg.height = image.shape[0]
        msg.width = image.shape[1]
        
        msg.class_ids = segmentation_mask.flatten().tolist()
        msg.confidence = confidence_map.flatten().tolist()
        msg.class_names = self.class_names
        
        unique_classes = np.unique(segmentation_mask)
        msg.num_classes = len(unique_classes)
        msg.mean_confidence = float(np.mean(confidence_map))
        
        if self.camera_info:
            msg.camera_info = self.camera_info
            
        return msg

    def _create_visualization(self, image, segmentation_mask):
        colored_mask = np.zeros_like(image)
        for class_id in range(self.num_classes):
            mask = segmentation_mask == class_id
            colored_mask[mask] = self.class_colors[class_id]
        
        alpha = 0.6
        visualization = cv2.addWeighted(image, 1-alpha, colored_mask, alpha, 0)
        
        return visualization

    def _generate_colors(self, num_classes):
        colors = []
        for i in range(num_classes):
            hue = int(180 * i / num_classes)
            color = cv2.cvtColor(np.uint8([[[hue, 255, 255]]]), cv2.COLOR_HSV2BGR)[0][0]
            colors.append([int(color[0]), int(color[1]), int(color[2])])
        return colors

if __name__ == '__main__':
    try:
        node = SemanticSegmentationNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```

## 2. 几何-语义融合核心模块 (geometry_semantic_fusion_node.py)

```python
#!/usr/bin/env python3
import rospy
import numpy as np
import cv2
import threading
from collections import deque
import tf2_ros
import tf2_geometry_msgs
from std_msgs.msg import Header
from sensor_msgs.msg import PointCloud2, CameraInfo
from geometry_msgs.msg import Point
from cv_bridge import CvBridge
from semantic_perception.msg import SemanticSegmentation
import sensor_msgs.point_cloud2 as pc2

class GeometrySemanticFusionNode:
    def __init__(self):
        rospy.init_node('geometry_semantic_fusion_node', anonymous=True)
        
        self.enable_temporal_smoothing = rospy.get_param('~enable_temporal_smoothing', True)
        self.geometric_constraint_weight = rospy.get_param('~geometric_constraint_weight', 0.3)
        self.temporal_smoothing_weight = rospy.get_param('~temporal_smoothing_weight', 0.2)
        self.confidence_boost_factor = rospy.get_param('~confidence_boost_factor', 1.2)
        
        self.bridge = CvBridge()
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)
        
        self.tsdf_pointcloud = None
        self.semantic_segmentation = None
        self.camera_info = None
        self.last_fusion_result = None
        
        self.data_lock = threading.Lock()
        
        self.semantic_history = deque(maxlen=5)
        
        self.tsdf_sub = rospy.Subscriber('/tsdf_mapping/pointcloud', PointCloud2, 
                                        self.tsdf_callback, queue_size=1)
        self.semantic_sub = rospy.Subscriber('/semantic_perception/segmentation', SemanticSegmentation, 
                                           self.semantic_callback, queue_size=1)
        self.camera_info_sub = rospy.Subscriber('/stereo_camera/left/camera_info', CameraInfo, 
                                               self.camera_info_callback, queue_size=1)
        
        self.fused_semantic_pub = rospy.Publisher('fused_semantic_segmentation', SemanticSegmentation, queue_size=1)
        self.enhanced_pointcloud_pub = rospy.Publisher('enhanced_semantic_pointcloud', PointCloud2, queue_size=1)
        
        self.fusion_thread = threading.Thread(target=self.fusion_loop)
        self.fusion_thread.daemon = True
        self.fusion_thread.start()

    def tsdf_callback(self, msg):
        with self.data_lock:
            self.tsdf_pointcloud = msg

    def semantic_callback(self, msg):
        with self.data_lock:
            self.semantic_segmentation = msg
            
            if self.enable_temporal_smoothing:
                self.semantic_history.append(msg)

    def camera_info_callback(self, msg):
        self.camera_info = msg

    def fusion_loop(self):
        rate = rospy.Rate(10.0)
        
        while not rospy.is_shutdown():
            try:
                with self.data_lock:
                    if (self.tsdf_pointcloud is None or 
                        self.semantic_segmentation is None or 
                        self.camera_info is None):
                        rate.sleep()
                        continue
                    
                    tsdf_data = self.tsdf_pointcloud
                    semantic_data = self.semantic_segmentation
                    camera_info = self.camera_info
                
                fusion_result = self.perform_fusion(tsdf_data, semantic_data, camera_info)
                
                if fusion_result is not None:
                    self.publish_fusion_results(fusion_result)
                    self.last_fusion_result = fusion_result
                
                rate.sleep()
                
            except Exception as e:
                rate.sleep()

    def perform_fusion(self, tsdf_pointcloud, semantic_segmentation, camera_info):
        try:
            tsdf_points = self.extract_pointcloud_data(tsdf_pointcloud)
            if tsdf_points is None or len(tsdf_points) == 0:
                return None
            
            semantic_data = self.reconstruct_semantic_data(semantic_segmentation)
            if semantic_data is None:
                return None
            
            geometric_features = self.compute_geometric_features(tsdf_points)
            
            projected_points = self.project_points_to_image(tsdf_points, camera_info)
            
            enhanced_semantic = self.apply_geometric_constraints(
                semantic_data, projected_points, geometric_features)
            
            if self.enable_temporal_smoothing:
                enhanced_semantic = self.apply_temporal_smoothing(enhanced_semantic)
            
            return enhanced_semantic
            
        except Exception as e:
            return None

    def extract_pointcloud_data(self, pointcloud_msg):
        try:
            points = []
            for point in pc2.read_points(pointcloud_msg, skip_nans=True):
                points.append([point[0], point[1], point[2]])
            
            if len(points) == 0:
                return None
                
            return np.array(points)
            
        except Exception as e:
            return None

    def reconstruct_semantic_data(self, semantic_msg):
        try:
            height = semantic_msg.height
            width = semantic_msg.width
            
            class_ids = np.array(semantic_msg.class_ids).reshape(height, width)
            confidence = np.array(semantic_msg.confidence).reshape(height, width)
            
            return {
                'class_ids': class_ids,
                'confidence': confidence,
                'class_names': semantic_msg.class_names,
                'height': height,
                'width': width
            }
            
        except Exception as e:
            return None

    def compute_geometric_features(self, points):
        try:
            normals = self.compute_surface_normals(points)
            curvatures = self.compute_curvatures(points, normals)
            
            return {
                'normals': normals,
                'curvatures': curvatures
            }
            
        except Exception as e:
            return {'normals': np.zeros((len(points), 3)), 'curvatures': np.zeros(len(points))}

    def compute_surface_normals(self, points):
        normals = np.zeros_like(points)
        
        for i in range(len(points)):
            neighbors = self.find_k_nearest_neighbors(points, i, k=10)
            if len(neighbors) >= 3:
                neighbor_points = points[neighbors]
                centroid = np.mean(neighbor_points, axis=0)
                centered_points = neighbor_points - centroid
                
                try:
                    _, _, V = np.linalg.svd(centered_points)
                    normal = V[-1]
                    normals[i] = normal / np.linalg.norm(normal)
                except:
                    normals[i] = [0, 0, 1]
            else:
                normals[i] = [0, 0, 1]
        
        return normals

    def compute_curvatures(self, points, normals):
        curvatures = np.zeros(len(points))
        
        for i in range(len(points)):
            neighbors = self.find_k_nearest_neighbors(points, i, k=10)
            if len(neighbors) >= 3:
                neighbor_normals = normals[neighbors]
                normal_variations = np.linalg.norm(neighbor_normals - normals[i], axis=1)
                curvatures[i] = np.mean(normal_variations)
        
        return curvatures

    def find_k_nearest_neighbors(self, points, index, k=10):
        distances = np.linalg.norm(points - points[index], axis=1)
        return np.argsort(distances)[1:k+1]

    def project_points_to_image(self, points, camera_info):
        try:
            fx = camera_info.K[0]
            fy = camera_info.K[4]
            cx = camera_info.K[2]
            cy = camera_info.K[5]
            
            projected_points = []
            valid_indices = []
            
            for i, point in enumerate(points):
                x, y, z = point
                if z > 0.1:
                    u = int(fx * x / z + cx)
                    v = int(fy * y / z + cy)
                    
                    if 0 <= u < camera_info.width and 0 <= v < camera_info.height:
                        projected_points.append([u, v, z])
                        valid_indices.append(i)
            
            return np.array(projected_points), np.array(valid_indices)
            
        except Exception as e:
            return np.array([]), np.array([])

    def apply_geometric_constraints(self, semantic_data, projected_points, geometric_features):
        try:
            class_ids = semantic_data['class_ids'].copy()
            confidence = semantic_data['confidence'].copy()
            
            if len(projected_points) == 0:
                return semantic_data
            
            proj_points, valid_indices = projected_points
            
            normals = geometric_features['normals'][valid_indices]
            curvatures = geometric_features['curvatures'][valid_indices]
            
            for i, (u, v, depth) in enumerate(proj_points):
                u, v = int(u), int(v)
                
                neighborhood = self.get_image_neighborhood(u, v, class_ids.shape, radius=3)
                
                depth_consistent = True
                for nu, nv in neighborhood:
                    pass
                
                if depth_consistent:
                    if curvatures[i] < 0.1:
                        confidence[v, u] *= self.confidence_boost_factor
                    
                    if curvatures[i] > 0.5:
                        confidence[v, u] *= 0.8
            
            return {
                'class_ids': class_ids,
                'confidence': confidence,
                'class_names': semantic_data['class_names'],
                'height': semantic_data['height'],
                'width': semantic_data['width']
            }
            
        except Exception as e:
            return semantic_data

    def get_image_neighborhood(self, u, v, shape, radius=3):
        h, w = shape
        neighborhood = []
        
        for dv in range(-radius, radius+1):
            for du in range(-radius, radius+1):
                nu, nv = u + du, v + dv
                if 0 <= nu < w and 0 <= nv < h:
                    neighborhood.append((nu, nv))
        
        return neighborhood

    def apply_temporal_smoothing(self, enhanced_semantic):
        try:
            if len(self.semantic_history) < 2:
                return enhanced_semantic
            
            current_confidence = enhanced_semantic['confidence']
            current_class_ids = enhanced_semantic['class_ids']
            
            weights = np.linspace(0.1, 0.9, len(self.semantic_history))
            weights = weights / weights.sum()
            
            smoothed_confidence = current_confidence * weights[-1]
            
            for i, hist_msg in enumerate(self.semantic_history[:-1]):
                try:
                    hist_data = self.reconstruct_semantic_data(hist_msg)
                    if hist_data is not None:
                        hist_confidence = hist_data['confidence']
                        hist_class_ids = hist_data['class_ids']
                        
                        same_class_mask = (hist_class_ids == current_class_ids)
                        smoothed_confidence[same_class_mask] += (
                            hist_confidence[same_class_mask] * weights[i])
                
                except Exception as e:
                    continue
            
            enhanced_semantic['confidence'] = smoothed_confidence
            return enhanced_semantic
            
        except Exception as e:
            return enhanced_semantic

    def publish_fusion_results(self, fusion_result):
        try:
            msg = SemanticSegmentation()
            msg.header.stamp = rospy.Time.now()
            msg.header.frame_id = "camera_link"
            msg.height = fusion_result['height']
            msg.width = fusion_result['width']
            
            msg.class_ids = fusion_result['class_ids'].flatten().tolist()
            msg.confidence = fusion_result['confidence'].flatten().tolist()
            msg.class_names = fusion_result['class_names']
            
            unique_classes = np.unique(fusion_result['class_ids'])
            msg.num_classes = len(unique_classes)
            msg.mean_confidence = float(np.mean(fusion_result['confidence']))
            
            self.fused_semantic_pub.publish(msg)
            
        except Exception as e:
            pass

if __name__ == '__main__':
    try:
        node = GeometrySemanticFusionNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```

## 3. 语义地图集成模块 (semantic_map_integration_node.py)

```python
#!/usr/bin/env python3
import rospy
import numpy as np
import tf2_ros
import tf2_geometry_msgs
from std_msgs.msg import Header
from sensor_msgs.msg import PointCloud2, Image
from geometry_msgs.msg import Point, Pose, PoseStamped
from visualization_msgs.msg import Marker, MarkerArray
from darknet_ros_msgs.msg import BoundingBoxes
from semantic_perception.msg import SemanticSegmentation, SemanticObject
import sensor_msgs.point_cloud2 as pc2

class SemanticMapIntegrationNode:
    def __init__(self):
        rospy.init_node('semantic_map_integration_node', anonymous=True)

        self.map_resolution = rospy.get_param('~map_resolution', 0.05)
        self.map_size_x = rospy.get_param('~map_size_x', 20.0)
        self.map_size_y = rospy.get_param('~map_size_y', 20.0)
        self.map_size_z = rospy.get_param('~map_size_z', 5.0)
        self.enable_semantic_mapping = rospy.get_param('~enable_semantic_mapping', True)
        self.enable_object_tracking = rospy.get_param('~enable_object_tracking', True)
        self.semantic_confidence_threshold = rospy.get_param('~semantic_confidence_threshold', 0.6)

        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)

        self.semantic_map = {}
        self.semantic_objects = {}
        self.fire_locations = []

        self.last_rtabmap_cloud = None
        self.last_semantic_data = None
        self.last_tsdf_cloud = None

        self.rtabmap_cloud_sub = rospy.Subscriber('rtabmap/cloud_map', PointCloud2, self.rtabmap_cloud_callback)
        self.semantic_segmentation_sub = rospy.Subscriber('semantic_segmentation', SemanticSegmentation, self.semantic_segmentation_callback)
        self.object_detection_sub = rospy.Subscriber('darknet_ros/bounding_boxes', BoundingBoxes, self.object_detection_callback)

        self.tsdf_cloud_sub = rospy.Subscriber('tsdf_pointcloud', PointCloud2, self.tsdf_cloud_callback)
        self.fused_semantic_sub = rospy.Subscriber('fused_semantic_segmentation', SemanticSegmentation, self.fused_semantic_callback)
        self.fire_detection_sub = rospy.Subscriber('fire_detections', SemanticObject, self.fire_detection_callback)
        self.enhanced_pointcloud_sub = rospy.Subscriber('enhanced_semantic_pointcloud', PointCloud2, self.enhanced_pointcloud_callback)

        self.semantic_map_pub = rospy.Publisher('semantic_map', PointCloud2, queue_size=1)
        self.semantic_markers_pub = rospy.Publisher('semantic_markers', MarkerArray, queue_size=1)
        self.fire_markers_pub = rospy.Publisher('fire_markers', MarkerArray, queue_size=1)

        self.integration_timer = rospy.Timer(rospy.Duration(1.0), self.integration_callback)
        self.publish_timer = rospy.Timer(rospy.Duration(0.5), self.publish_callback)

    def rtabmap_cloud_callback(self, msg):
        try:
            self.last_rtabmap_cloud = msg
        except Exception as e:
            pass

    def semantic_segmentation_callback(self, msg):
        try:
            self.last_semantic_data = msg
        except Exception as e:
            pass

    def tsdf_cloud_callback(self, msg):
        try:
            self.last_tsdf_cloud = msg
        except Exception as e:
            pass

    def fused_semantic_callback(self, msg):
        try:
            self.last_semantic_data = msg
        except Exception as e:
            pass

    def fire_detection_callback(self, msg):
        try:
            current_time = rospy.Time.now()

            fire_location = {
                'position': msg.position,
                'confidence': msg.confidence,
                'detected_time': current_time,
                'class_name': msg.class_name
            }

            self.fire_locations.append(fire_location)

        except Exception as e:
            pass

    def enhanced_pointcloud_callback(self, msg):
        try:
            pass
        except Exception as e:
            pass

    def object_detection_callback(self, msg):
        try:
            if not self.enable_object_tracking:
                return

            current_time = rospy.Time.now()

            for bbox in msg.bounding_boxes:
                if bbox.probability > self.semantic_confidence_threshold:
                    obj_id = f"{bbox.Class}_{bbox.id if hasattr(bbox, 'id') else len(self.semantic_objects)}"

                    self.semantic_objects[obj_id] = {
                        'class': bbox.Class,
                        'probability': bbox.probability,
                        'bbox': bbox,
                        'last_seen': current_time,
                        'position': self.estimate_object_position(bbox)
                    }

        except Exception as e:
            pass

    def estimate_object_position(self, bbox):
        try:
            center_x = (bbox.xmin + bbox.xmax) / 2.0
            center_y = (bbox.ymin + bbox.ymax) / 2.0

            estimated_depth = 2.0

            position = Point()
            position.x = center_x * estimated_depth / 1000.0
            position.y = center_y * estimated_depth / 1000.0
            position.z = estimated_depth

            return position

        except Exception as e:
            return Point()

    def integration_callback(self, event):
        try:
            if not self.enable_semantic_mapping:
                return

            current_time = rospy.Time.now()
            expired_objects = []

            for obj_id, obj_data in self.semantic_objects.items():
                if (current_time - obj_data['last_seen']).to_sec() > 10.0:
                    expired_objects.append(obj_id)

            for obj_id in expired_objects:
                del self.semantic_objects[obj_id]

            self.fire_locations = [
                fire for fire in self.fire_locations
                if (current_time - fire['detected_time']).to_sec() < 30.0
            ]

            self.process_fire_detections()
            self.integrate_enhanced_semantic_data()
            self.generate_emergency_zones()

        except Exception as e:
            pass

    def process_fire_detections(self):
        try:
            if not self.fire_locations:
                return

            for fire_data in self.fire_locations:
                grid_x, grid_y, grid_z = self.world_to_grid(
                    fire_data['position'].x,
                    fire_data['position'].y,
                    fire_data['position'].z
                )

                if self.is_valid_grid_position(grid_x, grid_y, grid_z):
                    grid_key = (grid_x, grid_y, grid_z)

                    if grid_key not in self.semantic_map:
                        self.semantic_map[grid_key] = {
                            'class_id': 255,
                            'class_name': 'fire',
                            'confidence': fire_data['confidence'],
                            'last_updated': rospy.Time.now()
                        }
                    else:
                        existing_confidence = self.semantic_map[grid_key]['confidence']
                        new_confidence = max(existing_confidence, fire_data['confidence'])
                        self.semantic_map[grid_key]['confidence'] = new_confidence
                        self.semantic_map[grid_key]['last_updated'] = rospy.Time.now()

        except Exception as e:
            pass

    def integrate_enhanced_semantic_data(self):
        try:
            if self.last_semantic_data is None:
                return

            semantic_data = self.last_semantic_data

            height = semantic_data.height
            width = semantic_data.width

            class_ids = np.array(semantic_data.class_ids).reshape(height, width)
            confidence = np.array(semantic_data.confidence).reshape(height, width)

            for v in range(0, height, 5):
                for u in range(0, width, 5):
                    if confidence[v, u] > self.semantic_confidence_threshold:
                        class_id = class_ids[v, u]
                        class_name = semantic_data.class_names[class_id] if class_id < len(semantic_data.class_names) else 'unknown'

                        world_point = self.pixel_to_world(u, v, 2.0)
                        if world_point is not None:
                            grid_x, grid_y, grid_z = self.world_to_grid(
                                world_point.x, world_point.y, world_point.z
                            )

                            if self.is_valid_grid_position(grid_x, grid_y, grid_z):
                                grid_key = (grid_x, grid_y, grid_z)

                                if grid_key not in self.semantic_map:
                                    self.semantic_map[grid_key] = {
                                        'class_id': class_id,
                                        'class_name': class_name,
                                        'confidence': confidence[v, u],
                                        'last_updated': rospy.Time.now()
                                    }
                                else:
                                    existing_confidence = self.semantic_map[grid_key]['confidence']
                                    if confidence[v, u] > existing_confidence:
                                        self.semantic_map[grid_key]['class_id'] = class_id
                                        self.semantic_map[grid_key]['class_name'] = class_name
                                        self.semantic_map[grid_key]['confidence'] = confidence[v, u]
                                        self.semantic_map[grid_key]['last_updated'] = rospy.Time.now()

        except Exception as e:
            pass

    def world_to_grid(self, x, y, z):
        grid_x = int((x + self.map_size_x / 2.0) / self.map_resolution)
        grid_y = int((y + self.map_size_y / 2.0) / self.map_resolution)
        grid_z = int(z / self.map_resolution)
        return grid_x, grid_y, grid_z

    def grid_to_world(self, grid_x, grid_y, grid_z):
        x = grid_x * self.map_resolution - self.map_size_x / 2.0
        y = grid_y * self.map_resolution - self.map_size_y / 2.0
        z = grid_z * self.map_resolution
        return x, y, z

    def is_valid_grid_position(self, grid_x, grid_y, grid_z):
        max_grid_x = int(self.map_size_x / self.map_resolution)
        max_grid_y = int(self.map_size_y / self.map_resolution)
        max_grid_z = int(self.map_size_z / self.map_resolution)

        return (0 <= grid_x < max_grid_x and
                0 <= grid_y < max_grid_y and
                0 <= grid_z < max_grid_z)

    def pixel_to_world(self, u, v, depth):
        try:
            fx = 525.0
            fy = 525.0
            cx = 320.0
            cy = 240.0

            x = (u - cx) * depth / fx
            y = (v - cy) * depth / fy
            z = depth

            point = Point()
            point.x = x
            point.y = y
            point.z = z

            return point

        except Exception as e:
            return None

    def publish_callback(self, event):
        try:
            self.publish_semantic_map()
            self.publish_semantic_markers()
            self.publish_fire_markers()
        except Exception as e:
            pass

    def publish_semantic_map(self):
        try:
            if not self.semantic_map:
                return

            points = []
            for (grid_x, grid_y, grid_z), semantic_data in self.semantic_map.items():
                x, y, z = self.grid_to_world(grid_x, grid_y, grid_z)

                class_id = semantic_data['class_id']
                confidence = semantic_data['confidence']

                point = [x, y, z, class_id, confidence]
                points.append(point)

            if points:
                header = Header()
                header.stamp = rospy.Time.now()
                header.frame_id = "map"

                fields = [
                    pc2.PointField('x', 0, pc2.PointField.FLOAT32, 1),
                    pc2.PointField('y', 4, pc2.PointField.FLOAT32, 1),
                    pc2.PointField('z', 8, pc2.PointField.FLOAT32, 1),
                    pc2.PointField('class_id', 12, pc2.PointField.UINT32, 1),
                    pc2.PointField('confidence', 16, pc2.PointField.FLOAT32, 1),
                ]

                cloud_msg = pc2.create_cloud(header, fields, points)
                self.semantic_map_pub.publish(cloud_msg)

        except Exception as e:
            pass

    def generate_emergency_zones(self):
        try:
            emergency_zones = []

            for fire_data in self.fire_locations:
                zone_center = fire_data['position']
                zone_radius = 2.0

                emergency_zone = {
                    'center': zone_center,
                    'radius': zone_radius,
                    'type': 'fire_hazard',
                    'confidence': fire_data['confidence']
                }

                emergency_zones.append(emergency_zone)

            self.emergency_zones = emergency_zones

        except Exception as e:
            pass

    def publish_semantic_markers(self):
        try:
            marker_array = MarkerArray()

            for i, (obj_id, obj_data) in enumerate(self.semantic_objects.items()):
                marker = Marker()
                marker.header.frame_id = "map"
                marker.header.stamp = rospy.Time.now()
                marker.ns = "semantic_objects"
                marker.id = i
                marker.type = Marker.CUBE
                marker.action = Marker.ADD

                marker.pose.position = obj_data['position']
                marker.pose.orientation.w = 1.0

                marker.scale.x = 0.5
                marker.scale.y = 0.5
                marker.scale.z = 0.5

                marker.color.r = 0.0
                marker.color.g = 1.0
                marker.color.b = 0.0
                marker.color.a = 0.8

                marker.lifetime = rospy.Duration(2.0)

                marker_array.markers.append(marker)

            self.semantic_markers_pub.publish(marker_array)

        except Exception as e:
            pass

    def publish_fire_markers(self):
        try:
            marker_array = MarkerArray()

            for i, fire_data in enumerate(self.fire_locations):
                marker = Marker()
                marker.header.frame_id = "map"
                marker.header.stamp = rospy.Time.now()
                marker.ns = "fire_locations"
                marker.id = i
                marker.type = Marker.SPHERE
                marker.action = Marker.ADD

                marker.pose.position = fire_data['position']
                marker.pose.orientation.w = 1.0

                marker.scale.x = 0.3
                marker.scale.y = 0.3
                marker.scale.z = 0.3

                marker.color.r = 1.0
                marker.color.g = 0.0
                marker.color.b = 0.0
                marker.color.a = 1.0

                marker.lifetime = rospy.Duration(5.0)

                marker_array.markers.append(marker)

            self.fire_markers_pub.publish(marker_array)

        except Exception as e:
            pass

if __name__ == '__main__':
    try:
        node = SemanticMapIntegrationNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```

## 4. 目标检测与跟踪模块 (object_detection_tracking.py)

```python
#!/usr/bin/env python3
import rospy
import cv2
import numpy as np
import torch
import torchvision.transforms as transforms
from collections import defaultdict, deque
import threading
from std_msgs.msg import Header
from sensor_msgs.msg import Image, CameraInfo
from geometry_msgs.msg import Point, Pose, PoseStamped
from visualization_msgs.msg import Marker, MarkerArray
from darknet_ros_msgs.msg import BoundingBox, BoundingBoxes
from cv_bridge import CvBridge
from semantic_perception.msg import SemanticObject, TrackedObject

class ObjectDetectionTrackingNode:
    def __init__(self):
        rospy.init_node('object_detection_tracking_node', anonymous=True)

        self.bridge = CvBridge()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        self.detection_model_path = rospy.get_param('~detection_model_path', '')
        self.tracking_enabled = rospy.get_param('~tracking_enabled', True)
        self.confidence_threshold = rospy.get_param('~confidence_threshold', 0.5)
        self.nms_threshold = rospy.get_param('~nms_threshold', 0.4)
        self.max_track_age = rospy.get_param('~max_track_age', 30)
        self.min_track_hits = rospy.get_param('~min_track_hits', 3)

        self.class_names = [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
            'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
            'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
            'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
            'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
            'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
            'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
            'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
            'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
            'toothbrush'
        ]

        self.detection_model = None
        self.load_detection_model()

        self.tracked_objects = {}
        self.next_track_id = 0
        self.track_history = defaultdict(lambda: deque(maxlen=50))

        self.camera_info = None
        self.latest_image = None
        self.data_lock = threading.Lock()

        self.image_sub = rospy.Subscriber('image_raw', Image, self.image_callback, queue_size=1)
        self.camera_info_sub = rospy.Subscriber('camera_info', CameraInfo, self.camera_info_callback, queue_size=1)

        self.detection_pub = rospy.Publisher('object_detections', BoundingBoxes, queue_size=1)
        self.tracking_pub = rospy.Publisher('tracked_objects', TrackedObject, queue_size=1)
        self.visualization_pub = rospy.Publisher('detection_visualization', Image, queue_size=1)
        self.marker_pub = rospy.Publisher('object_markers', MarkerArray, queue_size=1)

        self.detection_timer = rospy.Timer(rospy.Duration(0.1), self.detection_callback)

    def load_detection_model(self):
        try:
            if self.detection_model_path and self.detection_model_path.endswith('.pt'):
                self.detection_model = torch.jit.load(self.detection_model_path, map_location=self.device)
                self.detection_model.eval()
            else:
                import torchvision.models as models
                self.detection_model = models.detection.fasterrcnn_resnet50_fpn(pretrained=True)
                self.detection_model.to(self.device)
                self.detection_model.eval()

            self.transform = transforms.Compose([
                transforms.ToPILImage(),
                transforms.ToTensor()
            ])

        except Exception as e:
            self.detection_model = None

    def camera_info_callback(self, msg):
        self.camera_info = msg

    def image_callback(self, msg):
        with self.data_lock:
            self.latest_image = msg

    def detection_callback(self, event):
        try:
            with self.data_lock:
                if self.latest_image is None:
                    return
                image_msg = self.latest_image

            cv_image = self.bridge.imgmsg_to_cv2(image_msg, "bgr8")

            detections = self.detect_objects(cv_image)

            if self.tracking_enabled:
                tracked_objects = self.update_tracking(detections, image_msg.header.stamp)
                self.publish_tracking_results(tracked_objects, image_msg.header)

            self.publish_detection_results(detections, image_msg.header)

            vis_image = self.create_visualization(cv_image, detections)
            self.publish_visualization(vis_image, image_msg.header)

        except Exception as e:
            pass

    def detect_objects(self, image):
        if self.detection_model is None:
            return self.generate_dummy_detections(image)

        try:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            input_tensor = self.transform(image_rgb).unsqueeze(0).to(self.device)

            with torch.no_grad():
                predictions = self.detection_model(input_tensor)

            return self.process_predictions(predictions[0], image.shape)

        except Exception as e:
            return self.generate_dummy_detections(image)

    def process_predictions(self, prediction, image_shape):
        detections = []

        boxes = prediction['boxes'].cpu().numpy()
        scores = prediction['scores'].cpu().numpy()
        labels = prediction['labels'].cpu().numpy()

        for i in range(len(boxes)):
            if scores[i] > self.confidence_threshold:
                box = boxes[i]

                detection = {
                    'class_id': int(labels[i]),
                    'class_name': self.class_names[labels[i]] if labels[i] < len(self.class_names) else 'unknown',
                    'confidence': float(scores[i]),
                    'bbox': {
                        'xmin': int(box[0]),
                        'ymin': int(box[1]),
                        'xmax': int(box[2]),
                        'ymax': int(box[3])
                    },
                    'center': {
                        'x': int((box[0] + box[2]) / 2),
                        'y': int((box[1] + box[3]) / 2)
                    },
                    'area': int((box[2] - box[0]) * (box[3] - box[1]))
                }

                detections.append(detection)

        return self.apply_nms(detections)

    def apply_nms(self, detections):
        if not detections:
            return detections

        boxes = []
        scores = []

        for det in detections:
            bbox = det['bbox']
            boxes.append([bbox['xmin'], bbox['ymin'], bbox['xmax'], bbox['ymax']])
            scores.append(det['confidence'])

        boxes = torch.tensor(boxes, dtype=torch.float32)
        scores = torch.tensor(scores, dtype=torch.float32)

        keep_indices = torch.ops.torchvision.nms(boxes, scores, self.nms_threshold)

        return [detections[i] for i in keep_indices]

    def generate_dummy_detections(self, image):
        h, w = image.shape[:2]
        detections = []

        for i in range(np.random.randint(0, 5)):
            x1 = np.random.randint(0, w//2)
            y1 = np.random.randint(0, h//2)
            x2 = np.random.randint(x1 + 50, w)
            y2 = np.random.randint(y1 + 50, h)

            detection = {
                'class_id': np.random.randint(0, len(self.class_names)),
                'class_name': self.class_names[np.random.randint(0, len(self.class_names))],
                'confidence': np.random.uniform(0.5, 1.0),
                'bbox': {'xmin': x1, 'ymin': y1, 'xmax': x2, 'ymax': y2},
                'center': {'x': (x1 + x2) // 2, 'y': (y1 + y2) // 2},
                'area': (x2 - x1) * (y2 - y1)
            }
            detections.append(detection)

        return detections

    def update_tracking(self, detections, timestamp):
        current_time = timestamp.to_sec()

        unmatched_detections = list(range(len(detections)))
        unmatched_tracks = list(self.tracked_objects.keys())

        matches = self.associate_detections_to_tracks(detections, unmatched_detections, unmatched_tracks)

        for det_idx, track_id in matches:
            self.update_track(track_id, detections[det_idx], current_time)
            unmatched_detections.remove(det_idx)
            unmatched_tracks.remove(track_id)

        for det_idx in unmatched_detections:
            self.create_new_track(detections[det_idx], current_time)

        for track_id in unmatched_tracks:
            self.tracked_objects[track_id]['age'] += 1
            self.tracked_objects[track_id]['time_since_update'] += 1

        self.cleanup_old_tracks()

        return self.get_confirmed_tracks()

    def associate_detections_to_tracks(self, detections, unmatched_detections, unmatched_tracks):
        if not unmatched_detections or not unmatched_tracks:
            return []

        iou_matrix = np.zeros((len(unmatched_detections), len(unmatched_tracks)))

        for d, det_idx in enumerate(unmatched_detections):
            for t, track_id in enumerate(unmatched_tracks):
                iou_matrix[d, t] = self.calculate_iou(
                    detections[det_idx]['bbox'],
                    self.tracked_objects[track_id]['bbox']
                )

        matches = []
        min_iou = 0.3

        while True:
            max_iou = np.max(iou_matrix)
            if max_iou < min_iou:
                break

            max_indices = np.unravel_index(np.argmax(iou_matrix), iou_matrix.shape)
            det_idx = unmatched_detections[max_indices[0]]
            track_id = unmatched_tracks[max_indices[1]]

            matches.append((det_idx, track_id))

            iou_matrix[max_indices[0], :] = 0
            iou_matrix[:, max_indices[1]] = 0

        return matches

    def calculate_iou(self, bbox1, bbox2):
        x1 = max(bbox1['xmin'], bbox2['xmin'])
        y1 = max(bbox1['ymin'], bbox2['ymin'])
        x2 = min(bbox1['xmax'], bbox2['xmax'])
        y2 = min(bbox1['ymax'], bbox2['ymax'])

        if x2 <= x1 or y2 <= y1:
            return 0.0

        intersection = (x2 - x1) * (y2 - y1)

        area1 = (bbox1['xmax'] - bbox1['xmin']) * (bbox1['ymax'] - bbox1['ymin'])
        area2 = (bbox2['xmax'] - bbox2['xmin']) * (bbox2['ymax'] - bbox2['ymin'])

        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def create_new_track(self, detection, current_time):
        track_id = self.next_track_id
        self.next_track_id += 1

        self.tracked_objects[track_id] = {
            'id': track_id,
            'class_id': detection['class_id'],
            'class_name': detection['class_name'],
            'bbox': detection['bbox'],
            'center': detection['center'],
            'confidence': detection['confidence'],
            'hits': 1,
            'age': 1,
            'time_since_update': 0,
            'created_time': current_time,
            'last_update_time': current_time,
            'velocity': {'x': 0.0, 'y': 0.0},
            'predicted_bbox': detection['bbox']
        }

        self.track_history[track_id].append(detection['center'])

    def update_track(self, track_id, detection, current_time):
        track = self.tracked_objects[track_id]

        dt = current_time - track['last_update_time']
        if dt > 0:
            dx = detection['center']['x'] - track['center']['x']
            dy = detection['center']['y'] - track['center']['y']
            track['velocity']['x'] = dx / dt
            track['velocity']['y'] = dy / dt

        track['bbox'] = detection['bbox']
        track['center'] = detection['center']
        track['confidence'] = detection['confidence']
        track['hits'] += 1
        track['time_since_update'] = 0
        track['last_update_time'] = current_time

        self.track_history[track_id].append(detection['center'])

    def cleanup_old_tracks(self):
        tracks_to_remove = []

        for track_id, track in self.tracked_objects.items():
            if (track['time_since_update'] > self.max_track_age or
                (track['hits'] < self.min_track_hits and track['age'] > 10)):
                tracks_to_remove.append(track_id)

        for track_id in tracks_to_remove:
            del self.tracked_objects[track_id]
            if track_id in self.track_history:
                del self.track_history[track_id]

    def get_confirmed_tracks(self):
        confirmed_tracks = []

        for track_id, track in self.tracked_objects.items():
            if track['hits'] >= self.min_track_hits and track['time_since_update'] <= 1:
                confirmed_tracks.append(track)

        return confirmed_tracks

    def publish_detection_results(self, detections, header):
        try:
            msg = BoundingBoxes()
            msg.header = header
            msg.image_header = header

            for detection in detections:
                bbox = BoundingBox()
                bbox.Class = detection['class_name']
                bbox.probability = detection['confidence']
                bbox.xmin = detection['bbox']['xmin']
                bbox.ymin = detection['bbox']['ymin']
                bbox.xmax = detection['bbox']['xmax']
                bbox.ymax = detection['bbox']['ymax']

                msg.bounding_boxes.append(bbox)

            self.detection_pub.publish(msg)

        except Exception as e:
            pass

    def publish_tracking_results(self, tracked_objects, header):
        try:
            for track in tracked_objects:
                msg = TrackedObject()
                msg.header = header
                msg.id = track['id']
                msg.class_name = track['class_name']
                msg.confidence = track['confidence']

                msg.bbox.xmin = track['bbox']['xmin']
                msg.bbox.ymin = track['bbox']['ymin']
                msg.bbox.xmax = track['bbox']['xmax']
                msg.bbox.ymax = track['bbox']['ymax']

                msg.center.x = track['center']['x']
                msg.center.y = track['center']['y']

                msg.velocity.x = track['velocity']['x']
                msg.velocity.y = track['velocity']['y']

                msg.age = track['age']
                msg.hits = track['hits']

                if self.camera_info:
                    world_position = self.pixel_to_world(track['center']['x'], track['center']['y'], 2.0)
                    if world_position:
                        msg.position = world_position

                self.tracking_pub.publish(msg)

        except Exception as e:
            pass

    def pixel_to_world(self, u, v, depth):
        try:
            if not self.camera_info:
                return None

            fx = self.camera_info.K[0]
            fy = self.camera_info.K[4]
            cx = self.camera_info.K[2]
            cy = self.camera_info.K[5]

            x = (u - cx) * depth / fx
            y = (v - cy) * depth / fy
            z = depth

            point = Point()
            point.x = x
            point.y = y
            point.z = z

            return point

        except Exception as e:
            return None

    def create_visualization(self, image, detections):
        vis_image = image.copy()

        for detection in detections:
            bbox = detection['bbox']
            class_name = detection['class_name']
            confidence = detection['confidence']

            cv2.rectangle(vis_image,
                         (bbox['xmin'], bbox['ymin']),
                         (bbox['xmax'], bbox['ymax']),
                         (0, 255, 0), 2)

            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]

            cv2.rectangle(vis_image,
                         (bbox['xmin'], bbox['ymin'] - label_size[1] - 10),
                         (bbox['xmin'] + label_size[0], bbox['ymin']),
                         (0, 255, 0), -1)

            cv2.putText(vis_image, label,
                       (bbox['xmin'], bbox['ymin'] - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)

        for track_id, track in self.tracked_objects.items():
            if track['hits'] >= self.min_track_hits:
                center = track['center']
                cv2.circle(vis_image, (center['x'], center['y']), 5, (255, 0, 0), -1)

                track_label = f"ID: {track_id}"
                cv2.putText(vis_image, track_label,
                           (center['x'] + 10, center['y']),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)

                if track_id in self.track_history and len(self.track_history[track_id]) > 1:
                    points = list(self.track_history[track_id])
                    for i in range(1, len(points)):
                        cv2.line(vis_image,
                                (points[i-1]['x'], points[i-1]['y']),
                                (points[i]['x'], points[i]['y']),
                                (0, 0, 255), 2)

        return vis_image

    def publish_visualization(self, vis_image, header):
        try:
            vis_msg = self.bridge.cv2_to_imgmsg(vis_image, "bgr8")
            vis_msg.header = header
            self.visualization_pub.publish(vis_msg)
        except Exception as e:
            pass

if __name__ == '__main__':
    try:
        node = ObjectDetectionTrackingNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```

## 5. 火灾检测专用模块 (fire_detection_node.py)

```python
#!/usr/bin/env python3
import rospy
import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from collections import deque
import threading
from std_msgs.msg import Header, Bool
from sensor_msgs.msg import Image, CameraInfo
from geometry_msgs.msg import Point, PointStamped
from visualization_msgs.msg import Marker, MarkerArray
from cv_bridge import CvBridge
from semantic_perception.msg import SemanticObject, FireAlert

class FireDetectionCNN(nn.Module):
    def __init__(self, num_classes=2):
        super(FireDetectionCNN, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1)

        self.pool = nn.MaxPool2d(2, 2)
        self.dropout = nn.Dropout(0.5)

        self.fc1 = nn.Linear(256 * 8 * 8, 512)
        self.fc2 = nn.Linear(512, 128)
        self.fc3 = nn.Linear(128, num_classes)

    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = self.pool(F.relu(self.conv3(x)))
        x = self.pool(F.relu(self.conv4(x)))

        x = x.view(-1, 256 * 8 * 8)
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)

        return x

class FireDetectionNode:
    def __init__(self):
        rospy.init_node('fire_detection_node', anonymous=True)

        self.bridge = CvBridge()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        self.model_path = rospy.get_param('~model_path', '')
        self.confidence_threshold = rospy.get_param('~confidence_threshold', 0.7)
        self.enable_color_detection = rospy.get_param('~enable_color_detection', True)
        self.enable_motion_detection = rospy.get_param('~enable_motion_detection', True)
        self.enable_cnn_detection = rospy.get_param('~enable_cnn_detection', True)
        self.alert_cooldown = rospy.get_param('~alert_cooldown', 5.0)

        self.fire_model = None
        self.load_fire_model()

        self.camera_info = None
        self.latest_image = None
        self.previous_image = None
        self.data_lock = threading.Lock()

        self.fire_history = deque(maxlen=10)
        self.last_alert_time = 0.0
        self.fire_regions = []

        self.image_sub = rospy.Subscriber('image_raw', Image, self.image_callback, queue_size=1)
        self.camera_info_sub = rospy.Subscriber('camera_info', CameraInfo, self.camera_info_callback, queue_size=1)

        self.fire_detection_pub = rospy.Publisher('fire_detections', SemanticObject, queue_size=1)
        self.fire_alert_pub = rospy.Publisher('fire_alert', FireAlert, queue_size=1)
        self.fire_visualization_pub = rospy.Publisher('fire_visualization', Image, queue_size=1)
        self.fire_markers_pub = rospy.Publisher('fire_detection_markers', MarkerArray, queue_size=1)

        self.detection_timer = rospy.Timer(rospy.Duration(0.2), self.detection_callback)

    def load_fire_model(self):
        try:
            if self.model_path and self.model_path.endswith('.pth'):
                self.fire_model = FireDetectionCNN(num_classes=2)
                self.fire_model.load_state_dict(torch.load(self.model_path, map_location=self.device))
                self.fire_model.to(self.device)
                self.fire_model.eval()
            else:
                self.fire_model = FireDetectionCNN(num_classes=2)
                self.fire_model.to(self.device)
                self.fire_model.eval()

            self.transform = transforms.Compose([
                transforms.ToPILImage(),
                transforms.Resize((128, 128)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])

        except Exception as e:
            self.fire_model = None

    def camera_info_callback(self, msg):
        self.camera_info = msg

    def image_callback(self, msg):
        with self.data_lock:
            self.previous_image = self.latest_image
            self.latest_image = msg

    def detection_callback(self, event):
        try:
            with self.data_lock:
                if self.latest_image is None:
                    return
                image_msg = self.latest_image
                prev_image_msg = self.previous_image

            cv_image = self.bridge.imgmsg_to_cv2(image_msg, "bgr8")

            fire_detections = []

            if self.enable_color_detection:
                color_detections = self.detect_fire_by_color(cv_image)
                fire_detections.extend(color_detections)

            if self.enable_motion_detection and prev_image_msg is not None:
                prev_cv_image = self.bridge.imgmsg_to_cv2(prev_image_msg, "bgr8")
                motion_detections = self.detect_fire_by_motion(cv_image, prev_cv_image)
                fire_detections.extend(motion_detections)

            if self.enable_cnn_detection and self.fire_model is not None:
                cnn_detections = self.detect_fire_by_cnn(cv_image)
                fire_detections.extend(cnn_detections)

            combined_detections = self.combine_detections(fire_detections)

            self.fire_history.append(len(combined_detections) > 0)

            if self.should_trigger_alert(combined_detections):
                self.publish_fire_alert(combined_detections, image_msg.header)

            self.publish_fire_detections(combined_detections, image_msg.header)

            vis_image = self.create_fire_visualization(cv_image, combined_detections)
            self.publish_fire_visualization(vis_image, image_msg.header)

        except Exception as e:
            pass

    def detect_fire_by_color(self, image):
        detections = []

        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 50, 50])
        upper_red2 = np.array([180, 255, 255])

        lower_orange = np.array([10, 50, 50])
        upper_orange = np.array([25, 255, 255])

        lower_yellow = np.array([25, 50, 50])
        upper_yellow = np.array([35, 255, 255])

        mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask_orange = cv2.inRange(hsv, lower_orange, upper_orange)
        mask_yellow = cv2.inRange(hsv, lower_yellow, upper_yellow)

        fire_mask = mask_red1 + mask_red2 + mask_orange + mask_yellow

        kernel = np.ones((5, 5), np.uint8)
        fire_mask = cv2.morphologyEx(fire_mask, cv2.MORPH_CLOSE, kernel)
        fire_mask = cv2.morphologyEx(fire_mask, cv2.MORPH_OPEN, kernel)

        contours, _ = cv2.findContours(fire_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 500:
                x, y, w, h = cv2.boundingRect(contour)

                roi = image[y:y+h, x:x+w]
                confidence = self.calculate_color_confidence(roi)

                if confidence > 0.3:
                    detection = {
                        'type': 'color',
                        'bbox': {'x': x, 'y': y, 'width': w, 'height': h},
                        'center': {'x': x + w//2, 'y': y + h//2},
                        'confidence': confidence,
                        'area': area
                    }
                    detections.append(detection)

        return detections

    def calculate_color_confidence(self, roi):
        if roi.size == 0:
            return 0.0

        hsv_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)

        fire_pixels = 0
        total_pixels = roi.shape[0] * roi.shape[1]

        for i in range(roi.shape[0]):
            for j in range(roi.shape[1]):
                h, s, v = hsv_roi[i, j]

                if ((0 <= h <= 10 or 170 <= h <= 180) and s >= 50 and v >= 50) or \
                   (10 <= h <= 35 and s >= 50 and v >= 50):
                    fire_pixels += 1

        return fire_pixels / total_pixels if total_pixels > 0 else 0.0

    def detect_fire_by_motion(self, current_image, previous_image):
        detections = []

        gray_current = cv2.cvtColor(current_image, cv2.COLOR_BGR2GRAY)
        gray_previous = cv2.cvtColor(previous_image, cv2.COLOR_BGR2GRAY)

        diff = cv2.absdiff(gray_current, gray_previous)

        _, motion_mask = cv2.threshold(diff, 25, 255, cv2.THRESH_BINARY)

        kernel = np.ones((5, 5), np.uint8)
        motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_CLOSE, kernel)

        contours, _ = cv2.findContours(motion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 300:
                x, y, w, h = cv2.boundingRect(contour)

                roi_current = current_image[y:y+h, x:x+w]
                motion_confidence = self.calculate_motion_confidence(roi_current, area)

                if motion_confidence > 0.2:
                    detection = {
                        'type': 'motion',
                        'bbox': {'x': x, 'y': y, 'width': w, 'height': h},
                        'center': {'x': x + w//2, 'y': y + h//2},
                        'confidence': motion_confidence,
                        'area': area
                    }
                    detections.append(detection)

        return detections

    def calculate_motion_confidence(self, roi, area):
        if roi.size == 0:
            return 0.0

        brightness = np.mean(cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY))

        area_factor = min(area / 1000.0, 1.0)
        brightness_factor = min(brightness / 128.0, 1.0)

        return area_factor * brightness_factor * 0.5

    def detect_fire_by_cnn(self, image):
        detections = []

        try:
            h, w = image.shape[:2]
            window_size = 128
            stride = 64

            for y in range(0, h - window_size + 1, stride):
                for x in range(0, w - window_size + 1, stride):
                    roi = image[y:y+window_size, x:x+window_size]

                    input_tensor = self.transform(roi).unsqueeze(0).to(self.device)

                    with torch.no_grad():
                        output = self.fire_model(input_tensor)
                        probabilities = F.softmax(output, dim=1)
                        fire_prob = probabilities[0][1].item()

                    if fire_prob > self.confidence_threshold:
                        detection = {
                            'type': 'cnn',
                            'bbox': {'x': x, 'y': y, 'width': window_size, 'height': window_size},
                            'center': {'x': x + window_size//2, 'y': y + window_size//2},
                            'confidence': fire_prob,
                            'area': window_size * window_size
                        }
                        detections.append(detection)

        except Exception as e:
            pass

        return detections

    def combine_detections(self, all_detections):
        if not all_detections:
            return []

        combined = []

        for detection in all_detections:
            merged = False

            for existing in combined:
                if self.calculate_overlap(detection['bbox'], existing['bbox']) > 0.3:
                    existing['confidence'] = max(existing['confidence'], detection['confidence'])
                    existing['types'] = existing.get('types', [existing['type']]) + [detection['type']]
                    merged = True
                    break

            if not merged:
                detection['types'] = [detection['type']]
                combined.append(detection)

        return combined

    def calculate_overlap(self, bbox1, bbox2):
        x1_min, y1_min = bbox1['x'], bbox1['y']
        x1_max, y1_max = x1_min + bbox1['width'], y1_min + bbox1['height']

        x2_min, y2_min = bbox2['x'], bbox2['y']
        x2_max, y2_max = x2_min + bbox2['width'], y2_min + bbox2['height']

        overlap_x = max(0, min(x1_max, x2_max) - max(x1_min, x2_min))
        overlap_y = max(0, min(y1_max, y2_max) - max(y1_min, y2_min))

        overlap_area = overlap_x * overlap_y

        area1 = bbox1['width'] * bbox1['height']
        area2 = bbox2['width'] * bbox2['height']

        union_area = area1 + area2 - overlap_area

        return overlap_area / union_area if union_area > 0 else 0.0

    def should_trigger_alert(self, detections):
        current_time = rospy.Time.now().to_sec()

        if current_time - self.last_alert_time < self.alert_cooldown:
            return False

        if len(detections) == 0:
            return False

        high_confidence_detections = [d for d in detections if d['confidence'] > 0.8]
        if len(high_confidence_detections) > 0:
            return True

        recent_fire_count = sum(self.fire_history)
        if recent_fire_count >= 5:
            return True

        return False

    def publish_fire_alert(self, detections, header):
        try:
            current_time = rospy.Time.now().to_sec()
            self.last_alert_time = current_time

            alert_msg = FireAlert()
            alert_msg.header = header
            alert_msg.alert_level = 'HIGH' if any(d['confidence'] > 0.9 for d in detections) else 'MEDIUM'
            alert_msg.num_detections = len(detections)
            alert_msg.max_confidence = max(d['confidence'] for d in detections) if detections else 0.0
            alert_msg.detection_types = list(set([t for d in detections for t in d.get('types', [d['type']])]))

            for detection in detections:
                fire_location = Point()
                if self.camera_info:
                    world_pos = self.pixel_to_world(detection['center']['x'], detection['center']['y'], 3.0)
                    if world_pos:
                        fire_location = world_pos

                alert_msg.fire_locations.append(fire_location)

            self.fire_alert_pub.publish(alert_msg)

        except Exception as e:
            pass

    def publish_fire_detections(self, detections, header):
        try:
            for detection in detections:
                msg = SemanticObject()
                msg.header = header
                msg.class_name = 'fire'
                msg.confidence = detection['confidence']

                if self.camera_info:
                    world_position = self.pixel_to_world(detection['center']['x'], detection['center']['y'], 3.0)
                    if world_position:
                        msg.position = world_position

                msg.bbox.x = detection['bbox']['x']
                msg.bbox.y = detection['bbox']['y']
                msg.bbox.width = detection['bbox']['width']
                msg.bbox.height = detection['bbox']['height']

                self.fire_detection_pub.publish(msg)

        except Exception as e:
            pass

    def pixel_to_world(self, u, v, depth):
        try:
            if not self.camera_info:
                return None

            fx = self.camera_info.K[0]
            fy = self.camera_info.K[4]
            cx = self.camera_info.K[2]
            cy = self.camera_info.K[5]

            x = (u - cx) * depth / fx
            y = (v - cy) * depth / fy
            z = depth

            point = Point()
            point.x = x
            point.y = y
            point.z = z

            return point

        except Exception as e:
            return None

    def create_fire_visualization(self, image, detections):
        vis_image = image.copy()

        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            types = detection.get('types', [detection['type']])

            color = (0, 0, 255) if confidence > 0.8 else (0, 165, 255)

            cv2.rectangle(vis_image,
                         (bbox['x'], bbox['y']),
                         (bbox['x'] + bbox['width'], bbox['y'] + bbox['height']),
                         color, 2)

            label = f"FIRE: {confidence:.2f} ({','.join(types)})"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

            cv2.rectangle(vis_image,
                         (bbox['x'], bbox['y'] - label_size[1] - 10),
                         (bbox['x'] + label_size[0], bbox['y']),
                         color, -1)

            cv2.putText(vis_image, label,
                       (bbox['x'], bbox['y'] - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return vis_image

    def publish_fire_visualization(self, vis_image, header):
        try:
            vis_msg = self.bridge.cv2_to_imgmsg(vis_image, "bgr8")
            vis_msg.header = header
            self.fire_visualization_pub.publish(vis_msg)
        except Exception as e:
            pass

if __name__ == '__main__':
    try:
        node = FireDetectionNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```

## 6. 语义场景图谱生成模块 (scene_graph_generator.py)

```python
#!/usr/bin/env python3
import rospy
import numpy as np
import networkx as nx
from collections import defaultdict, deque
import threading
import json
from std_msgs.msg import Header, String
from sensor_msgs.msg import PointCloud2
from geometry_msgs.msg import Point, Pose, PoseStamped
from visualization_msgs.msg import Marker, MarkerArray
from semantic_perception.msg import SemanticSegmentation, SemanticObject, SceneGraph, ObjectRelation

class SceneGraphGenerator:
    def __init__(self):
        rospy.init_node('scene_graph_generator', anonymous=True)

        self.enable_spatial_relations = rospy.get_param('~enable_spatial_relations', True)
        self.enable_semantic_relations = rospy.get_param('~enable_semantic_relations', True)
        self.enable_temporal_relations = rospy.get_param('~enable_temporal_relations', True)
        self.relation_confidence_threshold = rospy.get_param('~relation_confidence_threshold', 0.5)
        self.max_relation_distance = rospy.get_param('~max_relation_distance', 5.0)

        self.scene_graph = nx.DiGraph()
        self.object_history = defaultdict(lambda: deque(maxlen=50))
        self.spatial_relations = ['left_of', 'right_of', 'above', 'below', 'in_front_of', 'behind', 'near', 'far']
        self.semantic_relations = ['supports', 'contains', 'part_of', 'attached_to', 'similar_to']
        self.temporal_relations = ['appears_before', 'appears_after', 'co_occurs']

        self.detected_objects = {}
        self.object_positions = {}
        self.data_lock = threading.Lock()

        self.semantic_sub = rospy.Subscriber('semantic_segmentation', SemanticSegmentation, self.semantic_callback, queue_size=1)
        self.object_sub = rospy.Subscriber('tracked_objects', SemanticObject, self.object_callback, queue_size=1)
        self.pointcloud_sub = rospy.Subscriber('semantic_pointcloud', PointCloud2, self.pointcloud_callback, queue_size=1)

        self.scene_graph_pub = rospy.Publisher('scene_graph', SceneGraph, queue_size=1)
        self.graph_visualization_pub = rospy.Publisher('scene_graph_markers', MarkerArray, queue_size=1)
        self.graph_json_pub = rospy.Publisher('scene_graph_json', String, queue_size=1)

        self.update_timer = rospy.Timer(rospy.Duration(2.0), self.update_scene_graph)

    def semantic_callback(self, msg):
        try:
            with self.data_lock:
                self.process_semantic_segmentation(msg)
        except Exception as e:
            pass

    def object_callback(self, msg):
        try:
            with self.data_lock:
                self.process_detected_object(msg)
        except Exception as e:
            pass

    def pointcloud_callback(self, msg):
        try:
            with self.data_lock:
                self.process_semantic_pointcloud(msg)
        except Exception as e:
            pass

    def process_semantic_segmentation(self, msg):
        height = msg.height
        width = msg.width
        class_ids = np.array(msg.class_ids).reshape(height, width)
        confidence = np.array(msg.confidence).reshape(height, width)

        unique_classes = np.unique(class_ids)

        for class_id in unique_classes:
            if class_id < len(msg.class_names):
                class_name = msg.class_names[class_id]
                mask = (class_ids == class_id) & (confidence > 0.6)

                if np.sum(mask) > 100:
                    y_coords, x_coords = np.where(mask)
                    center_x = np.mean(x_coords)
                    center_y = np.mean(y_coords)

                    object_id = f"seg_{class_name}_{int(center_x)}_{int(center_y)}"

                    self.add_or_update_object(object_id, class_name, center_x, center_y, np.mean(confidence[mask]))

    def process_detected_object(self, msg):
        object_id = f"det_{msg.class_name}_{msg.header.stamp.secs}"

        if hasattr(msg, 'position') and msg.position:
            self.object_positions[object_id] = msg.position

        self.add_or_update_object(object_id, msg.class_name,
                                msg.bbox.x + msg.bbox.width/2,
                                msg.bbox.y + msg.bbox.height/2,
                                msg.confidence)

    def process_semantic_pointcloud(self, msg):
        pass

    def add_or_update_object(self, object_id, class_name, x, y, confidence):
        current_time = rospy.Time.now()

        object_data = {
            'id': object_id,
            'class_name': class_name,
            'position_2d': {'x': x, 'y': y},
            'confidence': confidence,
            'last_seen': current_time,
            'attributes': self.extract_object_attributes(class_name)
        }

        if object_id in self.object_positions:
            object_data['position_3d'] = self.object_positions[object_id]

        self.detected_objects[object_id] = object_data
        self.object_history[object_id].append(object_data)

        if not self.scene_graph.has_node(object_id):
            self.scene_graph.add_node(object_id, **object_data)
        else:
            self.scene_graph.nodes[object_id].update(object_data)

    def extract_object_attributes(self, class_name):
        attributes = []

        furniture_objects = ['chair', 'table', 'sofa', 'bed', 'desk']
        electronic_objects = ['tv', 'laptop', 'phone', 'microwave']
        living_objects = ['person', 'cat', 'dog', 'plant']

        if class_name in furniture_objects:
            attributes.append('furniture')
        elif class_name in electronic_objects:
            attributes.append('electronic')
        elif class_name in living_objects:
            attributes.append('living')

        if class_name in ['table', 'desk', 'bed']:
            attributes.append('surface')

        if class_name in ['person', 'cat', 'dog']:
            attributes.append('movable')
        else:
            attributes.append('static')

        return attributes

    def update_scene_graph(self, event):
        try:
            with self.data_lock:
                self.cleanup_old_objects()

                if self.enable_spatial_relations:
                    self.update_spatial_relations()

                if self.enable_semantic_relations:
                    self.update_semantic_relations()

                if self.enable_temporal_relations:
                    self.update_temporal_relations()

                self.publish_scene_graph()
                self.publish_graph_visualization()
                self.publish_graph_json()

        except Exception as e:
            pass

    def cleanup_old_objects(self):
        current_time = rospy.Time.now()
        objects_to_remove = []

        for object_id, object_data in self.detected_objects.items():
            time_diff = (current_time - object_data['last_seen']).to_sec()
            if time_diff > 30.0:
                objects_to_remove.append(object_id)

        for object_id in objects_to_remove:
            if object_id in self.detected_objects:
                del self.detected_objects[object_id]
            if object_id in self.object_history:
                del self.object_history[object_id]
            if self.scene_graph.has_node(object_id):
                self.scene_graph.remove_node(object_id)

    def update_spatial_relations(self):
        object_ids = list(self.detected_objects.keys())

        for i, obj1_id in enumerate(object_ids):
            for j, obj2_id in enumerate(object_ids[i+1:], i+1):
                obj1 = self.detected_objects[obj1_id]
                obj2 = self.detected_objects[obj2_id]

                spatial_relation = self.compute_spatial_relation(obj1, obj2)

                if spatial_relation and spatial_relation['confidence'] > self.relation_confidence_threshold:
                    self.add_or_update_relation(obj1_id, obj2_id, spatial_relation)

    def compute_spatial_relation(self, obj1, obj2):
        pos1 = obj1['position_2d']
        pos2 = obj2['position_2d']

        dx = pos2['x'] - pos1['x']
        dy = pos2['y'] - pos1['y']
        distance = np.sqrt(dx**2 + dy**2)

        if distance > 500:
            return None

        relations = []

        if abs(dx) > abs(dy):
            if dx > 50:
                relations.append(('right_of', 0.8))
            elif dx < -50:
                relations.append(('left_of', 0.8))
        else:
            if dy > 50:
                relations.append(('below', 0.8))
            elif dy < -50:
                relations.append(('above', 0.8))

        if distance < 100:
            relations.append(('near', 0.9))
        elif distance > 300:
            relations.append(('far', 0.7))

        if relations:
            best_relation = max(relations, key=lambda x: x[1])
            return {
                'type': 'spatial',
                'relation': best_relation[0],
                'confidence': best_relation[1],
                'distance': distance
            }

        return None

    def update_semantic_relations(self):
        object_ids = list(self.detected_objects.keys())

        for i, obj1_id in enumerate(object_ids):
            for j, obj2_id in enumerate(object_ids[i+1:], i+1):
                obj1 = self.detected_objects[obj1_id]
                obj2 = self.detected_objects[obj2_id]

                semantic_relation = self.compute_semantic_relation(obj1, obj2)

                if semantic_relation and semantic_relation['confidence'] > self.relation_confidence_threshold:
                    self.add_or_update_relation(obj1_id, obj2_id, semantic_relation)

    def compute_semantic_relation(self, obj1, obj2):
        class1 = obj1['class_name']
        class2 = obj2['class_name']
        attrs1 = obj1['attributes']
        attrs2 = obj2['attributes']

        relations = []

        support_pairs = [
            ('table', 'laptop'), ('table', 'book'), ('desk', 'laptop'),
            ('chair', 'person'), ('bed', 'person'), ('sofa', 'person')
        ]

        for supporter, supported in support_pairs:
            if (class1 == supporter and class2 == supported):
                relations.append(('supports', 0.9))
            elif (class1 == supported and class2 == supporter):
                relations.append(('supported_by', 0.9))

        container_pairs = [
            ('room', 'furniture'), ('building', 'room'), ('bag', 'item')
        ]

        for container, contained in container_pairs:
            if (class1 == container and class2 == contained):
                relations.append(('contains', 0.8))
            elif (class1 == contained and class2 == container):
                relations.append(('contained_in', 0.8))

        if 'furniture' in attrs1 and 'furniture' in attrs2:
            relations.append(('similar_to', 0.6))

        if 'electronic' in attrs1 and 'electronic' in attrs2:
            relations.append(('similar_to', 0.6))

        if relations:
            best_relation = max(relations, key=lambda x: x[1])
            return {
                'type': 'semantic',
                'relation': best_relation[0],
                'confidence': best_relation[1]
            }

        return None

    def update_temporal_relations(self):
        object_ids = list(self.detected_objects.keys())

        for obj_id in object_ids:
            if obj_id in self.object_history and len(self.object_history[obj_id]) > 1:
                history = list(self.object_history[obj_id])

                for other_obj_id in object_ids:
                    if obj_id != other_obj_id and other_obj_id in self.object_history:
                        temporal_relation = self.compute_temporal_relation(obj_id, other_obj_id)

                        if temporal_relation and temporal_relation['confidence'] > self.relation_confidence_threshold:
                            self.add_or_update_relation(obj_id, other_obj_id, temporal_relation)

    def compute_temporal_relation(self, obj1_id, obj2_id):
        history1 = list(self.object_history[obj1_id])
        history2 = list(self.object_history[obj2_id])

        if not history1 or not history2:
            return None

        first_seen1 = history1[0]['last_seen']
        first_seen2 = history2[0]['last_seen']

        time_diff = (first_seen2 - first_seen1).to_sec()

        if abs(time_diff) < 2.0:
            return {
                'type': 'temporal',
                'relation': 'co_occurs',
                'confidence': 0.8,
                'time_difference': time_diff
            }
        elif time_diff > 2.0:
            return {
                'type': 'temporal',
                'relation': 'appears_before',
                'confidence': 0.7,
                'time_difference': time_diff
            }
        elif time_diff < -2.0:
            return {
                'type': 'temporal',
                'relation': 'appears_after',
                'confidence': 0.7,
                'time_difference': time_diff
            }

        return None

    def add_or_update_relation(self, obj1_id, obj2_id, relation_data):
        if self.scene_graph.has_edge(obj1_id, obj2_id):
            existing_data = self.scene_graph.edges[obj1_id, obj2_id]
            if relation_data['confidence'] > existing_data.get('confidence', 0):
                self.scene_graph.edges[obj1_id, obj2_id].update(relation_data)
        else:
            self.scene_graph.add_edge(obj1_id, obj2_id, **relation_data)

    def publish_scene_graph(self):
        try:
            msg = SceneGraph()
            msg.header.stamp = rospy.Time.now()
            msg.header.frame_id = "map"

            for node_id, node_data in self.scene_graph.nodes(data=True):
                obj_msg = SemanticObject()
                obj_msg.header = msg.header
                obj_msg.class_name = node_data.get('class_name', 'unknown')
                obj_msg.confidence = node_data.get('confidence', 0.0)

                if 'position_3d' in node_data:
                    obj_msg.position = node_data['position_3d']

                msg.objects.append(obj_msg)

            for edge in self.scene_graph.edges(data=True):
                obj1_id, obj2_id, edge_data = edge

                relation_msg = ObjectRelation()
                relation_msg.subject_id = obj1_id
                relation_msg.object_id = obj2_id
                relation_msg.relation_type = edge_data.get('relation', 'unknown')
                relation_msg.confidence = edge_data.get('confidence', 0.0)
                relation_msg.relation_category = edge_data.get('type', 'unknown')

                msg.relations.append(relation_msg)

            msg.num_objects = len(msg.objects)
            msg.num_relations = len(msg.relations)

            self.scene_graph_pub.publish(msg)

        except Exception as e:
            pass

    def publish_graph_visualization(self):
        try:
            marker_array = MarkerArray()
            marker_id = 0

            for node_id, node_data in self.scene_graph.nodes(data=True):
                marker = Marker()
                marker.header.frame_id = "map"
                marker.header.stamp = rospy.Time.now()
                marker.ns = "scene_graph_nodes"
                marker.id = marker_id
                marker.type = Marker.SPHERE
                marker.action = Marker.ADD

                if 'position_3d' in node_data:
                    marker.pose.position = node_data['position_3d']
                else:
                    marker.pose.position.x = node_data['position_2d']['x'] / 100.0
                    marker.pose.position.y = node_data['position_2d']['y'] / 100.0
                    marker.pose.position.z = 1.0

                marker.pose.orientation.w = 1.0

                marker.scale.x = 0.3
                marker.scale.y = 0.3
                marker.scale.z = 0.3

                marker.color.r = 0.0
                marker.color.g = 1.0
                marker.color.b = 0.0
                marker.color.a = 0.8

                marker.lifetime = rospy.Duration(5.0)

                marker_array.markers.append(marker)
                marker_id += 1

                text_marker = Marker()
                text_marker.header = marker.header
                text_marker.ns = "scene_graph_labels"
                text_marker.id = marker_id
                text_marker.type = Marker.TEXT_VIEW_FACING
                text_marker.action = Marker.ADD
                text_marker.pose = marker.pose
                text_marker.pose.position.z += 0.5

                text_marker.scale.z = 0.2
                text_marker.color.r = 1.0
                text_marker.color.g = 1.0
                text_marker.color.b = 1.0
                text_marker.color.a = 1.0

                text_marker.text = node_data.get('class_name', 'unknown')
                text_marker.lifetime = rospy.Duration(5.0)

                marker_array.markers.append(text_marker)
                marker_id += 1

            self.graph_visualization_pub.publish(marker_array)

        except Exception as e:
            pass

    def publish_graph_json(self):
        try:
            graph_data = {
                'nodes': [],
                'edges': []
            }

            for node_id, node_data in self.scene_graph.nodes(data=True):
                node_info = {
                    'id': node_id,
                    'class_name': node_data.get('class_name', 'unknown'),
                    'confidence': node_data.get('confidence', 0.0),
                    'attributes': node_data.get('attributes', [])
                }

                if 'position_3d' in node_data:
                    pos = node_data['position_3d']
                    node_info['position'] = {'x': pos.x, 'y': pos.y, 'z': pos.z}

                graph_data['nodes'].append(node_info)

            for edge in self.scene_graph.edges(data=True):
                obj1_id, obj2_id, edge_data = edge

                edge_info = {
                    'source': obj1_id,
                    'target': obj2_id,
                    'relation': edge_data.get('relation', 'unknown'),
                    'type': edge_data.get('type', 'unknown'),
                    'confidence': edge_data.get('confidence', 0.0)
                }

                graph_data['edges'].append(edge_info)

            json_msg = String()
            json_msg.data = json.dumps(graph_data, indent=2)

            self.graph_json_pub.publish(json_msg)

        except Exception as e:
            pass

if __name__ == '__main__':
    try:
        generator = SceneGraphGenerator()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
```

## 7. 语义消息定义文件 (SemanticSegmentation.msg)

```
Header header
uint32 height
uint32 width
uint32[] class_ids
float32[] confidence
string[] class_names
uint32 num_classes
float32 mean_confidence
sensor_msgs/CameraInfo camera_info
```

## 8. 语义对象消息定义文件 (SemanticObject.msg)

```
Header header
string class_name
float32 confidence
geometry_msgs/Point position
BoundingBox bbox
string[] attributes
uint32 instance_id
```

## 9. 边界框消息定义文件 (BoundingBox.msg)

```
int32 x
int32 y
int32 width
int32 height
```

## 10. 跟踪对象消息定义文件 (TrackedObject.msg)

```
Header header
uint32 id
string class_name
float32 confidence
BoundingBox bbox
geometry_msgs/Point center
geometry_msgs/Vector3 velocity
geometry_msgs/Point position
uint32 age
uint32 hits
uint32 time_since_update
```

## 11. 火灾警报消息定义文件 (FireAlert.msg)

```
Header header
string alert_level
uint32 num_detections
float32 max_confidence
string[] detection_types
geometry_msgs/Point[] fire_locations
string emergency_response
```

## 12. 场景图谱消息定义文件 (SceneGraph.msg)

```
Header header
SemanticObject[] objects
ObjectRelation[] relations
uint32 num_objects
uint32 num_relations
```

## 13. 对象关系消息定义文件 (ObjectRelation.msg)

```
string subject_id
string object_id
string relation_type
string relation_category
float32 confidence
float32 distance
```

## 14. 语义感知包配置文件 (package.xml)

```xml
<?xml version="1.0"?>
<package format="2">
  <name>semantic_perception</name>
  <version>1.0.0</version>
  <description>几何-语义融合建图系统语义感知包</description>

  <maintainer email="<EMAIL>">开发团队</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>

  <depend>roscpp</depend>
  <depend>rospy</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>visualization_msgs</depend>
  <depend>cv_bridge</depend>
  <depend>image_transport</depend>
  <depend>image_geometry</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>pcl_ros</depend>
  <depend>pcl_conversions</depend>
  <depend>darknet_ros_msgs</depend>

  <build_depend>message_generation</build_depend>
  <exec_depend>message_runtime</exec_depend>

  <export>
  </export>
</package>
```

## 15. 语义感知CMakeLists.txt配置文件

```cmake
cmake_minimum_required(VERSION 3.0.2)
project(semantic_perception)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  sensor_msgs
  geometry_msgs
  visualization_msgs
  cv_bridge
  image_transport
  image_geometry
  tf2
  tf2_ros
  tf2_geometry_msgs
  pcl_ros
  pcl_conversions
  message_generation
)

find_package(OpenCV REQUIRED)
find_package(PCL REQUIRED)

add_message_files(
  FILES
  SemanticSegmentation.msg
  SemanticObject.msg
  BoundingBox.msg
  TrackedObject.msg
  FireAlert.msg
  SceneGraph.msg
  ObjectRelation.msg
)

generate_messages(
  DEPENDENCIES
  std_msgs
  sensor_msgs
  geometry_msgs
)

catkin_package(
  CATKIN_DEPENDS
    roscpp rospy std_msgs sensor_msgs geometry_msgs visualization_msgs
    cv_bridge image_transport image_geometry tf2 tf2_ros tf2_geometry_msgs
    pcl_ros pcl_conversions message_runtime
  DEPENDS OpenCV PCL
)

include_directories(
  ${catkin_INCLUDE_DIRS}
  ${OpenCV_INCLUDE_DIRS}
  ${PCL_INCLUDE_DIRS}
)

catkin_install_python(PROGRAMS
  scripts/semantic_segmentation_node.py
  scripts/geometry_semantic_fusion_node.py
  scripts/semantic_map_integration_node.py
  scripts/object_detection_tracking.py
  scripts/fire_detection_node.py
  scripts/scene_graph_generator.py
  DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

install(DIRECTORY launch/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
  FILES_MATCHING PATTERN "*.launch"
)

install(DIRECTORY config/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/config
  FILES_MATCHING PATTERN "*.yaml" PATTERN "*.rviz"
)

install(DIRECTORY models/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/models
  FILES_MATCHING PATTERN "*.pth" PATTERN "*.pt"
)
```

## 16. 语义感知Launch文件 (semantic_perception.launch)

```xml
<?xml version="1.0"?>
<launch>
  <arg name="enable_semantic_segmentation" default="true"/>
  <arg name="enable_object_detection" default="true"/>
  <arg name="enable_fire_detection" default="true"/>
  <arg name="enable_scene_graph" default="true"/>
  <arg name="enable_geometry_fusion" default="true"/>
  <arg name="enable_map_integration" default="true"/>

  <arg name="semantic_model_path" default="$(find semantic_perception)/models/deeplabv3_resnet50.pth"/>
  <arg name="detection_model_path" default="$(find semantic_perception)/models/yolov5s.pt"/>
  <arg name="fire_model_path" default="$(find semantic_perception)/models/fire_detection.pth"/>

  <arg name="rgb_topic" default="/camera/color/image_raw"/>
  <arg name="depth_topic" default="/camera/aligned_depth_to_color/image_raw"/>
  <arg name="camera_info_topic" default="/camera/color/camera_info"/>
  <arg name="pointcloud_topic" default="/tsdf_mapping/pointcloud"/>

  <group if="$(arg enable_semantic_segmentation)">
    <node name="semantic_segmentation_node" pkg="semantic_perception" type="semantic_segmentation_node.py" output="screen">
      <param name="model_name" value="deeplabv3_resnet50"/>
      <param name="model_path" value="$(arg semantic_model_path)"/>
      <param name="num_classes" value="21"/>
      <param name="input_size" value="[512, 512]"/>
      <param name="confidence_threshold" value="0.5"/>
      <remap from="image_raw" to="$(arg rgb_topic)"/>
      <remap from="camera_info" to="$(arg camera_info_topic)"/>
    </node>
  </group>

  <group if="$(arg enable_object_detection)">
    <node name="object_detection_tracking" pkg="semantic_perception" type="object_detection_tracking.py" output="screen">
      <param name="detection_model_path" value="$(arg detection_model_path)"/>
      <param name="tracking_enabled" value="true"/>
      <param name="confidence_threshold" value="0.5"/>
      <param name="nms_threshold" value="0.4"/>
      <param name="max_track_age" value="30"/>
      <param name="min_track_hits" value="3"/>
      <remap from="image_raw" to="$(arg rgb_topic)"/>
      <remap from="camera_info" to="$(arg camera_info_topic)"/>
    </node>
  </group>

  <group if="$(arg enable_fire_detection)">
    <node name="fire_detection_node" pkg="semantic_perception" type="fire_detection_node.py" output="screen">
      <param name="model_path" value="$(arg fire_model_path)"/>
      <param name="confidence_threshold" value="0.7"/>
      <param name="enable_color_detection" value="true"/>
      <param name="enable_motion_detection" value="true"/>
      <param name="enable_cnn_detection" value="true"/>
      <param name="alert_cooldown" value="5.0"/>
      <remap from="image_raw" to="$(arg rgb_topic)"/>
      <remap from="camera_info" to="$(arg camera_info_topic)"/>
    </node>
  </group>

  <group if="$(arg enable_geometry_fusion)">
    <node name="geometry_semantic_fusion" pkg="semantic_perception" type="geometry_semantic_fusion_node.py" output="screen">
      <param name="enable_temporal_smoothing" value="true"/>
      <param name="geometric_constraint_weight" value="0.3"/>
      <param name="temporal_smoothing_weight" value="0.2"/>
      <param name="confidence_boost_factor" value="1.2"/>
      <remap from="/tsdf_mapping/pointcloud" to="$(arg pointcloud_topic)"/>
      <remap from="/semantic_perception/segmentation" to="/semantic_segmentation_node/semantic_segmentation"/>
      <remap from="/stereo_camera/left/camera_info" to="$(arg camera_info_topic)"/>
    </node>
  </group>

  <group if="$(arg enable_map_integration)">
    <node name="semantic_map_integration" pkg="semantic_perception" type="semantic_map_integration_node.py" output="screen">
      <param name="map_resolution" value="0.05"/>
      <param name="map_size_x" value="20.0"/>
      <param name="map_size_y" value="20.0"/>
      <param name="map_size_z" value="5.0"/>
      <param name="enable_semantic_mapping" value="true"/>
      <param name="enable_object_tracking" value="true"/>
      <param name="semantic_confidence_threshold" value="0.6"/>
      <remap from="semantic_segmentation" to="/geometry_semantic_fusion/fused_semantic_segmentation"/>
      <remap from="tracked_objects" to="/object_detection_tracking/tracked_objects"/>
      <remap from="fire_detections" to="/fire_detection_node/fire_detections"/>
    </node>
  </group>

  <group if="$(arg enable_scene_graph)">
    <node name="scene_graph_generator" pkg="semantic_perception" type="scene_graph_generator.py" output="screen">
      <param name="enable_spatial_relations" value="true"/>
      <param name="enable_semantic_relations" value="true"/>
      <param name="enable_temporal_relations" value="true"/>
      <param name="relation_confidence_threshold" value="0.5"/>
      <param name="max_relation_distance" value="5.0"/>
      <remap from="semantic_segmentation" to="/geometry_semantic_fusion/fused_semantic_segmentation"/>
      <remap from="tracked_objects" to="/object_detection_tracking/tracked_objects"/>
      <remap from="semantic_pointcloud" to="/semantic_map_integration/semantic_map"/>
    </node>
  </group>

</launch>
```

## 17. 语义感知配置文件 (semantic_config.yaml)

```yaml
semantic_segmentation:
  model:
    name: "deeplabv3_resnet50"
    path: "models/deeplabv3_resnet50.pth"
    num_classes: 21
    input_size: [512, 512]

  preprocessing:
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
    normalize: true

  postprocessing:
    confidence_threshold: 0.5
    apply_crf: false

  class_names:
    - "background"
    - "aeroplane"
    - "bicycle"
    - "bird"
    - "boat"
    - "bottle"
    - "bus"
    - "car"
    - "cat"
    - "chair"
    - "cow"
    - "diningtable"
    - "dog"
    - "horse"
    - "motorbike"
    - "person"
    - "pottedplant"
    - "sheep"
    - "sofa"
    - "train"
    - "tvmonitor"

object_detection:
  model:
    path: "models/yolov5s.pt"
    confidence_threshold: 0.5
    nms_threshold: 0.4

  tracking:
    enabled: true
    max_track_age: 30
    min_track_hits: 3
    iou_threshold: 0.3

fire_detection:
  model:
    path: "models/fire_detection.pth"
    confidence_threshold: 0.7

  detection_methods:
    color_detection: true
    motion_detection: true
    cnn_detection: true

  alert:
    cooldown: 5.0
    high_confidence_threshold: 0.9

geometry_fusion:
  temporal_smoothing:
    enabled: true
    history_frames: 5
    weight_decay: 0.8

  geometric_constraints:
    weight: 0.3
    surface_normal_threshold: 0.8
    depth_discontinuity_threshold: 0.1

  confidence_boost:
    factor: 1.2
    curvature_threshold: 0.1

semantic_mapping:
  map:
    resolution: 0.05
    size_x: 20.0
    size_y: 20.0
    size_z: 5.0

  confidence_threshold: 0.6
  object_tracking: true
  fire_detection: true

scene_graph:
  relations:
    spatial: true
    semantic: true
    temporal: true

  thresholds:
    relation_confidence: 0.5
    max_distance: 5.0

  spatial_relations:
    - "left_of"
    - "right_of"
    - "above"
    - "below"
    - "in_front_of"
    - "behind"
    - "near"
    - "far"

  semantic_relations:
    - "supports"
    - "contains"
    - "part_of"
    - "attached_to"
    - "similar_to"

  temporal_relations:
    - "appears_before"
    - "appears_after"
    - "co_occurs"
```
